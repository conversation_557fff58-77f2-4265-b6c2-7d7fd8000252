package ru.sbertroika.tms.model

import com.fasterxml.jackson.annotation.JsonFormat
import ru.sbertroika.common.tms.EventType
import java.sql.Timestamp
import java.time.ZonedDateTime

data class TerminalEvent(
    val type: Int,
    val createdAt: Timestamp,
    val attributes: Map<String, String>
)

data class Ticket(
    val ticketSeries: String,
    val ticketNumber: String,
    val createdAt: Timestamp,
    val manifest: String,
    val manifestVersion: Long,
    val shiftNumber: Long,
    val serviceId: String,
    val tariffId: String,
    val productId: String,
    val amount: Long,
    val stationFromId: String?,
    val stationToId: String?
)

data class Batch(
    val terminalSerial: String,
    val events: List<TerminalEvent>,
    val tickets: List<Ticket>,
    val timeZone: String,
    val timeZoneName: String
)

data class TerminalJournalEvent(
    val projectId: String,

    val eventType: EventType,

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
    val createdAt: ZonedDateTime,

    val terminalSerial: String,

    val ern: Int,

    val userId: String? = null,

    val shiftNum: Int? = null,

    val stopListVersion: String? = null,

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
    val stopListUpdate: ZonedDateTime? = null,

    val errorCode: Int? = null,

    val errorMessage: String? = null,

    val value: String? = null
)

data class FailEvent(
    val batchId: String,
    val terminalSerial: String,
    val event: TerminalEvent,
    val exception: Error
)

data class FailBatch(
    val batchId: String,
    val batch: String,
    val exception: Error
)