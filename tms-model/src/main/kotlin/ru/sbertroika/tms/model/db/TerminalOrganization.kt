package ru.sbertroika.tms.model.db

import org.springframework.data.relational.core.mapping.Column
import java.io.Serializable
import java.sql.Timestamp
import java.util.*


data class TerminalOrganizationPK(
    val tsId: UUID? = null,
    val tsVersion:Int? = null,
): Serializable

data class TerminalOrganization(

    @Column("to_id")
    var toId: UUID? = null,

    @Column("to_version")
    var toVersion: Int? = null,

    @Column("to_version_created_at")
    var toVersionCreatedAt: Timestamp? = null,

    @Column("to_version_created_by")
    var toVersionCreatedBy: UUID? = null,

    @Column("to_project_id")
    var toProjectId: UUID? = null,

    @Column("to_terminal_id")
    var toTerminalId: UUID? = null,

    @Column("to_terminal_version")
    var toTerminalVersion: Int? = null,

    @Column("to_organization_id")
    var toOrganizationId: UUID? = null,

    @Column("to_active_from")
    var toActiveFrom: Timestamp? = null,

    @Column("to_active_till")
    var toActiveTill: Timestamp? = null,
)
