# TMS Gate Integration Tests

Модуль интеграционных тестов для TMS Gate сервиса.

## Описание

Этот модуль содержит интеграционные тесты, которые проверяют полный цикл работы с TMS Gate сервисом:

1. **Регистрация терминала** - отправка CSR на PKI сервер для получения сертификата
2. **Heartbeat с mTLS** - отправка heartbeat запроса с использованием полученного сертификата
3. **Дополнительные тесты** - проверка других методов API с mTLS авторизацией

## Структура

```
tms-gate-it/
├── build.gradle.kts                    # Конфигурация сборки
├── README.md                           # Документация
└── src/
    ├── main/kotlin/
    │   └── ru/sbertroika/tms/gate/it/util/
    │       ├── CertificateUtils.kt     # Утилиты для работы с сертификатами
    │       └── GrpcClientUtils.kt      # Утилиты для работы с gRPC клиентами
    └── test/kotlin/
        └── ru/sbertroika/tms/gate/it/
            ├── TMSGateIntegrationTest.kt    # Основной класс интеграционных тестов
            └── CertificateUtilsTest.kt      # Тесты утилит для сертификатов
    └── test/resources/
        └── application-test.yml        # Конфигурация для тестов
```

## Логика тестов

### Test1: Регистрация терминала
1. Генерируется приватный ключ (RSA, 2048 бит)
2. Создается CSR (Certificate Signing Request) с CommonName = серийный номер терминала
3. **CSR конвертируется в base64 формат** для передачи в запросе
4. Отправляется запрос `registerTerminal` на `pki-tms.sbertroika.tech:5000` с CSR в base64
5. Получается сертификат из ответа и сохраняется в переменную

### Test2: Heartbeat с mTLS
1. Создается gRPC клиент с mTLS авторизацией (используется приватный ключ и полученный сертификат)
2. Отправляется запрос `heartbeat` на `tms.sbertroika.tech:5000`
3. Проверяется корректность ответа и время сервера

### Test3: Дополнительные методы
1. Тестируются другие методы API с mTLS авторизацией (например, `terminalUserList`)

## Запуск тестов

```bash
# Компиляция модуля
./gradlew :tms-gate-it:compileKotlin

# Компиляция тестов
./gradlew :tms-gate-it:compileTestKotlin

# Запуск всех интеграционных тестов
./gradlew :tms-gate-it:test

# Запуск конкретного теста
./gradlew :tms-gate-it:test --tests TMSGateIntegrationTest.testRegisterTerminal

# Запуск тестов утилит
./gradlew :tms-gate-it:test --tests CertificateUtilsTest

# Запуск с подробным логированием
./gradlew :tms-gate-it:test --info
```

## Конфигурация

Настройки тестов находятся в `src/test/resources/application-test.yml`:

```yaml
test:
  pki:
    server: pki-tms.sbertroika.tech
    port: 5000
  tms:
    server: tms.sbertroika.tech
    port: 5000
  terminal:
    serial-number: "1003465905019620"
```

## Зависимости

- **BouncyCastle** - для работы с сертификатами и криптографией
- **gRPC** - для взаимодействия с сервисами
- **JUnit 5** - для написания тестов
- **Kotlin Coroutines** - для асинхронных операций

## Требования

- Java 17+
- Доступ к серверам `pki-tms.sbertroika.tech` и `tms.sbertroika.tech`
- Сетевой доступ к порту 5000 на указанных серверах

## Особенности реализации

### Генерация ключей и CSR
- Используется RSA алгоритм с длиной ключа 2048 бит
- CSR создается с использованием BouncyCastle библиотеки
- Подпись выполняется алгоритмом SHA256withRSA
- **CSR передается в base64 формате** в запросе регистрации

### Форматы CSR
Модуль поддерживает два формата CSR:
- **PEM формат** - для отладки и логирования
- **Base64 формат** - для передачи в gRPC запросах

```kotlin
// Генерация CSR в PEM формате
val csrPem = CertificateUtils.generateCSR(keyPair, "CN=terminal123")

// Генерация CSR в Base64 формате
val csrBase64 = CertificateUtils.generateCSRBase64(keyPair, "CN=terminal123")
```

### mTLS авторизация
- Клиентский сертификат передается в gRPC клиент
- Используется InsecureTrustManager для тестов (доверяет всем сертификатам)
- Поддерживается полная mTLS аутентификация

### Порядок выполнения тестов
- Тесты выполняются в определенном порядке с помощью аннотации `@Order`
- Переменные (ключ, сертификат) сохраняются между тестами
- Используется `@TestInstance(Lifecycle.PER_CLASS)` для сохранения состояния

## Статус

✅ **Модуль создан и настроен**
✅ **Код компилируется без ошибок**
✅ **Базовые тесты утилит проходят**
✅ **Поддержка CSR в base64 формате**
⚠️ **Интеграционные тесты требуют доступ к серверам**

Модуль готов к использованию. Для полного тестирования необходимо иметь доступ к серверам `pki-tms.sbertroika.tech` и `tms.sbertroika.tech`. 