import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

plugins {
    idea
    kotlin("jvm")
    kotlin("plugin.spring") version libs.versions.kotlin.get() apply false
    id("com.google.protobuf") version libs.versions.protobufPlugin.get()
    id("org.springframework.boot") version "3.5.4"
    id("io.spring.dependency-management") version "1.1.0"
}

group = "ru.sbertroika.tms"
version = rootProject.version

java.sourceCompatibility = JavaVersion.VERSION_17

configurations {
    compileOnly {
        extendsFrom(configurations.annotationProcessor.get())
    }
}

repositories {
    maven {
        url = uri("https://nexus.sbertroika.tech/repository/maven-public/")
        credentials {
            username = providers.gradleProperty("mavenUser").get()
            password = providers.gradleProperty("mavenPassword").get()
        }
    }
    maven {
        url = uri("https://nexus.sbertroika.tech/repository/maven-releases/")
        credentials {
            username = providers.gradleProperty("mavenUser").get()
            password = providers.gradleProperty("mavenPassword").get()
        }
    }
    maven {
        url = uri("https://nexus.sbertroika.tech/repository/maven-snapshots/")
        credentials {
            username = providers.gradleProperty("mavenUser").get()
            password = providers.gradleProperty("mavenPassword").get()
        }
    }
}

dependencies {
    implementation(project(":tms-api"))
    implementation(project(":tms-common"))
    implementation(project(":tms-model"))
    implementation("ru.sbertroika.common:certs:1.0.6")
    implementation("ru.sbertroika.common:common-api:1.0.6")

    implementation("io.github.lognet:grpc-spring-boot-starter:5.2.0") {
        exclude(group = "io.grpc", module = "grpc-netty-shaded")
    }

    implementation("org.springframework.boot:spring-boot-starter-test")
    implementation("org.springframework.kafka:spring-kafka-test:3.3.8")

    //Logging
    implementation("org.springframework.boot:spring-boot-starter-logging")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-slf4j:1.8.1")

    //Kotlin
    implementation("io.projectreactor.kotlin:reactor-kotlin-extensions")
    implementation(libs.kotlin.reflect)
    implementation(libs.kotlin.stdlib)

    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-slf4j:1.8.1")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-reactor")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core")
    implementation("com.fasterxml.jackson.module:jackson-module-kotlin:2.14.0")

    //gRPC
    implementation("io.grpc:grpc-kotlin-stub:1.4.3")
    implementation("io.grpc:grpc-netty:1.71.0")
    implementation("io.grpc:grpc-protobuf:1.71.0")
    implementation("io.grpc:grpc-services:1.71.0")
    implementation("com.google.protobuf:protobuf-kotlin:3.21.7")

    //BouncyCastle для работы с сертификатами
    implementation("org.bouncycastle:bcprov-ext-debug-jdk14:1.73")
    implementation("org.bouncycastle:bcpkix-jdk14:1.73")

    //Test dependencies
    testImplementation("org.junit.jupiter:junit-jupiter:5.12.2")
    testImplementation("org.mockito:mockito-junit-jupiter:5.17.0")
    testImplementation("org.assertj:assertj-core:3.27.3")
    testImplementation("org.awaitility:awaitility:4.2.2")
}

tasks.withType<KotlinCompile> {
    kotlinOptions {
        freeCompilerArgs += "-Xjsr305=strict"
        jvmTarget = "17"
    }
}

tasks.withType<Test> {
    useJUnitPlatform()
    systemProperty("junit.jupiter.execution.parallel.enabled", "false")
    systemProperty("junit.jupiter.execution.parallel.mode.default", "same_thread")
} 