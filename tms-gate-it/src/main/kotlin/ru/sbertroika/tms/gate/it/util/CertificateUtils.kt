package ru.sbertroika.tms.gate.it.util

import org.bouncycastle.asn1.x500.X500Name
import org.bouncycastle.openssl.jcajce.JcaPEMWriter
import org.bouncycastle.operator.jcajce.JcaContentSignerBuilder
import org.bouncycastle.pkcs.PKCS10CertificationRequest
import org.bouncycastle.pkcs.jcajce.JcaPKCS10CertificationRequestBuilder
import java.io.StringWriter
import java.security.*
import java.util.*
import java.security.cert.X509Certificate
import java.security.cert.CertificateFactory

object CertificateUtils {

    fun generateKeyPair(): KeyPair {
        val keyPairGenerator = KeyPairGenerator.getInstance("RSA")
        keyPairGenerator.initialize(2048, SecureRandom())
        return keyPairGenerator.generateKeyPair()
    }

    fun generateCSR(keyPair: KeyPair, commonName: String): String {
        val csrBuilder = JcaPKCS10CertificationRequestBuilder(
            X500Name("CN=$commonName"),
            keyPair.public
        )
        val csr = csrBuilder.build(JcaContentSignerBuilder("SHA256withRSA")
            .setProvider("BC")
            .build(keyPair.private))
        return encodeToPEM(csr)
    }

    fun generateCSRBase64(keyPair: KeyPair, commonName: String): String {
        val csrBuilder = JcaPKCS10CertificationRequestBuilder(
            X500Name("CN=$commonName"),
            keyPair.public
        )
        val csr = csrBuilder.build(JcaContentSignerBuilder("SHA256withRSA")
            .setProvider("BC")
            .build(keyPair.private))
        // Encode the entire PEM block (including headers) to Base64
        return Base64.getEncoder().encodeToString(encodeToPEM(csr).toByteArray())
    }

    fun parseCertificate(certificatePEM: String): X509Certificate {
        val certBytes = decodePEM(certificatePEM, "CERTIFICATE")
        val certFactory = CertificateFactory.getInstance("X.509")
        return certFactory.generateCertificate(certBytes.inputStream()) as X509Certificate
    }

    fun parseCertificateFromBase64(certificateBase64: String): X509Certificate {
        // Очищаем base64 от переносов строк и пробелов
        val cleanBase64 = certificateBase64.replace(Regex("[\\s\\n\\r]"), "")
        // Декодируем base64 в байты
        val decodedBytes = Base64.getDecoder().decode(cleanBase64)
        // Создаем сертификат напрямую из байтов
        val certFactory = CertificateFactory.getInstance("X.509")
        return certFactory.generateCertificate(decodedBytes.inputStream()) as X509Certificate
    }

    private fun encodeToPEM(csr: PKCS10CertificationRequest): String {
        val stringWriter = StringWriter()
        val pemWriter = JcaPEMWriter(stringWriter)
        pemWriter.writeObject(csr)
        pemWriter.close()
        return stringWriter.toString()
    }

    private fun decodePEM(pemString: String, type: String): ByteArray {
        val lines = pemString.lines()
        val startIndex = lines.indexOfFirst { it.contains("-----BEGIN $type-----") }
        val endIndex = lines.indexOfFirst { it.contains("-----END $type-----") }
        if (startIndex == -1 || endIndex == -1) {
            throw IllegalArgumentException("Invalid PEM format")
        }
        val base64Data = lines.subList(startIndex + 1, endIndex).joinToString("")
        return Base64.getDecoder().decode(base64Data)
    }
} 