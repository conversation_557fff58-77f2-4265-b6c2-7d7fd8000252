package ru.sbertroika.tms.gate.it.util

import io.grpc.*
import io.grpc.netty.GrpcSslContexts
import io.grpc.netty.NettyChannelBuilder
import io.netty.handler.ssl.util.InsecureTrustManagerFactory
import ru.sbertroika.tms.gate.v1.TMSGateServiceGrpcKt
import java.security.cert.X509Certificate

object GrpcClientUtils {

    fun createInsecureClient(server: String, port: Int, isTls: Boolean = true): TMSGateServiceGrpcKt.TMSGateServiceCoroutineStub {
        val channel = if (isTls) {
            val credentials: ChannelCredentials = TlsChannelCredentials.newBuilder() //You can use your own certificate here .trustManager(new File("cert.pem"))
                .trustManager(InsecureTrustManagerFactory.INSTANCE.trustManagers[0])
                .build()
            Grpc.newChannelBuilderForAddress(server, port, credentials)
                .build()
        } else {
            ManagedChannelBuilder.forTarget("$server:$port")
                .usePlaintext()
                .build()
        }
        return TMSGateServiceGrpcKt.TMSGateServiceCoroutineStub(channel)
    }

    fun createMtlsClient(
        server: String,
        port: Int,
        privateKey: java.security.PrivateKey,
        certificate: X509Certificate
    ): TMSGateServiceGrpcKt.TMSGateServiceCoroutineStub {

        // Создаем SSL контекст с mTLS
        val sslContext = GrpcSslContexts.forClient()
            .keyManager(privateKey, certificate)
            .trustManager(InsecureTrustManagerFactory.INSTANCE)
            .build()

        val channel = NettyChannelBuilder.forTarget("$server:$port")
            .sslContext(sslContext)
            .build()

        return TMSGateServiceGrpcKt.TMSGateServiceCoroutineStub(channel)
    }

    fun createMetadata(serialNumber: String): Metadata {
        val meta = Metadata()
        meta.put(Metadata.Key.of("tms.terminal.serial", Metadata.ASCII_STRING_MARSHALLER), serialNumber)
        return meta
    }
} 