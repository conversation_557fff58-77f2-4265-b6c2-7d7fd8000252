spring:
  application:
    name: tms-gate-it
  main:
    allow-bean-definition-overriding: true

logging:
  level:
    root: INFO
    ru.sbertroika.tms.gate.it: DEBUG
    io.grpc: DEBUG
    io.netty: DEBUG
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# Конфигурация для интеграционных тестов
test:
  pki:
    server: pki-tms.sbertroika.tech
    port: 5000
  tms:
    server: tms.sbertroika.tech
    port: 5000
  terminal:
    serial-number: "1003465905019620" 