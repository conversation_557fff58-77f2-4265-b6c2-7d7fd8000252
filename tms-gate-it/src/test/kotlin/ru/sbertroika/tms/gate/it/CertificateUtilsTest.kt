package ru.sbertroika.tms.gate.it

import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*
import ru.sbertroika.tms.gate.it.util.CertificateUtils
import java.security.KeyPair
import java.util.Base64

class CertificateUtilsTest {

    companion object {
        @BeforeAll
        @JvmStatic
        fun setup() {
            // Инициализируем BouncyCastle провайдер
            try {
                java.security.Security.addProvider(org.bouncycastle.jce.provider.BouncyCastleProvider())
            } catch (e: Exception) {
                // Провайдер уже добавлен
            }
        }
    }

    @Test
    fun testGenerateKeyPair() {
        val keyPair = CertificateUtils.generateKeyPair()
        
        assertNotNull(keyPair)
        assertNotNull(keyPair.private)
        assertNotNull(keyPair.public)
        assertEquals("RSA", keyPair.private.algorithm)
        assertEquals("RSA", keyPair.public.algorithm)
    }

    @Test
    fun testGenerateCSR() {
        val keyPair = CertificateUtils.generateKeyPair()
        val commonName = "test-terminal-123"
        val csr = CertificateUtils.generateCSR(keyPair, commonName)
        
        assertNotNull(csr)
        assertTrue(csr.contains("-----BEGIN CERTIFICATE REQUEST-----"))
        assertTrue(csr.contains("-----END CERTIFICATE REQUEST-----"))
        assertTrue(csr.contains("CN=$commonName"))
        
        println("Generated CSR (PEM):")
        println(csr)
    }

    @Test
    fun testGenerateCSRBase64() {
        val keyPair = CertificateUtils.generateKeyPair()
        val commonName = "test-terminal-456"
        val csrBase64 = CertificateUtils.generateCSRBase64(keyPair, commonName)
        
        assertNotNull(csrBase64)
        assertFalse(csrBase64.isEmpty())
        
        // Проверяем, что это валидный base64
        try {
            val decoded = Base64.getDecoder().decode(csrBase64)
            assertTrue(decoded.isNotEmpty())
            
            // Декодируем обратно в строку и проверяем, что это PEM
            val decodedString = String(decoded)
            assertTrue(decodedString.contains("-----BEGIN CERTIFICATE REQUEST-----"))
            assertTrue(decodedString.contains("-----END CERTIFICATE REQUEST-----"))
            assertTrue(decodedString.contains("CN=$commonName"))
            
        } catch (e: IllegalArgumentException) {
            fail("CSR should be valid base64")
        }
        
        println("Generated CSR (Base64):")
        println(csrBase64)
        
        // Проверяем, что base64 и PEM версии генерируют одинаковый CSR
        val csrPem = CertificateUtils.generateCSR(keyPair, commonName)
        val csrBase64FromPem = Base64.getEncoder().encodeToString(csrPem.toByteArray())
        
        assertEquals(csrBase64FromPem, csrBase64, "Base64 should encode the entire PEM block")
        
        // Проверяем декодирование
        val decodedPem = String(Base64.getDecoder().decode(csrBase64))
        assertEquals(csrPem, decodedPem, "Decoded base64 should match original PEM")
    }

    @Test
    fun testParseCertificate() {
        // Тестовый сертификат (самоподписанный)
        val testCertificatePEM = """
            -----BEGIN CERTIFICATE-----
            MIIDXTCCAkWgAwIBAgIJAKoK/OvJ8gQNMA0GCSqGSIb3DQEBCwUAMEUxCzAJBgNV
            BAYTAlVTMRMwEQYDVQQIDApTb21lLVN0YXRlMSEwHwYDVQQKDBhJbnRlcm5ldCBX
            aWRnaXRzIFB0eSBMdGQwHhcNMTkwMzI2MTIzNDU2WhcNMjAwMzI1MTIzNDU2WjBF
            MQswCQYDVQQGEwJVUzETMBEGA1UECAwKU29tZS1TdGF0ZTEhMB8GA1UECgwYSW50
            ZXJuZXQgV2lkZ2l0cyBQdHkgTHRkMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIB
            CgKCAQEA0vx7agoebGcQSuu3L7LydZWWi44HVs/tsdZxOtOpHp62vRUpvq9PYW3l
            sYfZ6wqTJGJ1rS3wQHLkW0baoILd/aL/PL2YrVqeuDrOtqh9XdvjF5iKJC+hmXbf
            Qs/6JAhFEy8UaHpiCL9We5/mFK2FQj9O+2ECsdnfQTIqVQZ4Ek5/9atELct5Zk9H
            zVAVbbKSm/RCD6X98FE8fovjJesxX9mjol5M9f0HHi/0GfxuL/Fk6pZq6c59sKca
            YzdkDq7VgBuRjBwZBrffPDiCyMKkXRohfPUx/7Jf1kCAwEAAaNQME4wHQYDVR0O
            BBYEFMsR6MrStBZYAck3LjMWFrlMlgofMB8GA1UdIwQYMBaAFMsR6MrStBZYAck3
            LjMWFrlMlgofMAwGA1UdEwQFMAMBAf8wDQYJKoZIhvcNAQELBQADggEBABltAan
            sDT8HZE1kCBTxIZM2JdfghwF456xyTca12TlpY/9SB2n9niP1UYsAnStVYFyJaA
            fAqPpxwya2jiERdcwOQEPL1J7Agjgoo1h03wXo4txWf5nk9qXxfc7K3Ovgwl0dg
            k42R95UlbTmaoRXjOK6+f87WqLPBVaYwNa6Qf+ZnJEmowtfcCuiyz5Vyj+kJDta
            fO9sF1i9af/7FKJWzOYcX48dwoM6cLIEmXR9Dq1YqNu0Suttup5oZARA26l6xEO
            6IWIDJqXm+JmGnxCo0oAAyinPrcIvdVF85NqgFcM+hR3JBMBx4CqNZrhfCEVSs
            dLAa6kqvtJwvbHpu
            -----END CERTIFICATE-----
        """.trimIndent()
        
        val certificate = CertificateUtils.parseCertificate(testCertificatePEM)
        
        assertNotNull(certificate)
        assertEquals("CN=Internet Widgits Pty Ltd", certificate.subjectX500Principal.name)
        assertEquals("CN=Internet Widgits Pty Ltd", certificate.issuerX500Principal.name)
    }

    @Test
    fun testParseCertificateFromBase64() {
        // Создаем простой тест с реальным сертификатом
        val keyPair = CertificateUtils.generateKeyPair()
        
        // Создаем простой самоподписанный сертификат используя BouncyCastle
        val certBuilder = org.bouncycastle.cert.jcajce.JcaX509v3CertificateBuilder(
            org.bouncycastle.asn1.x500.X500Name("CN=TestCertificate"),
            java.math.BigInteger.valueOf(System.currentTimeMillis()),
            java.util.Date(),
            java.util.Date(System.currentTimeMillis() + 365 * 24 * 60 * 60 * 1000L),
            org.bouncycastle.asn1.x500.X500Name("CN=TestCertificate"),
            keyPair.public
        )
        
        val certSigner = org.bouncycastle.operator.jcajce.JcaContentSignerBuilder("SHA256withRSA")
            .setProvider("BC")
            .build(keyPair.private)
        
        val certHolder = certBuilder.build(certSigner)
        val certConverter = org.bouncycastle.cert.jcajce.JcaX509CertificateConverter()
            .setProvider("BC")
        val certificate = certConverter.getCertificate(certHolder)
        
        // Кодируем сертификат в base64
        val certBytesArray = certificate.encoded
        val certBase64 = java.util.Base64.getEncoder().encodeToString(certBytesArray)
        
        // Тестируем парсинг из base64
        val parsedCertificate = CertificateUtils.parseCertificateFromBase64(certBase64)
        
        assertNotNull(parsedCertificate)
        assertEquals(certificate.subjectX500Principal, parsedCertificate.subjectX500Principal)
        assertEquals(certificate.issuerX500Principal, parsedCertificate.issuerX500Principal)
    }
} 