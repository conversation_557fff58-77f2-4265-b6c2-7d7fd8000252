package ru.sbertroika.tms.gate.it

import com.google.protobuf.Empty
import com.google.protobuf.Timestamp
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.*
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.TestInstance.Lifecycle
import ru.sbertroika.common.v1.Position
import ru.sbertroika.tms.gate.it.util.CertificateUtils
import ru.sbertroika.tms.gate.it.util.GrpcClientUtils
import ru.sbertroika.tms.gate.v1.*
import java.security.KeyPair
import java.security.cert.X509Certificate
import java.time.Instant
import java.time.ZoneId
import java.util.concurrent.atomic.AtomicInteger

@TestInstance(Lifecycle.PER_CLASS)
@TestMethodOrder(MethodOrderer.OrderAnnotation::class)
class TMSGateIntegrationTest {

    companion object {
        private const val PKI_SERVER = "pki-tms.sbertroika.tech"
        private const val TMS_SERVER = "tms.sbertroika.tech"
        private const val PORT = 5000
        private const val SERIAL_NUMBER = "1003465905019620"

        init {
            // Инициализируем BouncyCastle провайдер
            try {
                java.security.Security.addProvider(org.bouncycastle.jce.provider.BouncyCastleProvider())
            } catch (e: Exception) {
                // Провайдер уже добавлен
            }
        }
    }

    private lateinit var keyPair: KeyPair
    private lateinit var csr: String
    private lateinit var csrBase64: String
    private lateinit var certificate: X509Certificate
    private lateinit var manifestId: String
    private val manifestVersion = AtomicInteger(0)

    @BeforeAll
    fun setup() {
        // 1. Формируем приватный ключ и CSR
        keyPair = CertificateUtils.generateKeyPair()
        csr = CertificateUtils.generateCSR(keyPair, "CN=$SERIAL_NUMBER")
        csrBase64 = CertificateUtils.generateCSRBase64(keyPair, "CN=$SERIAL_NUMBER")
        
        println("Generated CSR (PEM):")
        println(csr)
        println("Generated CSR (Base64):")
        println(csrBase64)
    }

    @Test
    @Order(1)
    fun testRegisterTerminal(): Unit = runBlocking {
        // 2. Отправляем запрос registerTerminal с CSR в base64 на pki-tms.sbertroika.tech
        val client = GrpcClientUtils.createInsecureClient(PKI_SERVER, PORT, true)
        
        val request = registrationRequest {
            serialNumber = SERIAL_NUMBER
            this.csr = csrBase64  // Используем CSR в base64 формате
            wifiMac = "wifi"
            bluetoothMac = "bluet"
            ethernetMac = "ethernet"
            versionPO = "vers po"
        }
        
        val response = client.registration(request)
        println("Registration response: $response")
        
        // Проверяем, что нет ошибки
        assertFalse(response.hasError(), "Registration should not have error")
        
        // 3. Получаем сертификат из ответа (в base64 формате)
        val certificateBase64 = response.result.certificate
        Assertions.assertNotNull(certificateBase64, "Certificate should not be null")
        assertFalse(certificateBase64.isEmpty(), "Certificate should not be empty")
        
        // Парсим сертификат из base64
        certificate = CertificateUtils.parseCertificateFromBase64(certificateBase64)
        
        println("Received certificate (Base64):")
        println(certificateBase64)
        println("Certificate subject: ${certificate.subjectX500Principal}")
        println("Certificate issuer: ${certificate.issuerX500Principal}")
        println("Certificate valid from: ${certificate.notBefore}")
        println("Certificate valid until: ${certificate.notAfter}")
    }

    @Test
    @Order(2)
    fun testHeartbeatWithMtls(): Unit = runBlocking {
        // Проверяем, что сертификат получен в предыдущем тесте
        Assertions.assertNotNull(certificate, "Certificate should be available from previous test")
        
        // 4. Отправляем запрос heartbeat с mTLS авторизацией на tms.sbertroika.tech
        val client = GrpcClientUtils.createMtlsClient(TMS_SERVER, PORT, keyPair.private, certificate)
        
        val request = HeartbeatRequest.newBuilder()
            .setCharge(40)
            .setPosition(
                Position.newBuilder()
                    .setLatitude(0.00)
                    .setLongitude(0.00)
            )
            .setTerminalTime(
                Timestamp.newBuilder()
                    .setSeconds(System.currentTimeMillis() / 1000)
                    .setNanos(System.nanoTime().toInt())
            )
            .setTimeZone("+03:00")
            .setTimeZoneName("Europe/Moscow")
            .addImei("000000000000000000")
            .build()
        
        val metadata = GrpcClientUtils.createMetadata(SERIAL_NUMBER)
        val response = client.heartbeat(request, metadata)
        
        println("Heartbeat response: $response")
        
        // Проверяем, что нет ошибки
        assertFalse(response.hasError(), "Heartbeat should not have error")
        
        // Выводим время сервера
        val serverTime = Instant.ofEpochSecond(
            response.result.serverTime.seconds,
            response.result.serverTime.nanos.toLong()
        ).atZone(ZoneId.of("Europe/Moscow")).toLocalDateTime()
        
        println("Server time: $serverTime")
    }

    @Test
    @Order(3)
    fun testTerminalUserListWithMtls(): Unit = runBlocking {
        // Дополнительный тест для проверки других методов с mTLS
        val client = GrpcClientUtils.createMtlsClient(TMS_SERVER, PORT, keyPair.private, certificate)
        
        val request = TerminalUserListRequest.newBuilder().build()
        val metadata = GrpcClientUtils.createMetadata(SERIAL_NUMBER)
        val response = client.terminalUserList(request, metadata)
        
        println("TerminalUserList response: $response")
        assertFalse(response.hasError(), "TerminalUserList should not have error")
    }

    @Test
    @Order(4)
    fun testGetManifest(): Unit = runBlocking {
        val client = GrpcClientUtils.createMtlsClient(TMS_SERVER, PORT, keyPair.private, certificate)
        val response = client.getManifest(Empty.getDefaultInstance())
        manifestId = response.manifest.id
        manifestVersion.set(response.manifest.version)

        println("GetManifest response: $response")
        assertFalse(response.hasError(), "GetManifest should not have error")
    }

    @Test
    @Order(5)
    fun testGetStationList(): Unit = runBlocking {
        val client = GrpcClientUtils.createMtlsClient(TMS_SERVER, PORT, keyPair.private, certificate)
        val response = client.getStationList(
            manifest {
                id = manifestId
                version = manifestVersion.get()
            }
        )
        println("GetStationList response: $response")
        assertFalse(response.hasError(), "GetStationList should not have error")
    }

    @Test
    @Order(6)
    fun testGetProductList(): Unit = runBlocking {
        val client = GrpcClientUtils.createMtlsClient(TMS_SERVER, PORT, keyPair.private, certificate)
        val response = client.getProductList(
            manifest {
                id = manifestId
                version = manifestVersion.get()
            }
        )
        println("GetProductList response: $response")
        assertFalse(response.hasError(), "GetProductList should not have error")
    }

    @Test
    @Order(7)
    fun testGetTariffList(): Unit = runBlocking {
        val client = GrpcClientUtils.createMtlsClient(TMS_SERVER, PORT, keyPair.private, certificate)
        val response = client.getTariffList(
            manifest {
                id = manifestId
                version = manifestVersion.get()
            }
        )
        println("GetTariffList response: $response")
        assertFalse(response.hasError(), "GetTariffList should not have error")
    }

    @Test
    @Order(8)
    fun testGetRouteList(): Unit = runBlocking {
        val client = GrpcClientUtils.createMtlsClient(TMS_SERVER, PORT, keyPair.private, certificate)
        val response = client.getRouteList(
            manifest {
                id = manifestId
                version = manifestVersion.get()
            }
        )
        println("GetRouteList response: $response")
        assertFalse(response.hasError(), "GetRouteList should not have error")
    }

    @Test
    @Order(9)
    fun testGetTransportList(): Unit = runBlocking {
        val client = GrpcClientUtils.createMtlsClient(TMS_SERVER, PORT, keyPair.private, certificate)
        val response = client.getTransportList(
            manifest {
                id = manifestId
                version = manifestVersion.get()
            }
        )
        println("GetTransportList response: $response")
        assertFalse(response.hasError(), "GetTransportList should not have error")
    }

    @Test
    @Order(10)
    fun testGetProductMenu(): Unit = runBlocking {
        val client = GrpcClientUtils.createMtlsClient(TMS_SERVER, PORT, keyPair.private, certificate)
        val response = client.getProductMenu(
            manifest {
                id = manifestId
                version = manifestVersion.get()
            }
        )
        println("GetProductMenu response: $response")
        assertFalse(response.hasError(), "GetProductMenu should not have error")
    }

    @Test
    @Order(11)
    fun testGetSubscriptionSettings(): Unit = runBlocking {
        val client = GrpcClientUtils.createMtlsClient(TMS_SERVER, PORT, keyPair.private, certificate)
        val response = client.getSubscriptionSettings(
            manifest {
                id = manifestId
                version = manifestVersion.get()
            }
        )
        println("GetSubscriptionSettings response: $response")
        assertFalse(response.hasError(), "GetSubscriptionSettings should not have error")
    }
} 