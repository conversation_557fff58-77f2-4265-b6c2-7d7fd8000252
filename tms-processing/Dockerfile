#FROM arm64v8/gradle:8.14-jdk17 as builder
FROM gradle:8.13-jdk17-alpine as builder
ARG GRADLE_USER_HOME=/tmp/.gradle
ENV GRADLE_USER_HOME=$GRADLE_USER_HOME
RUN apk add gcompat

WORKDIR /build
ADD . /build

COPY .ci-gradle/gradle.properties /tmp/.gradle/gradle.properties
COPY .ci-gradle/gradle.properties /home/<USER>/.gradle/gradle.properties
COPY .ci-gradle/gradle.properties /root/.gradle/gradle.properties

RUN env
RUN gradle --no-daemon :tms-processing:bootJar -i



#--------------------------------------
#FROM eclipse-temurin:18-jdk
#30.08.2025 CVE 0H6M9L
FROM swr.ru-moscow-1.hc.sbercloud.ru/tkp3/kotlin-ms:latest
# Если нужны нативные библиотеки, раскомментируйте следующую строку
# RUN apk add --no-cache libstdc++
# COPY ./lib-tms/lib/* /opt/libs/
# ENV LD_LIBRARY_PATH=/opt/libs

COPY --from=builder /build/tms-processing/build/libs/tms-processing-*.jar ./tms-processing.jar
EXPOSE 8080 6000

ENTRYPOINT ["java", "-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:6000", "-Djava.security.egd=file:/dev/./urandom", "-jar", "tms-processing.jar"]