package ru.sbertroika.tms.processing.consumer

import com.fasterxml.jackson.core.json.JsonReadFeature
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import org.junit.jupiter.api.Test

class BatchProcessingConsumerTest {

    private val mapper = ObjectMapper()

    init {
        mapper.configure(JsonReadFeature.ALLOW_UNESCAPED_CONTROL_CHARS.mappedFeature(), true)
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
        mapper.registerKotlinModule()
    }

    @Test
    fun testDateTimeConvert() {
//        val batch =
    }

    companion object {
        private const val BATCH = "" +
                "{\n" +
                "\t\"terminalSerial\": \"1003352070040230\",\n" +
                "\t\"events\": [\n" +
                "\t\t{\n" +
                "\t\t\t\"type\": 4,\n" +
                "\t\t\t\"createdAt\": 1699593891947000,\n" +
                "\t\t\t\"attributes\": {\n" +
                "\t\t\t\t\"ern\": \"3\",\n" +
                "\t\t\t\t\"tickets\": \"0-1-1-1:1699-5938-9198-2;0-1-1-1:1699-5938-9200-3;\"\n" +
                "\t\t\t}\n" +
                "\t\t},\n" +
                "\t\t{\n" +
                "\t\t\t\"type\": 6,\n" +
                "\t\t\t\"createdAt\": 1699593894936000,\n" +
                "\t\t\t\"attributes\": {\n" +
                "\t\t\t\t\"ern\": \"4\",\n" +
                "\t\t\t\t\"tickets\": \"0-1-1-1:1699-5938-9198-2;0-1-1-1:1699-5938-9200-3;\"\n" +
                "\t\t\t}\n" +
                "\t\t}\n" +
                "\t],\n" +
                "\t\"tickets\": [\n" +
                "\t\t{\n" +
                "\t\t\t\"ticketSeries\": \"0-1-1-1\",\n" +
                "\t\t\t\"ticketNumber\": \"1699-5938-9198-2\",\n" +
                "\t\t\t\"createdAt\": 1699593891987000,\n" +
                "\t\t\t\"manifest\": \"test-2023-10-23\",\n" +
                "\t\t\t\"manifestVersion\": 1,\n" +
                "\t\t\t\"shiftNumber\": 1,\n" +
                "\t\t\t\"serviceId\": \"\",\n" +
                "\t\t\t\"tariffId\": \"445bf378-c22b-4e08-b637-6b83e3d9408a\"\n" +
                "\t\t},\n" +
                "\t\t{\n" +
                "\t\t\t\"ticketSeries\": \"0-1-1-1\",\n" +
                "\t\t\t\"ticketNumber\": \"1699-5938-9200-3\",\n" +
                "\t\t\t\"createdAt\": 1699593892001000,\n" +
                "\t\t\t\"manifest\": \"test-2023-10-23\",\n" +
                "\t\t\t\"manifestVersion\": 1,\n" +
                "\t\t\t\"shiftNumber\": 1,\n" +
                "\t\t\t\"serviceId\": \"\",\n" +
                "\t\t\t\"tariffId\": \"a5989707-d8a3-45bf-ab7a-3cab55ad722e\"\n" +
                "\t\t}\n" +
                "\t],\n" +
                "\t\"timeZone\": \"+03:00\",\n" +
                "\t\"timeZoneName\": \"Europe/Moscow\"\n" +
                "}"
    }
}