package ru.sbertroika.tms.processing

import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test

class DateTimeConverterTest {


    @Test
    fun testConvertTimeToZonedDateTime() {
        val time = 1699593891947000
        val ztime = DateTimeConverter.convertTimeToZonedDateTime(time, "Europe/Moscow")
        Assertions.assertEquals("2023-11-10T05:24:51Z", ztime.format(DATETIME_FMT))
    }
}