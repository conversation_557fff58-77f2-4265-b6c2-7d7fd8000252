spring:
  kafka:
#    bootstrap-servers: ${KAFKA_SERVERS:localhost:29092}
    bootstrap-servers: ${KAFKA_SERVERS:**********:9092;***********:9092;**********:9092}
#    bootstrap-servers: ${KAFKA_SERVERS:************:9092,***********:9092,************:9092} #prod

s3:
  url: ${S3_URL:http://s3:9001}
  access_key_id: ${S3_ACCESS_KEY_ID:s3__user}
  secret_access_key: ${S3_SECRET_ACCESS_KEY:s3__pass}
  region: ${S3_REGION:ru-moscow}
  bucket: ${S3_BUCKET:tkp3-manifest}

zookeeper:
  nodes: ${ZOOKEEPER_NODES:localhost:2181}
  namespace: ${ZOOKEEPER_NAMESPACE:tkp3}

server:
  port: 8888
