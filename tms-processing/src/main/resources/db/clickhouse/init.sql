create table tms_journal
(
    project_id              UUID comment 'Идентификатор проекта',
    event_type              Enum8('T_SHIFT_OPEN' = 0, 'T_SHIFT_CLOSE' = 1, 'T_EMV_PAY_SUCCESS' = 2, 'T_EMV_FAIL' = 3, 'T_CASH_PAY' = 4, 'T_SERVICE_STAT' = 5, 'T_PRINT_SUCCESS' = 6, 'T_PRINT_ERROR' = 7, 'T_SHIFT_REPORT_PRINT' = 8, 'T_EMV_BIN_STOP_LIST' = 9, 'T_EMV_PAN_STOP_LIST' = 10, 'T_TK_PROSTOR_PAY' = 11) comment 'Тип события',
    created_at              DateTime('UTC') comment 'Серверное время получения события',
    terminal_serial         String comment 'Идентификатор терминала',
    ern                     <PERSON>ullable(UInt64) comment 'Единый регистрационный номер (уникальныйы в рамках смены)',
    user_id                 Nullable(UUID) comment 'Идентификатор пользователя',
    shift_num               Nullable(UInt32) comment 'Номер смены на терминале',
    stop_list_version       Nullable(UInt64) comment 'Версия стоп-листа',
    stop_list_update        Nullable(DateTime('UTC')) comment 'Дата обновления стоп-листа',
    error_code              Nullable(UInt32) comment 'Код ошибки',
    error_message           Nullable(String) comment 'Сообщение об ошибке',
    value                   Nullable(String) comment 'Значение в рамках события'
)
    engine = MergeTree PARTITION BY toYYYYMM(created_at)
        ORDER BY (project_id, created_at)
        SETTINGS index_granularity = 8192;

ALTER TABLE tms_journal modify column event_type Enum8('T_SHIFT_OPEN' = 0, 'T_SHIFT_CLOSE' = 1, 'T_EMV_PAY_SUCCESS' = 2, 'T_EMV_FAIL' = 3, 'T_CASH_PAY' = 4, 'T_SERVICE_STAT' = 5, 'T_PRINT_SUCCESS' = 6, 'T_PRINT_ERROR' = 7, 'T_SHIFT_REPORT_PRINT' = 8, 'T_EMV_BIN_STOP_LIST' = 9, 'T_EMV_PAN_STOP_LIST' = 10, 'T_TK_PROSTOR_PAY' = 11, 'T_POLL_CARD' = 12, 'T_CARD_PROCESS_FAIL' = 13, 'T_TROIKA_WALLET_PAY_SUCCESS' = 14, 'T_TROIKA_WALLET_PAY_FAIL' = 15, 'T_EMV_PAR_STOP_LIST' = 16, 'T_TROIKA_STOP_LIST' = 17, 'T_ROUTE_SELECT' = 18, 'T_VEHICLE_SELECT' = 19);
ALTER TABLE tms_journal modify column error_code Nullable(Int32);

CREATE TABLE kafka_tms_journal_out
(
    projectId           String,
    eventType           String,
    createdAt           String,
    terminalSerial      String,
    ern                 Nullable(UInt64),
    userId              Nullable(String),
    shiftNum            Nullable(UInt32),
    stopListVersion     Nullable(String),
    stopListUpdate      Nullable(String),
    errorCode           Nullable(Int32),
    errorMessage        Nullable(String),
    value               Nullable(String)
)
    ENGINE = Kafka('10.4.32.25:9092,10.4.32.140:9092,10.4.32.88:9092', 'TMS.JOURNAL.OUT', 'clickhouse',
             'JSONEachRow') settings kafka_thread_per_consumer = 1, kafka_num_consumers = 1;

CREATE MATERIALIZED VIEW tms_journal_mv TO tms_journal as
SELECT projectId                                            as project_id,
       eventType                                            as event_type,
       parseDateTimeBestEffort(createdAt, 'UTC')            as created_at,
       terminalSerial                                       as terminal_serial,
       ern,
       userId                                               as user_id,
       shiftNum                                             as shift_num,
       toUInt64OrNull(stopListVersion)                      as stop_list_version,
       parseDateTimeBestEffortOrNull(stopListUpdate, 'UTC') as stop_list_update,
       errorCode                                            as error_code,
       errorMessage                                         as error_message,
       value
FROM kafka_tms_journal_out;

ALTER TABLE tms_journal modify column event_type Enum8('T_SHIFT_OPEN' = 0, 'T_SHIFT_CLOSE' = 1, 'T_EMV_PAY_SUCCESS' = 2, 'T_EMV_FAIL' = 3, 'T_CASH_PAY' = 4, 'T_SERVICE_STAT' = 5, 'T_PRINT_SUCCESS' = 6, 'T_PRINT_ERROR' = 7, 'T_SHIFT_REPORT_PRINT' = 8, 'T_EMV_BIN_STOP_LIST' = 9, 'T_EMV_PAN_STOP_LIST' = 10, 'T_TK_PROSTOR_PAY' = 11, 'T_POLL_CARD' = 12, 'T_CARD_PROCESS_FAIL' = 13, 'T_TROIKA_WALLET_PAY_SUCCESS' = 14, 'T_TROIKA_WALLET_PAY_FAIL' = 15, 'T_EMV_PAR_STOP_LIST' = 16, 'T_TROIKA_STOP_LIST' = 17, 'T_ROUTE_SELECT' = 18, 'T_VEHICLE_SELECT' = 19, 'T_TROIKA_TICKET_PAY_SUCCESS' = 20, 'T_TROIKA_TICKET_PAY_FAIL' = 21, 'T_ABT_WALLET_PAY_SUCCESS' = 22, 'T_ABT_WALLET_PAY_FAIL' = 23, 'T_ABT_TICKET_PAY_SUCCESS' = 24, 'T_ABT_TICKET_PAY_FAIL' = 25, 'T_QR_WALLET_PAY_SUCCESS' = 26, 'T_QR_WALLET_PAY_FAIL' = 27, 'T_QR_TICKET_PAY_SUCCESS' = 28, 'T_QR_TICKET_PAY_FAIL' = 29, 'T_TARIFF_NOT_FOUND' = 30, 'T_KRS_CONTROL_SUCCESS' = 31, 'T_KRS_CONTROL_FAIL' = 32, 'T_KRS_CONFIG_ERROR' = 33, 'T_CARD_ATTACH_AGAIN' = 34);