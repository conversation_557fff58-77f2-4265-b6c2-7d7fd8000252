spring:
  application:
    name: tms-processing
  main:
    allow-bean-definition-overriding: true

  kafka:
    bootstrap-servers: ${KAFKA_SERVERS:localhost:9092;localhost:9093}
    listener:
      ack-mode: manual
    consumer:
      enable-auto-commit: false
      properties:
        spring:
          json:
            trusted:
              packages: "*"
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    tms_batches_in_topic: ${TMS_BATCHES_IN_TOPIC:TMS.BATCHES.IN}
    pro_operation_in_topic: ${PRO_IN_TOPIC:PRO.OPERATION.IN}
    pro_operation_shift_in_topic: ${PRO_IN_TOPIC:PRO.OPERATION.SHIFT.IN}
    tms_event_fail_out_topic: ${TMS_EVENT_FAIL_OUT_TOPIC:TMS.EVENT.FAIL.OUT}
    tms_batch_fail_out_topic: ${TMS_BATCH_FAIL_OUT_TOPIC:TMS.BATCH.FAIL.OUT}
    tms_journal_out_topic: ${PRO_IN_TOPIC:TMS.JOURNAL.OUT}
    tms_batch_processing_in_group: ${BATCH_PROCESSING_IN_GROUP:tms_batch_processing_in_group}
    qr_operation_in_topic: ${QR_IN_TOPIC:QR.OPERATION.IN}
    check_operation_in_topic: ${CHECK_IN_TOPIC:CHECK.OPERATION.IN}

  r2dbc:
    url: r2dbc:pool:${R2DB_URL:postgresql://postgres:postgres@localhost:5432/tms}

server:
  port: 8080

s3:
  url: ${S3_URL}
  access_key_id: ${S3_ACCESS_KEY_ID}
  secret_access_key: ${S3_SECRET_ACCESS_KEY}
  region: ${S3_REGION:ru-moscow}
  bucket: ${S3_BUCKET:tkp3-manifest}

zookeeper:
  nodes: ${ZOOKEEPER_NODES:localhost:2181,localhost:2182,localhost:2183}
  namespace: ${ZOOKEEPER_NAMESPACE:tkp3}

logging:
  structured:
    format:
      console: ecs
      file: ecs
  level:
    root: ${LOG_LEVEL:INFO}
    io.r2dbc.postgresql: ${LOG_LEVEL:INFO}
    org.zalando.logbook: TRACE

logbook:
  predicate:
    include:
      - path: /**
        methods:
          - GET
          - POST
  filter.enabled: false
  secure-filter.enabled: true
  format.style: json
  strategy: default
  minimum-status: 200
  obfuscate:
    headers:
      - Authorization

management:
  endpoint:
    health:
      show-details: always
      probes:
        enabled: true
      group:
        liveness:
          include: '*'
        readiness:
          include: '*'
