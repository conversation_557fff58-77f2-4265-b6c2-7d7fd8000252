package ru.sbertroika.tms.processing

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import ru.sbertroika.common.tms.EventAttribute
import ru.sbertroika.common.tms.EventType
import ru.sbertroika.tms.model.TerminalEvent
import ru.sbertroika.tms.model.TerminalJournalEvent
import ru.sbertroika.tms.processing.DateTimeConverter.convertTimeToZonedDateTime
import java.time.Instant
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.*

object EventConverter {

    private val POLL_CARD_TPL = "uid=%s, sak=%s, type=%s, atqa=%s"

    fun convertEvent(projectId: String, event: TerminalEvent, terminalSerial: String, timeZoneName: String): Either<Error, TerminalJournalEvent> {
        return try {
            when (EventType.values()[event.type]) {
                // События открытия/закрытия смены
                EventType.T_SHIFT_OPEN -> makeShiftOpenEvent(event, projectId, terminalSerial, timeZoneName)
                EventType.T_SHIFT_CLOSE -> makeShiftCloseEvent(event, projectId, terminalSerial, timeZoneName)

                // События печати отчетов
                EventType.T_SHIFT_REPORT_PRINT -> makeShiftReportPrintEvent(event, projectId, terminalSerial, timeZoneName)

                // События отказа в обслуживании
                EventType.T_EMV_FAIL -> makeEmvFailEvent(event, projectId, terminalSerial, timeZoneName)
                EventType.T_CARD_PROCESS_FAIL -> makePollCardEvent(event, projectId, terminalSerial, timeZoneName)
                EventType.T_TROIKA_WALLET_PAY_FAIL -> makeTroikaWalletPayFailEvent(event, projectId, terminalSerial, timeZoneName)
                EventType.T_TROIKA_TICKET_PAY_FAIL -> makeTroikaTicketPayFailEvent(event, projectId, terminalSerial, timeZoneName)
                EventType.T_ABT_WALLET_PAY_FAIL -> makeAbtWalletPayFailEvent(event, projectId, terminalSerial, timeZoneName)
                EventType.T_ABT_TICKET_PAY_FAIL -> makeAbtTicketPayFailEvent(event, projectId, terminalSerial, timeZoneName)
                EventType.T_QR_WALLET_PAY_FAIL -> makeQrWalletPayFailEvent(event, projectId, terminalSerial, timeZoneName)

                EventType.T_TARIFF_NOT_FOUND -> makeTariffNotFoundEvent(event, projectId, terminalSerial, timeZoneName)

                // События отказа в обслуживании по стоп-листам
                EventType.T_EMV_BIN_STOP_LIST -> makeEmvBinStopListEvent(event, projectId, terminalSerial, timeZoneName)
                EventType.T_EMV_PAN_STOP_LIST -> makeEmvPanStopListEvent(event, projectId, terminalSerial, timeZoneName)
                EventType.T_EMV_PAR_STOP_LIST -> makeEmvParStopListEvent(event, projectId, terminalSerial, timeZoneName)
                EventType.T_TROIKA_STOP_LIST -> makeTroikaStopListEvent(event, projectId, terminalSerial, timeZoneName)

                // Сервисные события
                EventType.T_SERVICE_STAT -> makeServiceStatEvent(event, projectId, terminalSerial, timeZoneName)
                EventType.T_ROUTE_SELECT -> makeRouteSelectEvent(event, projectId, terminalSerial, timeZoneName)
                EventType.T_VEHICLE_SELECT -> makeVehicleSelectEvent(event, projectId, terminalSerial, timeZoneName)

                // События обработки карт
                EventType.T_POLL_CARD -> makePollCardEvent(event, projectId, terminalSerial, timeZoneName)

                // События печати
                EventType.T_PRINT_SUCCESS -> makePrintSuccessEvent(event, projectId, terminalSerial, timeZoneName)
                EventType.T_PRINT_ERROR -> makePrintFailEvent(event, projectId, terminalSerial, timeZoneName)

                // События оплаты
                EventType.T_EMV_PAY_SUCCESS -> makeEmvPaySuccessEvent(event, projectId, terminalSerial, timeZoneName)
                EventType.T_CASH_PAY -> makeCashPaySuccessEvent(event, projectId, terminalSerial, timeZoneName)
                EventType.T_TK_PROSTOR_PAY -> makePostorPaySuccessEvent(event, projectId, terminalSerial, timeZoneName)
                EventType.T_TROIKA_WALLET_PAY_SUCCESS -> makeTroikaWalletPaySuccessEvent(event, projectId, terminalSerial, timeZoneName)
                EventType.T_TROIKA_TICKET_PAY_SUCCESS -> makeTroikaTicketPaySuccessEvent(event, projectId, terminalSerial, timeZoneName)
                EventType.T_ABT_WALLET_PAY_SUCCESS -> makeAbtWalletPaySuccessEvent(event, projectId, terminalSerial, timeZoneName)
                EventType.T_ABT_TICKET_PAY_SUCCESS -> makeAbtTicketPaySuccessEvent(event, projectId, terminalSerial, timeZoneName)
                EventType.T_QR_WALLET_PAY_SUCCESS -> makeQrWalletPaySuccessEvent(event, projectId, terminalSerial, timeZoneName)

                else -> Error("Unknown event ${event.type}").left()
            }
        } catch (e: Exception) {
            Error(e).left()
        }
    }

    /* События открытия/закрытия смены */
    private fun makeShiftOpenEvent(event: TerminalEvent, projectId: String, terminalSerial: String, timeZoneName: String) = TerminalJournalEvent(
        projectId = projectId,
        eventType = EventType.values()[event.type],
        createdAt = convertTimeToZonedDateTime(event.createdAt.time, timeZoneName),
        terminalSerial = terminalSerial,
        ern = event.attributes.getIntAttribute(EventAttribute.EA_ERN)!!,
        userId = event.attributes.getStringAttribute(EventAttribute.EA_USER_ID),
        shiftNum = event.attributes.getIntAttribute(EventAttribute.EA_SHIFT_NUM)
    ).right()

    private fun makeShiftCloseEvent(event: TerminalEvent, projectId: String, terminalSerial: String, timeZoneName: String) = TerminalJournalEvent(
        projectId = projectId,
        eventType = EventType.values()[event.type],
        createdAt = convertTimeToZonedDateTime(event.createdAt.time, timeZoneName),
        terminalSerial = terminalSerial,
        ern = event.attributes.getIntAttribute(EventAttribute.EA_ERN)!!,
        userId = event.attributes.getStringAttribute(EventAttribute.EA_USER_ID),
        shiftNum = event.attributes.getIntAttribute(EventAttribute.EA_SHIFT_NUM)
    ).right()

    /* События печати отчетов */
    private fun makeShiftReportPrintEvent(event: TerminalEvent, projectId: String, terminalSerial: String, timeZoneName: String) = TerminalJournalEvent(
        projectId = projectId,
        eventType = EventType.values()[event.type],
        createdAt = convertTimeToZonedDateTime(event.createdAt.time, timeZoneName),
        terminalSerial = terminalSerial,
        ern = event.attributes.getIntAttribute(EventAttribute.EA_ERN)!!,
        userId = event.attributes.getStringAttribute(EventAttribute.EA_USER_ID),
        shiftNum = event.attributes.getIntAttribute(EventAttribute.EA_SHIFT_NUM)
    ).right()

    /* События отказа в обслуживании */
    private fun makeEmvFailEvent(event: TerminalEvent, projectId: String, terminalSerial: String, timeZoneName: String) = TerminalJournalEvent(
        projectId = projectId,
        eventType = EventType.values()[event.type],
        createdAt = convertTimeToZonedDateTime(event.createdAt.time, timeZoneName),
        terminalSerial = terminalSerial,
        ern = event.attributes.getIntAttribute(EventAttribute.EA_ERN)!!,
        userId = event.attributes.getStringAttribute(EventAttribute.EA_USER_ID),
        shiftNum = event.attributes.getIntAttribute(EventAttribute.EA_SHIFT_NUM),
        errorCode = event.attributes.getIntAttribute(EventAttribute.EA_CODE),
        errorMessage = event.attributes.getStringAttribute(EventAttribute.EA_MESSAGE)
    ).right()

    private fun makeEmvBinStopListEvent(event: TerminalEvent, projectId: String, terminalSerial: String, timeZoneName: String) = TerminalJournalEvent(
        projectId = projectId,
        eventType = EventType.values()[event.type],
        createdAt = convertTimeToZonedDateTime(event.createdAt.time, timeZoneName),
        terminalSerial = terminalSerial,
        ern = event.attributes.getIntAttribute(EventAttribute.EA_ERN)!!,
        userId = event.attributes.getStringAttribute(EventAttribute.EA_USER_ID),
        shiftNum = event.attributes.getIntAttribute(EventAttribute.EA_SHIFT_NUM),
        value = event.attributes.getStringAttribute(EventAttribute.EA_PAN_HASH),
        stopListVersion = event.attributes.getStringAttribute(EventAttribute.EA_STOP_LIST_VERSION),
        stopListUpdate = ZonedDateTime.ofInstant(Instant.ofEpochSecond(event.attributes.getLongAttribute(EventAttribute.EA_STOP_LIST_VERSION) ?: 0), ZoneId.of("UTC"))
    ).right()

    private fun makeEmvPanStopListEvent(event: TerminalEvent, projectId: String, terminalSerial: String, timeZoneName: String) = TerminalJournalEvent(
        projectId = projectId,
        eventType = EventType.values()[event.type],
        createdAt = convertTimeToZonedDateTime(event.createdAt.time, timeZoneName),
        terminalSerial = terminalSerial,
        ern = event.attributes.getIntAttribute(EventAttribute.EA_ERN)!!,
        userId = event.attributes.getStringAttribute(EventAttribute.EA_USER_ID),
        shiftNum = event.attributes.getIntAttribute(EventAttribute.EA_SHIFT_NUM),
        value = event.attributes.getStringAttribute(EventAttribute.EA_PAN_HASH),
        stopListVersion = event.attributes.getStringAttribute(EventAttribute.EA_STOP_LIST_VERSION),
        stopListUpdate = ZonedDateTime.ofInstant(Instant.ofEpochSecond(event.attributes.getLongAttribute(EventAttribute.EA_STOP_LIST_VERSION) ?: 0), ZoneId.of("UTC"))
    ).right()

    private fun makeEmvParStopListEvent(event: TerminalEvent, projectId: String, terminalSerial: String, timeZoneName: String) = TerminalJournalEvent(
        projectId = projectId,
        eventType = EventType.values()[event.type],
        createdAt = convertTimeToZonedDateTime(event.createdAt.time, timeZoneName),
        terminalSerial = terminalSerial,
        ern = event.attributes.getIntAttribute(EventAttribute.EA_ERN)!!,
        userId = event.attributes.getStringAttribute(EventAttribute.EA_USER_ID),
        shiftNum = event.attributes.getIntAttribute(EventAttribute.EA_SHIFT_NUM),
        value = event.attributes.getStringAttribute(EventAttribute.EA_PAR),
        stopListVersion = event.attributes.getStringAttribute(EventAttribute.EA_STOP_LIST_VERSION),
        stopListUpdate = ZonedDateTime.ofInstant(Instant.ofEpochSecond(event.attributes.getLongAttribute(EventAttribute.EA_STOP_LIST_VERSION) ?: 0), ZoneId.of("UTC"))
    ).right()

    /* События печати */
    private fun makePrintFailEvent(event: TerminalEvent, projectId: String, terminalSerial: String, timeZoneName: String) = TerminalJournalEvent(
        projectId = projectId,
        eventType = EventType.values()[event.type],
        createdAt = convertTimeToZonedDateTime(event.createdAt.time, timeZoneName),
        terminalSerial = terminalSerial,
        ern = event.attributes.getIntAttribute(EventAttribute.EA_ERN)!!,
        userId = event.attributes.getStringAttribute(EventAttribute.EA_USER_ID),
        shiftNum = event.attributes.getIntAttribute(EventAttribute.EA_SHIFT_NUM),
        errorCode = event.attributes.getIntAttribute(EventAttribute.EA_CODE),
        value = try {
            String(Base64.getDecoder().decode(event.attributes.getStringAttribute(EventAttribute.EA_MESSAGE)))
        } catch (e: Exception) {
            ""
        }
    ).right()

    private fun makePrintSuccessEvent(event: TerminalEvent, projectId: String, terminalSerial: String, timeZoneName: String) = TerminalJournalEvent(
        projectId = projectId,
        eventType = EventType.values()[event.type],
        createdAt = convertTimeToZonedDateTime(event.createdAt.time, timeZoneName),
        terminalSerial = terminalSerial,
        ern = event.attributes.getIntAttribute(EventAttribute.EA_ERN)!!,
        userId = event.attributes.getStringAttribute(EventAttribute.EA_USER_ID),
        shiftNum = event.attributes.getIntAttribute(EventAttribute.EA_SHIFT_NUM),
        value = try {
            String(Base64.getDecoder().decode(event.attributes.getStringAttribute(EventAttribute.EA_MESSAGE)))
        } catch (e: Exception) {
            ""
        }
    ).right()

    /* Сервисные события */

    private fun makeServiceStatEvent(event: TerminalEvent, projectId: String, terminalSerial: String, timeZoneName: String) = TerminalJournalEvent(
        projectId = projectId,
        eventType = EventType.values()[event.type],
        createdAt = convertTimeToZonedDateTime(event.createdAt.time, timeZoneName),
        terminalSerial = terminalSerial,
        ern = event.attributes.getIntAttribute(EventAttribute.EA_ERN)!!,
        userId = event.attributes.getStringAttribute(EventAttribute.EA_USER_ID),
        shiftNum = event.attributes.getIntAttribute(EventAttribute.EA_SHIFT_NUM)
    ).right()

    /* События оплаты */
    private fun makeEmvPaySuccessEvent(event: TerminalEvent, projectId: String, terminalSerial: String, timeZoneName: String) = TerminalJournalEvent(
        projectId = projectId,
        eventType = EventType.values()[event.type],
        createdAt = convertTimeToZonedDateTime(event.createdAt.time, timeZoneName),
        terminalSerial = terminalSerial,
        ern = event.attributes.getIntAttribute(EventAttribute.EA_ERN)!!,
        userId = event.attributes.getStringAttribute(EventAttribute.EA_USER_ID),
        shiftNum = event.attributes.getIntAttribute(EventAttribute.EA_SHIFT_NUM)
    ).right()

    private fun makeCashPaySuccessEvent(event: TerminalEvent, projectId: String, terminalSerial: String, timeZoneName: String) = TerminalJournalEvent(
        projectId = projectId,
        eventType = EventType.values()[event.type],
        createdAt = convertTimeToZonedDateTime(event.createdAt.time, timeZoneName),
        terminalSerial = terminalSerial,
        ern = event.attributes.getIntAttribute(EventAttribute.EA_ERN)!!,
        userId = event.attributes.getStringAttribute(EventAttribute.EA_USER_ID),
        shiftNum = event.attributes.getIntAttribute(EventAttribute.EA_SHIFT_NUM)
    ).right()

    private fun makePostorPaySuccessEvent(event: TerminalEvent, projectId: String, terminalSerial: String, timeZoneName: String) = TerminalJournalEvent(
        projectId = projectId,
        eventType = EventType.values()[event.type],
        createdAt = convertTimeToZonedDateTime(event.createdAt.time, timeZoneName),
        terminalSerial = terminalSerial,
        ern = event.attributes.getIntAttribute(EventAttribute.EA_ERN)!!,
        userId = event.attributes.getStringAttribute(EventAttribute.EA_USER_ID),
        shiftNum = event.attributes.getIntAttribute(EventAttribute.EA_SHIFT_NUM)
    ).right()

    private fun makeTroikaWalletPaySuccessEvent(event: TerminalEvent, projectId: String, terminalSerial: String, timeZoneName: String) = TerminalJournalEvent(
        projectId = projectId,
        eventType = EventType.values()[event.type],
        createdAt = convertTimeToZonedDateTime(event.createdAt.time, timeZoneName),
        terminalSerial = terminalSerial,
        ern = event.attributes.getIntAttribute(EventAttribute.EA_ERN)!!,
        userId = event.attributes.getStringAttribute(EventAttribute.EA_USER_ID),
        shiftNum = event.attributes.getIntAttribute(EventAttribute.EA_SHIFT_NUM),
        value = "uid=${event.attributes.getStringAttribute(EventAttribute.EA_CARD_UID)}"
    ).right()

    private fun makeTroikaTicketPaySuccessEvent(event: TerminalEvent, projectId: String, terminalSerial: String, timeZoneName: String) =
        makeTroikaWalletPaySuccessEvent(event, projectId, terminalSerial, timeZoneName)

    private fun makeAbtWalletPaySuccessEvent(event: TerminalEvent, projectId: String, terminalSerial: String, timeZoneName: String) =
        makeTroikaWalletPaySuccessEvent(event, projectId, terminalSerial, timeZoneName)

    private fun makeAbtTicketPaySuccessEvent(event: TerminalEvent, projectId: String, terminalSerial: String, timeZoneName: String) =
        makeTroikaWalletPaySuccessEvent(event, projectId, terminalSerial, timeZoneName)

    private fun makeQrWalletPaySuccessEvent(event: TerminalEvent, projectId: String, terminalSerial: String, timeZoneName: String) =
        makeTroikaWalletPaySuccessEvent(event, projectId, terminalSerial, timeZoneName)

    /* События обработки карт */
    private fun makePollCardEvent(event: TerminalEvent, projectId: String, terminalSerial: String, timeZoneName: String) = TerminalJournalEvent(
        projectId = projectId,
        eventType = EventType.values()[event.type],
        createdAt = convertTimeToZonedDateTime(event.createdAt.time, timeZoneName),
        terminalSerial = terminalSerial,
        ern = event.attributes.getIntAttribute(EventAttribute.EA_ERN)!!,
        userId = event.attributes.getStringAttribute(EventAttribute.EA_USER_ID),
        shiftNum = event.attributes.getIntAttribute(EventAttribute.EA_SHIFT_NUM),
        value = String.format(
            POLL_CARD_TPL,
            event.attributes.getStringAttribute(EventAttribute.EA_CARD_UID),
            event.attributes.getStringAttribute(EventAttribute.EA_CARD_SAK),
            if (event.attributes.getStringAttribute(EventAttribute.EA_CARD_B) == "0") "A" else "B",
            event.attributes.getStringAttribute(EventAttribute.EA_CARD_ATQA)
        )
    ).right()

    private fun makeTroikaWalletPayFailEvent(event: TerminalEvent, projectId: String, terminalSerial: String, timeZoneName: String) = TerminalJournalEvent(
        projectId = projectId,
        eventType = EventType.values()[event.type],
        createdAt = convertTimeToZonedDateTime(event.createdAt.time, timeZoneName),
        terminalSerial = terminalSerial,
        ern = event.attributes.getIntAttribute(EventAttribute.EA_ERN)!!,
        userId = event.attributes.getStringAttribute(EventAttribute.EA_USER_ID),
        shiftNum = event.attributes.getIntAttribute(EventAttribute.EA_SHIFT_NUM),
        value = "uid=${event.attributes.getStringAttribute(EventAttribute.EA_CARD_UID)}",
        errorCode = event.attributes.getIntAttribute(EventAttribute.EA_CODE),
        errorMessage = event.attributes.getStringAttribute(EventAttribute.EA_MESSAGE)
    ).right()

    private fun makeTroikaTicketPayFailEvent(event: TerminalEvent, projectId: String, terminalSerial: String, timeZoneName: String) =
        makeTroikaWalletPayFailEvent(event, projectId, terminalSerial, timeZoneName)

    private fun makeAbtWalletPayFailEvent(event: TerminalEvent, projectId: String, terminalSerial: String, timeZoneName: String) =
        makeTroikaWalletPayFailEvent(event, projectId, terminalSerial, timeZoneName)

    private fun makeAbtTicketPayFailEvent(event: TerminalEvent, projectId: String, terminalSerial: String, timeZoneName: String) =
        makeTroikaWalletPayFailEvent(event, projectId, terminalSerial, timeZoneName)

    private fun makeQrWalletPayFailEvent(event: TerminalEvent, projectId: String, terminalSerial: String, timeZoneName: String) =
        makeTroikaWalletPayFailEvent(event, projectId, terminalSerial, timeZoneName)

    private fun makeTroikaStopListEvent(event: TerminalEvent, projectId: String, terminalSerial: String, timeZoneName: String) = TerminalJournalEvent(
        projectId = projectId,
        eventType = EventType.values()[event.type],
        createdAt = convertTimeToZonedDateTime(event.createdAt.time, timeZoneName),
        terminalSerial = terminalSerial,
        ern = event.attributes.getIntAttribute(EventAttribute.EA_ERN)!!,
        userId = event.attributes.getStringAttribute(EventAttribute.EA_USER_ID),
        shiftNum = event.attributes.getIntAttribute(EventAttribute.EA_SHIFT_NUM),
        value = "uid=${event.attributes.getStringAttribute(EventAttribute.EA_CARD_UID)}",
        stopListVersion = event.attributes.getStringAttribute(EventAttribute.EA_STOP_LIST_VERSION),
        stopListUpdate = ZonedDateTime.ofInstant(Instant.ofEpochSecond(event.attributes.getLongAttribute(EventAttribute.EA_STOP_LIST_VERSION) ?: 0), ZoneId.of("UTC"))
    ).right()

    private fun makeRouteSelectEvent(event: TerminalEvent, projectId: String, terminalSerial: String, timeZoneName: String) = TerminalJournalEvent(
        projectId = projectId,
        eventType = EventType.values()[event.type],
        createdAt = convertTimeToZonedDateTime(event.createdAt.time, timeZoneName),
        terminalSerial = terminalSerial,
        ern = event.attributes.getIntAttribute(EventAttribute.EA_ERN)!!,
        userId = event.attributes.getStringAttribute(EventAttribute.EA_USER_ID),
        shiftNum = event.attributes.getIntAttribute(EventAttribute.EA_SHIFT_NUM),
        value = "routeId=${event.attributes.getStringAttribute(EventAttribute.EA_ROUTE_ID)}"
    ).right()

    private fun makeVehicleSelectEvent(event: TerminalEvent, projectId: String, terminalSerial: String, timeZoneName: String) = TerminalJournalEvent(
        projectId = projectId,
        eventType = EventType.values()[event.type],
        createdAt = convertTimeToZonedDateTime(event.createdAt.time, timeZoneName),
        terminalSerial = terminalSerial,
        ern = event.attributes.getIntAttribute(EventAttribute.EA_ERN)!!,
        userId = event.attributes.getStringAttribute(EventAttribute.EA_USER_ID),
        shiftNum = event.attributes.getIntAttribute(EventAttribute.EA_SHIFT_NUM),
        value = "vehicleId=${event.attributes.getStringAttribute(EventAttribute.EA_VEHICLE_ID)}"
    ).right()

    private fun makeTariffNotFoundEvent(event: TerminalEvent, projectId: String, terminalSerial: String, timeZoneName: String) = TerminalJournalEvent(
        projectId = projectId,
        eventType = EventType.values()[event.type],
        createdAt = convertTimeToZonedDateTime(event.createdAt.time, timeZoneName),
        terminalSerial = terminalSerial,
        ern = event.attributes.getIntAttribute(EventAttribute.EA_ERN)!!,
        userId = event.attributes.getStringAttribute(EventAttribute.EA_USER_ID),
        shiftNum = event.attributes.getIntAttribute(EventAttribute.EA_SHIFT_NUM),
        value = "routeId=${event.attributes.getStringAttribute(EventAttribute.EA_ROUTE_ID)},payType=${event.attributes.getStringAttribute(EventAttribute.EA_PAY_TYPE)}"
    ).right()
}