package ru.sbertroika.tms.processing

import ru.sbertroika.common.tms.EventAttribute
import ru.sbertroika.common.tms.EventType
import ru.sbertroika.common.tms.OperationRawFormat
import ru.sbertroika.tkp3.manifest.model.Manifest
import ru.sbertroika.tkp3.pro.api.model.Operation
import ru.sbertroika.tkp3.pro.api.model.OperationType
import ru.sbertroika.tkp3.pro.api.model.ShiftOperation
import ru.sbertroika.tkp3.pro.api.model.ShiftOperationType
import ru.sbertroika.tms.model.TerminalEvent
import ru.sbertroika.tms.model.Ticket
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.*

object OperationConverter {

    fun convertPassCashOperation(tickets: List<Ticket>, payEvent: TerminalEvent, terminalSerial: String, timeZoneName: String, terminalId: UUID): Operation =
        Operation(
            type = OperationType.PASS_CASH,
            terminalSerial = terminalSerial,
            terminalId = terminalId,
            ern = payEvent.attributes.getIntAttribute(EventAttribute.EA_ERN)!!,
            tickets = tickets.map { ticket -> TicketConverter.convertTicket(ticket, timeZoneName) }.toList()
        )

    fun convertPassEmvOperation(tickets: List<Ticket>, payEvent: TerminalEvent, terminalSerial: String, tid: String?, timeZoneName: String, terminalId: UUID): Operation =
        Operation(
            type = OperationType.PASS_EMV,
            terminalSerial = terminalSerial,
            tid = tid,
            terminalId = terminalId,
            ern = payEvent.attributes.getIntAttribute(EventAttribute.EA_ERN)!!,
            raw = payEvent.attributes.getStringAttribute(EventAttribute.EA_RAW)!!,
            rawType = when (OperationRawFormat.valueOf(payEvent.attributes.getStringAttribute(EventAttribute.EA_RAW_FORMAT)!!)) {
                OperationRawFormat.ORF_CB64 -> ru.sbertroika.tkp3.pro.api.model.OperationRawFormat.CB64
                else -> ru.sbertroika.tkp3.pro.api.model.OperationRawFormat.CB64
            },
            tickets = tickets.map { ticket -> TicketConverter.convertTicket(ticket, timeZoneName) }.toList()
        )

    fun convertPassTroikaWalletOperation(tickets: List<Ticket>, payEvent: TerminalEvent, terminalSerial: String, tid: String?, timeZoneName: String, terminalId: UUID): Operation =
        Operation(
            type = if (payEvent.type == EventType.T_TROIKA_WALLET_PAY_SUCCESS_VALUE) OperationType.PASS_TROIKA_WALLET else OperationType.PASS_TROIKA_TICKET,
            terminalSerial = terminalSerial,
            tid = tid,
            terminalId = terminalId,
            ern = payEvent.attributes.getIntAttribute(EventAttribute.EA_ERN)!!,
            raw = payEvent.attributes.getStringAttribute(EventAttribute.EA_RAW)!!,
            tickets = tickets.map { ticket -> TicketConverter.convertTicket(ticket, timeZoneName) }.toList(),
            cardUid = payEvent.attributes.getStringAttribute(EventAttribute.EA_CARD_UID)!!,
            // Должен быть обязательно шаблон по которому была произведена операция списания по билету
            templateId = if (payEvent.type == EventType.T_TROIKA_TICKET_PAY_SUCCESS_VALUE) UUID.fromString(payEvent.attributes.getStringAttribute(EventAttribute.EA_TEMPLATE_ID)) else null
        )

    fun convertPassAbtOperation(tickets: List<Ticket>, payEvent: TerminalEvent, terminalSerial: String, tid: String?, timeZoneName: String, terminalId: UUID): Operation =
        Operation(
            type = if (payEvent.type == EventType.T_ABT_WALLET_PAY_SUCCESS_VALUE) OperationType.PASS_ABT_WALLET else OperationType.PASS_ABT_TICKET,
            terminalSerial = terminalSerial,
            tid = tid,
            terminalId = terminalId,
            ern = payEvent.attributes.getIntAttribute(EventAttribute.EA_ERN)!!,
            raw = payEvent.attributes.getStringAttribute(EventAttribute.EA_RAW)!!,
            tickets = tickets.map { ticket -> TicketConverter.convertTicket(ticket, timeZoneName) }.toList(),
            cardUid = payEvent.attributes.getStringAttribute(EventAttribute.EA_CARD_UID),
            cardHash = payEvent.attributes.getStringAttribute(EventAttribute.EA_PAN_HASH),
            cardHashType = if (payEvent.attributes.getStringAttribute(EventAttribute.EA_PAN_HASH).isNullOrEmpty()) null
            else payEvent.attributes.getStringAttribute(EventAttribute.EA_PAN_HASH_TYPE) ?: "SHA256",
            // Должен быть обязательно шаблон по которому была произведена операция списания
            templateId = UUID.fromString(payEvent.attributes.getStringAttribute(EventAttribute.EA_TEMPLATE_ID)),
            abonementId = payEvent.attributes.getLongAttribute(EventAttribute.EA_ABONEMENT_NUM)
        )

    fun convertShiftOpenOperation(
        shiftOpenEvent: TerminalEvent,
        routeSelectEvent: TerminalEvent,
        vehicleSelectEvent: TerminalEvent,
        terminalSerial: String,
        tid: String?,
        timeZoneName: String,
        terminalId: UUID,
        orgId: UUID,
        manifest: Manifest,
        projectId: UUID
    ): ShiftOperation {
        val routeId = routeSelectEvent.attributes.getStringAttribute(EventAttribute.EA_ROUTE_ID)
        val vehicleId = vehicleSelectEvent.attributes.getStringAttribute(EventAttribute.EA_VEHICLE_ID)
        return ShiftOperation(
            type = ShiftOperationType.OPEN,
            terminalSerial = terminalSerial,
            tid = tid,
            terminalId = terminalId,
            projectId = projectId,
            orgId = orgId,
            ern = shiftOpenEvent.attributes.getIntAttribute(EventAttribute.EA_ERN)!!,
            createdAt = DateTimeConverter.convertTimeToZonedDateTime(shiftOpenEvent.createdAt.time, timeZoneName),
            recordAt = ZonedDateTime.now(ZoneId.of("UTC")),
            shiftNumber = shiftOpenEvent.attributes.getLongAttribute(EventAttribute.EA_SHIFT_NUM)!!,
            userId = shiftOpenEvent.attributes.getStringAttribute(EventAttribute.EA_USER_ID)!!,
            manifest = shiftOpenEvent.attributes.getStringAttribute(EventAttribute.EA_MANIFEST_NUMBER),
            manifestVersion = shiftOpenEvent.attributes.getIntAttribute(EventAttribute.EA_MANIFEST_VER)!!,
            routeId = routeId,
            routeVersion = manifest.service.pro?.dict?.route?.first { it.id.toString() == routeId }?.version,
            vehicleId = vehicleId,
            vehicleVersion = manifest.service.pro?.dict?.transport?.first { it.id.toString() == vehicleId }?.version
        )
    }

    fun convertShiftCloseOperation(shiftCloseEvent: TerminalEvent, terminalSerial: String, tid: String?, timeZoneName: String, terminalId: UUID, orgId: UUID, projectId: UUID): ShiftOperation =
        ShiftOperation(
            type = ShiftOperationType.CLOSE,
            terminalSerial = terminalSerial,
            tid = tid,
            terminalId = terminalId,
            projectId = projectId,
            orgId = orgId,
            ern = shiftCloseEvent.attributes.getIntAttribute(EventAttribute.EA_ERN)!!,
            createdAt = DateTimeConverter.convertTimeToZonedDateTime(shiftCloseEvent.createdAt.time, timeZoneName),
            recordAt = ZonedDateTime.now(ZoneId.of("UTC")),
            shiftNumber = shiftCloseEvent.attributes.getLongAttribute(EventAttribute.EA_SHIFT_NUM)!!,
            userId = shiftCloseEvent.attributes.getStringAttribute(EventAttribute.EA_USER_ID)!!
        )
}