package ru.sbertroika.tms.processing

import java.time.Instant
import java.time.ZoneId
import java.time.ZonedDateTime

object DateTimeConverter {

    fun convertTimeToZonedDateTime(time: Long, timeZone: String): ZonedDateTime = Instant
        .ofEpochMilli(time / 1000)
        .atZone(ZoneId.of(timeZone))
        .toLocalDateTime()
        .atZone(ZoneId.of(timeZone))
        .withZoneSameInstant(ZoneId.of("UTC"))
}