package ru.sbertroika.tms.processing.consumer

import com.fasterxml.jackson.module.kotlin.readValue
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.kafka.clients.producer.ProducerRecord
import org.springframework.beans.factory.annotation.Value
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.kafka.core.ProducerFactory
import org.springframework.kafka.support.Acknowledgment
import org.springframework.stereotype.Component
import ru.sbertroika.common.tms.EventAttribute
import ru.sbertroika.common.tms.EventType
import ru.sbertroika.tkp3.manifest.starter.ManifestService
import ru.sbertroika.tkp3.pro.api.model.Operation
import ru.sbertroika.tkp3.pro.api.model.ShiftOperation
import ru.sbertroika.tms.model.Batch
import ru.sbertroika.tms.model.FailBatch
import ru.sbertroika.tms.model.FailEvent
import ru.sbertroika.tms.model.TerminalJournalEvent
import ru.sbertroika.tms.processing.*
import ru.sbertroika.tms.processing.repository.TerminalOrganizationRepository
import ru.sbertroika.tms.processing.repository.TerminalRepository
import java.util.*

@Component
class BatchProcessingConsumer(
    kafkaProducerFactory: ProducerFactory<String, Any>,
    @Value("\${spring.kafka.pro_operation_in_topic}")
    private val proOperationInTopic: String?,
    @Value("\${spring.kafka.qr_operation_in_topic}")
    private val qrOperationInTopic: String?,
    @Value("\${spring.kafka.pro_operation_shift_in_topic}")
    private val proOperationShiftInTopic: String?,
    @Value("\${spring.kafka.tms_journal_out_topic}")
    private val tmsJournalOutTopic: String?,
    @Value("\${spring.kafka.check_operation_in_topic}")
    private val checkOperationInTopic: String?,
    @Value("\${spring.kafka.tms_event_fail_out_topic}")
    private val tmsEventFailOutTopic: String?,
    @Value("\${spring.kafka.tms_batch_fail_out_topic}")
    private val tmsBatchFailOutTopic: String?,

    private val terminalRepository: TerminalRepository,
    private val terminalOrganizationRepository: TerminalOrganizationRepository,

    private val manifestService: ManifestService
) {
    private val mapper = mapper()
    private val producer = kafkaProducerFactory.createProducer()

    @KafkaListener(groupId = "\${spring.kafka.tms_batch_processing_in_group}", topics = ["\${spring.kafka.tms_batches_in_topic}"])
    fun filteredTransactionExporter(record: ConsumerRecord<String, String>, acknowledgment: Acknowledgment) = runBlocking {
        try {
            val batch = mapper.readValue<Batch>(record.value())

            val terminalId = terminalRepository.getTerminalId(batch.terminalSerial)
            val tid = terminalRepository.getTid(batch.terminalSerial)
            val timeZoneName = batch.timeZoneName.ifEmpty { "Europe/Moscow" }
            val projectId = terminalOrganizationRepository.findTerminalProjectId(terminalId)

            batch.events.forEach { event ->
                EventConverter.convertEvent(projectId.toString(), event, batch.terminalSerial, timeZoneName).fold(
                    { error ->
                        pushFailEvent(
                            FailEvent(
                                batchId = record.key(),
                                terminalSerial = batch.terminalSerial,
                                event = event,
                                exception = error
                            )
                        )
                    },
                    { journalEvent -> pushJournalEvent(journalEvent) }
                )
            }
            if (batch.tickets.isNotEmpty()) {
                // Оплата билетов производилась за наличные
                val cashPayEvent = batch.events.find { EventType.values()[it.type] == EventType.T_CASH_PAY }
                if (cashPayEvent != null) {
                    pushOperation(
                        OperationConverter.convertPassCashOperation(
                            tickets = batch.tickets,
                            payEvent = cashPayEvent,
                            terminalSerial = batch.terminalSerial,
                            terminalId = terminalId,
                            timeZoneName = timeZoneName
                        )
                    )
                    acknowledgment.acknowledge()
                    return@runBlocking
                }

                // Оплата билетов производилась по банковской карте
                val emvPayEvent = batch.events.find { EventType.values()[it.type] == EventType.T_EMV_PAY_SUCCESS }
                if (emvPayEvent != null) {
                    pushOperation(
                        OperationConverter.convertPassEmvOperation(
                            tickets = batch.tickets,
                            payEvent = emvPayEvent,
                            terminalSerial = batch.terminalSerial,
                            tid = tid,
                            terminalId = terminalId,
                            timeZoneName = timeZoneName
                        )
                    )
                    acknowledgment.acknowledge()
                    return@runBlocking
                }

                // Оплата билетов производилась по транспортной карте Тройка
                val troikaPayEvent = batch.events.find { EventType.values()[it.type] == EventType.T_TROIKA_WALLET_PAY_SUCCESS || EventType.values()[it.type] == EventType.T_TROIKA_TICKET_PAY_SUCCESS }
                if (troikaPayEvent != null) {
                    pushOperation(
                        OperationConverter.convertPassTroikaWalletOperation(
                            tickets = batch.tickets,
                            payEvent = troikaPayEvent,
                            terminalSerial = batch.terminalSerial,
                            tid = tid,
                            terminalId = terminalId,
                            timeZoneName = timeZoneName
                        )
                    )
                    acknowledgment.acknowledge()
                    return@runBlocking
                }

                // Оплата билетов производилась по ABT
                val abtPayEvent = batch.events.find { EventType.values()[it.type] == EventType.T_ABT_WALLET_PAY_SUCCESS || EventType.values()[it.type] == EventType.T_ABT_TICKET_PAY_SUCCESS }
                if (abtPayEvent != null) {
                    pushOperation(
                        OperationConverter.convertPassAbtOperation(
                            tickets = batch.tickets,
                            payEvent = abtPayEvent,
                            terminalSerial = batch.terminalSerial,
                            tid = tid,
                            terminalId = terminalId,
                            timeZoneName = timeZoneName
                        )
                    )
                    acknowledgment.acknowledge()
                    return@runBlocking
                }

                // Оплата билетов производилась виртуальной картой
                val qrPayEvent = batch.events.find { EventType.values()[it.type] == EventType.T_QR_WALLET_PAY_SUCCESS }
                if (qrPayEvent != null) {
                    pushQrOperation(
                        QrOperationConverter.convertPassQrOperation(
                            tickets = batch.tickets,
                            payEvent = qrPayEvent,
                            terminalSerial = batch.terminalSerial,
                            tid = tid,
                            terminalId = terminalId,
                            timeZoneName = timeZoneName
                        )
                    )
                    acknowledgment.acknowledge()
                    return@runBlocking
                }
            } else {
                batch.events.find { EventType.values()[it.type] == EventType.T_SHIFT_OPEN }?.let { shiftOpenEvent ->
                    val routeSelectEvent = batch.events.find { EventType.values()[it.type] == EventType.T_ROUTE_SELECT }
                    val vehicleSelectEvent = batch.events.find { EventType.values()[it.type] == EventType.T_VEHICLE_SELECT }

                    val manifestId = shiftOpenEvent.attributes.getStringAttribute(EventAttribute.EA_MANIFEST_NUMBER)
                    val manifestVer = shiftOpenEvent.attributes.getIntAttribute(EventAttribute.EA_MANIFEST_VER)

                    if (manifestId.isNullOrEmpty() || manifestVer == null) {
                        pushFailBatch(
                            FailBatch(
                                batchId = record.key(),
                                batch = record.value(),
                                exception = Error("Attribute EA_MANIFEST_NUMBER and EA_MANIFEST_VER require in T_SHIFT_OPEN event")
                            )
                        )
                        acknowledgment.acknowledge()
                        return@runBlocking
                    }

                    val manifest = manifestService.getManifest(manifestId, manifestVer).fold(
                        { error ->
                            pushFailEvent(
                                FailEvent(
                                    batchId = record.key(),
                                    terminalSerial = batch.terminalSerial,
                                    event = shiftOpenEvent,
                                    exception =
                                    Error(error)
                                )
                            )
                            acknowledgment.acknowledge()
                            return@runBlocking
                        },
                        { it }
                    )

                    if (routeSelectEvent != null && vehicleSelectEvent != null) {
                        pushShiftOperation(
                            OperationConverter.convertShiftOpenOperation(
                                shiftOpenEvent = shiftOpenEvent,
                                routeSelectEvent = routeSelectEvent,
                                vehicleSelectEvent = vehicleSelectEvent,
                                terminalSerial = batch.terminalSerial,
                                tid = tid,
                                terminalId = terminalId,
                                orgId = terminalOrganizationRepository.findTerminalOrgId(terminalId),
                                projectId = projectId,
                                timeZoneName = timeZoneName,
                                manifest = manifest
                            )
                        )
                    } else {
                        pushFailBatch(
                            FailBatch(
                                batchId = record.key(),
                                batch = record.value(),
                                exception = Error("T_ROUTE_SELECT and T_VEHICLE_SELECT require in T_SHIFT_OPEN batch")
                            )
                        )
                    }

                    acknowledgment.acknowledge()
                    return@runBlocking
                }

                batch.events.find { EventType.values()[it.type] == EventType.T_SHIFT_CLOSE }?.let { shiftCloseEvent ->
                    pushShiftOperation(
                        OperationConverter.convertShiftCloseOperation(
                            shiftCloseEvent = shiftCloseEvent,
                            terminalSerial = batch.terminalSerial,
                            tid = tid,
                            terminalId = terminalId,
                            timeZoneName = timeZoneName,
                            orgId = terminalOrganizationRepository.findTerminalOrgId(terminalId),
                            projectId = projectId
                        )
                    )
                }
            }
        } catch (e: Exception) {
            pushFailBatch(
                FailBatch(
                    batchId = record.key(),
                    batch = record.value(),
                    exception = Error(e)
                )
            )
        }
        acknowledgment.acknowledge()
    }

    private fun pushJournalEvent(event: TerminalJournalEvent) {
        val out = ProducerRecord<String, Any>(tmsJournalOutTopic, UUID.randomUUID().toString(), mapper.writeValueAsString(event))
        producer.send(out)
    }

    private fun pushFailEvent(event: FailEvent) {
        val out = ProducerRecord<String, Any>(tmsEventFailOutTopic, UUID.randomUUID().toString(), mapper.writeValueAsString(event))
        producer.send(out)
    }

    private fun pushFailBatch(batch: FailBatch) {
        val out = ProducerRecord<String, Any>(tmsBatchFailOutTopic, UUID.randomUUID().toString(), mapper.writeValueAsString(batch))
        producer.send(out)
    }

    private fun pushOperation(operation: Operation) {
        val first = operation.tickets.first()
        val oper = first.ticketNumber.split("-")
        val key = "${operation.terminalSerial}-${first.shiftNumber}-${operation.ern}-${first.ticketSeries}-${oper[0]}-${oper[1]}-${oper[2]}"
        val out = ProducerRecord<String, Any>(proOperationInTopic, key, mapper.writeValueAsString(operation))
        producer.send(out)
    }

    private fun pushQrOperation(operation: ru.sbertroika.tkp3.qr.api.model.Operation) {
        val first = operation.tickets.first()
        val oper = first.ticketNumber.split("-")
        val key = "${operation.terminalSerial}-${first.shiftNumber}-${operation.ern}-${first.ticketSeries}-${oper[0]}-${oper[1]}-${oper[2]}"
        val out = ProducerRecord<String, Any>(qrOperationInTopic, key, mapper.writeValueAsString(operation))
        producer.send(out)
    }

    private fun pushCheckOperation(operation: ru.sbertroika.tkp3.qr.api.model.Operation) {
        val key = "${operation.terminalSerial}-${operation.ern}"
        val out = ProducerRecord<String, Any>(checkOperationInTopic, key, mapper.writeValueAsString(operation))
        producer.send(out)
    }

    private fun pushShiftOperation(operation: ShiftOperation) {
        val out = ProducerRecord<String, Any>(proOperationShiftInTopic, UUID.randomUUID().toString(), mapper.writeValueAsString(operation))
        producer.send(out)
    }
}