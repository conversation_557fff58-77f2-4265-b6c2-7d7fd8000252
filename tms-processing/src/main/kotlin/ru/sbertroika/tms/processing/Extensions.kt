package ru.sbertroika.tms.processing

import ru.sbertroika.common.tms.EventAttribute

fun Map<String, String>.getStringAttribute(type: EventAttribute): String? = if (this.contains<PERSON><PERSON>(type.name)) this[type.name] else null

fun Map<String, String>.getIntAttribute(type: EventAttribute): Int? = this.getStringAttribute(type)?.toInt()

fun Map<String, String>.getLongAttribute(type: EventAttribute): Long? = this.getStringAttribute(type)?.toLong()