package ru.sbertroika.tms.processing.repository

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.r2dbc.repository.R2dbcRepository
import ru.sbertroika.tms.model.db.Terminal
import ru.sbertroika.tms.model.db.TerminalPK
import java.util.*

interface TerminalRepository : R2dbcRepository<Terminal, TerminalPK> {

    @Query("SELECT distinct on (t_serial_number) t_tid from terminal WHERE t_serial_number = :terminalSerial ORDER BY t_serial_number, t_version DESC")
    suspend fun getTid(terminalSerial: String): String?

    @Query("SELECT distinct on (t_serial_number) t_id from terminal WHERE t_serial_number = :terminalSerial ORDER BY t_serial_number, t_version DESC")
    suspend fun getTerminalId(terminalSerial: String): UUID
}