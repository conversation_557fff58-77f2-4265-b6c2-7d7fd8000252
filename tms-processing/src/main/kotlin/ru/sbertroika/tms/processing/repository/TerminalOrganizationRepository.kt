package ru.sbertroika.tms.processing.repository

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import ru.sbertroika.tms.model.db.TerminalOrganization
import ru.sbertroika.tms.model.db.TerminalOrganizationPK
import java.util.*

interface TerminalOrganizationRepository: CoroutineCrudRepository<TerminalOrganization, TerminalOrganizationPK> {

    @Query("SELECT distinct on (to_terminal_id) to_organization_id from terminal_organization WHERE to_terminal_id = :terminalId ORDER BY to_terminal_id, to_version DESC")
    suspend fun findTerminalOrgId(terminalId: UUID): UUID

    @Query("SELECT distinct on (to_terminal_id) to_project_id from terminal_organization WHERE to_terminal_id = :terminalId ORDER BY to_terminal_id, to_version DESC")
    suspend fun findTerminalProjectId(terminalId: UUID): UUID
}
