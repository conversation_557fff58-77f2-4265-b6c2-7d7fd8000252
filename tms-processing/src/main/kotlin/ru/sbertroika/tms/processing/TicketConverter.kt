package ru.sbertroika.tms.processing

import ru.sbertroika.tms.model.Ticket
import ru.sbertroika.tms.processing.DateTimeConverter.convertTimeToZonedDateTime

object TicketConverter {

    fun convertTicket(ticket: Ticket, timeZoneName: String): ru.sbertroika.tkp3.pro.api.model.Ticket =
        ru.sbertroika.tkp3.pro.api.model.Ticket(
            ticketSeries = ticket.ticketSeries,
            ticketNumber = ticket.ticketNumber,
            createdAt = convertTimeToZonedDateTime(ticket.createdAt.time, timeZoneName),
            manifest = ticket.manifest,
            manifestVersion = ticket.manifestVersion,
            shiftNumber = ticket.shiftNumber,
            serviceId = ticket.serviceId,
            tariffId = ticket.tariffId,
            productId = ticket.productId,
            amount = ticket.amount,
            stationToId = ticket.stationToId,
            stationFromId = ticket.stationFromId
        )
}