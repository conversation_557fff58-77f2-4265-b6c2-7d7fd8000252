package ru.sbertroika.tms.processing

import ru.sbertroika.common.tms.EventAttribute
import ru.sbertroika.tkp3.qr.api.model.Operation
import ru.sbertroika.tkp3.qr.api.model.OperationType
import ru.sbertroika.tms.model.TerminalEvent
import ru.sbertroika.tms.model.Ticket
import java.util.*

object QrOperationConverter {

    fun convertPassQrOperation(tickets: List<Ticket>, payEvent: TerminalEvent, terminalSerial: String, tid: String?, timeZoneName: String, terminalId: UUID): Operation =
        Operation(
            type = OperationType.PASS_QR_WALLET,
            terminalSerial = terminalSerial,
            terminalId = terminalId,
            ern = payEvent.attributes.getIntAttribute(EventAttribute.EA_ERN)!!,
            tickets = tickets.map { ticket -> QrTicketConverter.convertTicket(ticket, timeZoneName) }.toList(),
            cardNumber = payEvent.attributes.getStringAttribute(EventAttribute.EA_CARD_UID)
        )
}