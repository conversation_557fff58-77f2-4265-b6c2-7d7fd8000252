package ru.sbertroika.tms.processing

import ru.sbertroika.tms.model.Ticket

object QrTicketConverter {

    fun convertTicket(ticket: Ticket, timeZoneName: String): ru.sbertroika.tkp3.qr.api.model.Ticket =
        ru.sbertroika.tkp3.qr.api.model.Ticket(
            ticketSeries = ticket.ticketSeries,
            ticketNumber = ticket.ticketNumber,
            createdAt = DateTimeConverter.convertTimeToZonedDateTime(ticket.createdAt.time, timeZoneName),
            manifest = ticket.manifest,
            manifestVersion = ticket.manifestVersion,
            shiftNumber = ticket.shiftNumber,
            serviceId = ticket.serviceId,
            tariffId = ticket.tariffId,
            productId = ticket.productId,
            amount = ticket.amount,
            stationToId = ticket.stationToId,
            stationFromId = ticket.stationFromId
        )
}