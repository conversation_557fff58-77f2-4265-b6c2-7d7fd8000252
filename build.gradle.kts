plugins {
    kotlin("jvm") version libs.versions.kotlin.get() apply false
    id("net.researchgate.release") version libs.versions.releasePlugin.get()
    `maven-publish`
}

allprojects {
    repositories {
        maven {
            url = uri("https://nexus.sbertroika.tech/repository/maven-public/")
            credentials {
                username = providers.gradleProperty("mavenUser").get()
                password = providers.gradleProperty("mavenPassword").get()
            }
        }
        maven {
            url = uri("https://nexus.sbertroika.tech/repository/maven-releases/")
            credentials {
                username = providers.gradleProperty("mavenUser").get()
                password = providers.gradleProperty("mavenPassword").get()
            }
        }
        maven {
            url = uri("https://nexus.sbertroika.tech/repository/maven-snapshots/")
            credentials {
                username = providers.gradleProperty("mavenUser").get()
                password = providers.gradleProperty("mavenPassword").get()
            }
        }
    }
}

// Устанавливаем версию для всех проектов из корневого gradle.properties
allprojects {
    version = rootProject.version
}

//extra["version"] = version as String

release {
    failOnUnversionedFiles = false
    failOnUpdateNeeded = false
    failOnCommitNeeded = false

//    tagTemplate.set("v" + project.version.toString().removeSuffix("-SNAPSHOT"))

    versionPropertyFile.set("gradle.properties")
    git {
        requireBranch.set("master")
    }
}

// Задача для публикации в Nexus
tasks.register("publishToNexus") {
    dependsOn(":tms-api:publishToNexus")
    dependsOn(":tms-api-private:publishToNexus")
    dependsOn(":tms-common:publishToNexus")
    dependsOn(":tms-model:publishToNexus")
    group = "publishing"
    description = "Публикует все модули в Nexus"
}

// Настройка задач release
tasks.named("preTagCommit") {
    dependsOn("build")
    dependsOn("publishToNexus")
}
