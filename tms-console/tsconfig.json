{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "module": "ESNext", "moduleResolution": "Node", "strict": true, "jsx": "preserve", "jsxImportSource": "vue", "resolveJsonModule": true, "verbatimModuleSyntax": true, "esModuleInterop": true, "noImplicitAny": true, "lib": ["ESNext", "DOM"], "skipLibCheck": true, "noEmit": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "src/json/*.json"], "references": [{"path": "./tsconfig.node.json"}]}