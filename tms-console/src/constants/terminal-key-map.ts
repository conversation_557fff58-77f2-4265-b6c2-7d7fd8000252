import type { Terminal } from "@/service/__generated-api__/tms-gate-private";
import { KeyName, type EntityKeyNameMap } from "crm-core";

export const TERMINAL_KEY_MAP: EntityKeyNameMap<Terminal> = {
  id: KeyName.ID,
  serialNumber: "Серийный № терминала",
  title: "Название",
  status: KeyName.STATUS,
  versionPO: "Версия ПО",
  type: "Тип терминала",
  organizationId: "Организация",
  imei: "imei",
  wifiMac: "WiFi",
  bluetoothMac: "Bluetooth",
  ethernetMac: "Ethernet",
  tid: "TID",
};
