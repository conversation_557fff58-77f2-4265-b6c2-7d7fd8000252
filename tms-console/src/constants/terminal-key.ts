import type { Terminal } from "@/service/__generated-api__/tms-gate-private";
import type { KeysOfEntity } from "crm-core";

export const TERMINAL_KEY: KeysOfEntity<Terminal> = {
  id: "id",
  serialNumber: "serialNumber",
  title: "title",
  status: "status",
  versionPO: "versionPO",
  type: "type",
  organizationId: "organizationId",
  imei: "imei",
  wifiMac: "wifiMac",
  bluetoothMac: "bluetoothMac",
  ethernetMac: "ethernetMac",
  isActivated: "isActivated",
  tid: "tid",
};
