<script setup lang="tsx">
import { tmsGateApiService } from "@/service/api-instances/tms-gate-api-service";
import { JournalListFilter, TerminalJournalEvent } from "@/service/__generated-api__/tms-gate-private";
import { EventType } from "@/service/__generated-api__/common-tms";
import { Timestamp } from "@/service/__generated-api__/google/protobuf/timestamp";
import {
  Button,
  DEFAULT_FORMAT,
  FormatService,
  InputNumber,
  InputText,
  StCalendar,
  StFilterPanel,
  StMultiSelect,
  StTablePage,
  Table,
  useOneOfKindResult,
  useRouter,
  useTableSettings,
  useWatchOneOfKindResult,
} from "crm-core";
import type { StEventListEntity } from "@/entities/st-event-list-entity";
import { computed, reactive, ref } from "vue";
import { Empty } from "@/service/__generated-api__/google/protobuf/empty";
import { NavMenuLabel } from "@/constants/nav-menu-label";

const router = useRouter();
const routeState = router.options.history.state as StEventListEntity.RouteState;

const pageNum = ref(0);
const filter = reactive<JournalListFilter>({
  createdAtFrom: undefined,
  createdAtTo: undefined,
  ern: undefined,
  eventType: [],
  shiftNum: undefined,
  terminalSerial: "",
  userId: "",
});

const settings = useTableSettings("event-list", filter, pageNum);

if (routeState.terminalNum) {
  filter.terminalSerial = routeState.terminalNum;
  delete routeState.terminalNum;
}

const { data: eventTypeList, loading: eventTypeLoading } = useOneOfKindResult(tmsGateApiService.getEventTypeList(Empty));
const { data, loading } = useWatchOneOfKindResult(() =>
  tmsGateApiService.journalList({
    pagination: {
      limit: settings.rowsPerPage,
      page: pageNum.value,
    },
    filter: settings.filter,
  }),
);

const eventTypeMap = computed(() =>
  eventTypeList.value?.eventType.reduce(
    (res, val) => {
      res[val.type] = val.description;
      return res;
    },
    {} as Record<EventType, string>,
  ),
);

const COLUMNS: Table.Column<TerminalJournalEvent>[] = [
  {
    field: "eventType",
    header: "Тип события",
    formatter: (data) => eventTypeMap.value && eventTypeMap.value[data.eventType],
  },
  {
    field: "createdAt",
    header: "Дата",
    formatter: (data) => formatDateTime(data.createdAt),
  },
  {
    field: "terminalSerial",
    header: "Номер терминала",
    component(data) {
      return (
        <Button
          link
          onClick={() => searchByTerminal(data)}
        >
          {data.terminalSerial}
        </Button>
      );
    },
  },
  {
    field: "ern",
    header: "ЕРН",
  },
  {
    field: "userId",
    header: "Идентификатор пользователя",
  },
  {
    field: "shiftNum",
    header: "Номер смены",
  },
  {
    field: "stopListVersion",
    header: "Версия стоп-листа",
  },
  {
    field: "stopListUpdate",
    header: "Дата обновления стоп-листа",
    formatter: (data) => formatDateTime(data.stopListUpdate),
  },
  {
    field: "errorCode",
    header: "Код ошибки",
  },
  {
    field: "errorMessage",
    header: "Сообщение об ошибке",
  },
  {
    field: "value",
    header: "Значение в рамках события",
    component(data) {
      return (
        <textarea
          rows="5"
          cols="45"
          disabled
        >
          {data.value}
        </textarea>
      );
    },
  },
];

function searchByTerminal(data: TerminalJournalEvent) {
  filter.terminalSerial = data.terminalSerial;
  settings.applyFilter();
}

function formatDateTime(date?: Timestamp) {
  return FormatService.formatServerDate(date, DEFAULT_FORMAT.dateTime);
}
</script>

<template>
  <StTablePage
    :header="NavMenuLabel.EVENT_LIST"
    :columns="COLUMNS"
    :table-value="data?.event"
    :pagination="data?.pagination"
    v-model:page-num="pageNum"
    :table-settings="settings"
    :loading="loading || eventTypeLoading"
  >
    <template #filters>
      <StFilterPanel
        v-model:primary-value="filter.terminalSerial"
        placeholder="Номер терминала"
        :filter="filter"
        :saved-filter="settings.filter"
        primary-key="terminalSerial"
        @submit="settings.applyFilter"
      >
        <template #more>
          <StMultiSelect
            v-model="filter.eventType"
            :options="eventTypeList?.eventType"
            option-label="description"
            option-value="type"
            placeholder="Тип события"
            :maxSelectedLabels="1"
            filter
          />
          <div class="sidePanelDoubleCol">
            <StCalendar
              v-model:timestamp="filter.createdAtFrom"
              placeholder="Начальная дата"
              showTime
            />
            <StCalendar
              v-model:timestamp="filter.createdAtTo"
              placeholder="Конечная дата"
              showTime
            />
          </div>
          <InputText
            v-model.trim="filter.userId"
            placeholder="Идентификатор пользователя"
          />
          <div class="sidePanelDoubleCol">
            <InputNumber
              v-model.trim="filter.shiftNum"
              placeholder="Номер смены"
              :useGrouping="false"
            />
            <InputNumber
              v-model.trim="filter.ern"
              placeholder="ЕРН"
              :useGrouping="false"
            />
          </div>
        </template>
      </StFilterPanel>
    </template>
  </StTablePage>
</template>
