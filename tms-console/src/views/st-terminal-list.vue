<script setup lang="ts">
import { defineAsyncComponent, reactive, ref } from "vue";
import { tmsGateApiService } from "@/service/api-instances/tms-gate-api-service";
import { Terminal, TerminalListRequest, TerminalStatus } from "@/service/__generated-api__/tms-gate-private";
import { TERMINAL_STATUS_MAP } from "../constants/terminal-status-map";
import {
  type ApiFilter,
  ConvertService,
  StFilterPanel,
  StTablePage,
  useTableSettings,
  useWatchOneOfKindResult,
  StRemoveDialog,
  useRouter,
  DefaultNotification,
  Table,
  type MenuItem,
  Button,
  Dropdown,
  type EnumMap,
  StConfirmDialog,
  Notification,
} from "crm-core";
import { RouteName } from "@/constants/route-name";
import StEditTerminal from "@/components/st-edit-terminal.vue";
import { TERMINAL_KEY } from "@/constants/terminal-key";
import { TERMINAL_KEY_MAP } from "@/constants/terminal-key-map";
import { NavMenuLabel } from "@/constants/nav-menu-label";

enum Action {
  Block,
  Remove,
}

type Data = Terminal;

const ROW_STYLE_BY_STATUS: Partial<EnumMap<TerminalStatus>> = {
  [TerminalStatus.IS_DELETED]: "table__removedRow",
  [TerminalStatus.BLOCKED]: "table__blockedRow",
  [TerminalStatus.DISABLED]: "table__disabledRow",
};

const StOrgName = defineAsyncComponent(() => import("pasiv-console/st-org-name"));

const statusOptions = ConvertService.mapToOptions(TERMINAL_STATUS_MAP);

const router = useRouter();

const pageNum = ref(0);
const filter = reactive<ApiFilter<TerminalListRequest>>({
  organizationId: undefined,
  serialNumber: "",
  status: undefined,
});
const settings = useTableSettings("terminal-list", filter, pageNum);

const dialogAction = ref<Action>();
const dialogData = ref<Data>();
const editData = ref<Data>();
const showAdd = ref(false);

const { data, loading, update } = useWatchOneOfKindResult(() =>
  tmsGateApiService.terminalList({
    pagination: {
      limit: settings.rowsPerPage,
      page: pageNum.value,
    },
    ...settings.filter,
  }),
);

const COLUMNS: Table.Column<Data>[] = [
  {
    field: TERMINAL_KEY.serialNumber,
    header: TERMINAL_KEY_MAP.serialNumber,
  },
  {
    field: TERMINAL_KEY.title,
    header: TERMINAL_KEY_MAP.title,
  },
  {
    field: TERMINAL_KEY.status,
    header: TERMINAL_KEY_MAP.status,
    formatter: (data) => TERMINAL_STATUS_MAP[data.status],
  },
  {
    field: TERMINAL_KEY.versionPO,
    header: TERMINAL_KEY_MAP.versionPO,
  },
  {
    field: "type.name",
    header: TERMINAL_KEY_MAP.type,
  },
  {
    field: TERMINAL_KEY.imei,
    header: TERMINAL_KEY_MAP.imei,
  },
  {
    field: TERMINAL_KEY.wifiMac,
    header: TERMINAL_KEY_MAP.wifiMac,
  },
  {
    field: TERMINAL_KEY.bluetoothMac,
    header: TERMINAL_KEY_MAP.bluetoothMac,
  },
  {
    field: TERMINAL_KEY.ethernetMac,
    header: TERMINAL_KEY_MAP.ethernetMac,
  },
  {
    field: TERMINAL_KEY.tid,
    header: TERMINAL_KEY_MAP.tid,
  },
];

function blockExec() {
  return tmsGateApiService.blockTerminal({
    serialNumber: dialogData.value!.serialNumber,
    isBlock: isActivated(dialogData.value!),
  });
}

function removeTerminalExec() {
  return tmsGateApiService.deleteTerminal({
    serialNumber: dialogData.value!.serialNumber,
  });
}

function getBlockWord(data: Data, header: boolean = false) {
  let word = isActivated(data) ? "заблокировать" : "разблокировать";
  if (header) {
    word = word[0].toLocaleUpperCase() + word.slice(1);
  }
  return word;
}

function getBlockMessage(data: Data): Notification.Message {
  return isActivated(data)
    ? DefaultNotification.BLOCK
    : {
        content: getBlockWord(data),
      };
}

function onDialogClose() {
  dialogData.value = undefined;
  dialogAction.value = undefined;
}

function onDialogSuccess() {
  update();
  onDialogClose();
}

function onBlockClick(data: Data) {
  dialogAction.value = Action.Block;
  dialogData.value = data;
}

function onRemoveClick(data: Data) {
  dialogAction.value = Action.Remove;
  dialogData.value = data;
}

function onViewEventsClick(data: Data) {
  router.push({
    name: RouteName.EVENT_LIST,
    state: {
      terminalNum: data.serialNumber,
    },
  });
}

function isActivated(data: Data) {
  return data.isActivated;
}

function getRowActions(data: Data): MenuItem[] {
  return [
    {
      label: "Редактировать",
      command: () => (editData.value = data),
    },
    {
      label: "Журнал",
      command: () => onViewEventsClick(data),
    },
    {
      label: getBlockWord(data, true),
      command: () => onBlockClick(data),
    },
    {
      label: "Удалить",
      command: () => onRemoveClick(data),
    },
  ];
}

function getRowClass(data: Data) {
  return [isActivated(data) ? ROW_STYLE_BY_STATUS[data.status] : ROW_STYLE_BY_STATUS[TerminalStatus.BLOCKED]];
}
</script>

<template>
  <StTablePage
    :header="NavMenuLabel.TERMINAL_LIST"
    :columns="COLUMNS"
    :table-value="data?.terminal"
    :pagination="data?.pagination"
    v-model:page-num="pageNum"
    :table-settings="settings"
    :loading="loading"
    :row-class="getRowClass"
    :get-row-actions="getRowActions"
  >
    <template #filters>
      <StFilterPanel
        v-model:primary-value="filter.serialNumber"
        placeholder="Номер терминала"
        :filter="filter"
        :saved-filter="settings.filter"
        primary-key="serialNumber"
        @submit="settings.applyFilter"
      >
        <template #more>
          <StOrgName
            v-model:orgId="filter.organizationId"
            placeholder="Организация / группа"
            edit-mode
          />
          <Dropdown
            v-model="filter.status"
            :options="statusOptions"
            option-label="label"
            option-value="value"
            placeholder="Статус"
            show-clear
          />
        </template>
      </StFilterPanel>
    </template>
    <template #actions>
      <Button
        icon="pi pi-plus-circle"
        label="Добавить"
        @click="showAdd = true"
      />
    </template>
  </StTablePage>
  <StEditTerminal
    v-model:visible="showAdd"
    v-model:data="editData"
    @saved="update"
  />
  <StRemoveDialog
    v-if="dialogAction === Action.Remove"
    :name="dialogData!.title"
    @okActionFn="removeTerminalExec"
    @success="onDialogSuccess"
    @close="onDialogClose"
  />
  <StConfirmDialog
    v-if="dialogAction === Action.Block"
    :header="getBlockWord(dialogData!, true) + ' терминал'"
    :ok-action-fn="blockExec"
    :message="getBlockMessage(dialogData!)"
    @success="onDialogSuccess"
    @close="onDialogClose"
  >
    Вы уверены, что хотите {{ getBlockWord(dialogData!) }} {{ dialogData?.title }}?
  </StConfirmDialog>
</template>
