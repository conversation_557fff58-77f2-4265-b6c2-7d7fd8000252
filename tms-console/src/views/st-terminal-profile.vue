<script setup lang="ts">
import { SettingsGroup } from "@/service/__generated-api__/tms-gate-private-ts";
import { tmsGateTsApiService } from "@/service/api-instances/tms-gate-ts-api-service";
import {
  type IdRouteParams,
  StTablePage,
  useOneOfKindResult,
  useProcessUnaryCall,
  useRouteParams,
  useWatchOneOfKindResult,
  MultiSelect,
  Button,
  Column,
} from "crm-core";
import { computed, ref } from "vue";

const routeParams = useRouteParams<Required<IdRouteParams>>();

const { data: allGroups, loading: allGroupsLoading } = useOneOfKindResult(tmsGateTsApiService.settingsGroupList({}));
const { data, loading, update } = useWatchOneOfKindResult(() => tmsGateTsApiService.getSettingGroupForTerminalProfile(routeParams));
const { process, loading: addLoading } = useProcessUnaryCall();

const selectedGroups = ref<SettingsGroup[]>([]);

const allowedGroup = computed(() => {
  if (data.value?.settingsGroup.length) {
    return allGroups.value?.settingsGroup.filter((all) => data.value?.settingsGroup.every((g) => g.id !== all.id));
  }
  return allGroups.value?.settingsGroup;
});

async function onAddClick() {
  if (!selectedGroups.value.length) {
    return;
  }
  const settingsGroup = selectedGroups.value;
  selectedGroups.value = [];
  await process(
    tmsGateTsApiService.assignSettingGroupToTerminalProfile({
      terminalProfileId: routeParams.id,
      settingsGroup,
    }),
  );
  update();
}
</script>

<template>
  <StTablePage
    header="Группа настроек профиля"
    :table-value="data?.settingsGroup"
    :pagination="data?.pagination"
    :loading="loading"
  >
    <template #actions>
      <MultiSelect
        v-model="selectedGroups"
        :loading="allGroupsLoading"
        :options="allowedGroup"
        option-label="name"
        filter
        class="inputWidth"
      />
      <Button
        :disabled="!selectedGroups.length || addLoading"
        @click="onAddClick"
        >Добавить</Button
      >
    </template>
    <Column
      field="id"
      header="Идентификатор"
    />
    <Column
      field="name"
      header="Название"
    />
    <Column
      field="comment"
      header="Комментарий"
    />
  </StTablePage>
</template>
