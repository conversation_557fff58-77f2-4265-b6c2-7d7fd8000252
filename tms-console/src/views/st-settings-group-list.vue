<script setup lang="tsx">
import { ref } from "vue";
import { tmsGateTsApiService } from "@/service/api-instances/tms-gate-ts-api-service";
import { SettingsGroup } from "@/service/__generated-api__/tms-gate-private-ts";
import StEditSettingsGroup from "../components/st-edit-settings-group.vue";
import {
  Button,
  Column,
  DefaultNotification,
  StRemoveDialog,
  StTablePage,
  useProcessUnaryCall,
  useRouter,
  useTableSettings,
  useWatchOneOfKindResult,
  type MenuItem,
} from "crm-core";
import { RouteName } from "@/constants/route-name";
import { NavMenuLabel } from "@/constants/nav-menu-label";

type RowData = SettingsGroup;

const router = useRouter();

const pageNum = ref(0);
const settings = useTableSettings("settings-group-list");
const showAdd = ref(false);
const editData = ref<RowData>();
const removeData = ref<RowData>();

const { data, loading, update } = useWatchOneOfKindResult(() =>
  tmsGateTsApiService.settingsGroupList({
    pagination: {
      limit: settings.rowsPerPage,
      page: pageNum.value,
    },
  }),
);
const { process } = useProcessUnaryCall();

function onRemoveClick(data: RowData) {
  removeData.value = data;
}

function onDialogClose() {
  showAdd.value = false;
  editData.value = undefined;
  removeData.value = undefined;
}

function confirmRemove() {
  process(tmsGateTsApiService.deleteSettingsGroup({ id: removeData.value!.id! }), () => {
    update();
    onDialogClose();
    return DefaultNotification.REMOVE;
  });
}

function getRowActions(data: RowData): MenuItem[] {
  return [
    {
      label: "Редактировать",
      command: () => (editData.value = data),
    },
    {
      label: "Настроить",
      command: () => router.push({ name: RouteName.SETTINGS_GROUP, params: { id: data.id } }),
    },
    {
      label: "Удалить",
      command: () => (removeData.value = data),
    },
  ];
}
</script>

<template>
  <StTablePage
    :header="NavMenuLabel.SETTINGS_GROUP_LIST"
    :table-value="data?.settingsGroup"
    :pagination="data?.pagination"
    v-model:page-num="pageNum"
    :table-settings="settings"
    :loading="loading"
    :get-row-actions="getRowActions"
  >
    <template #actions>
      <Button
        icon="pi pi-plus-circle"
        label="Добавить"
        @click="showAdd = true"
      />
    </template>
    <Column
      field="name"
      header="Название"
    />
    <Column
      field="comment"
      header="Комментарий"
    />
  </StTablePage>
  <StEditSettingsGroup
    v-model:visible="showAdd"
    v-model:data="editData"
    @saved="update"
  />
  <StRemoveDialog
    v-if="removeData"
    :name="removeData.name"
    @ok="confirmRemove"
    @cancel="onDialogClose"
  />
</template>
