<script setup lang="ts">
import { tmsGateTsApiService } from "@/service/api-instances/tms-gate-ts-api-service";
import { TemplateSettings, TemplateSettingsType } from "@/service/__generated-api__/tms-gate-private-ts";
import { TEMPLATE_SETTINGS_TYPE_MAP } from "../constants/terminal-settings-type-map";
import {
  DefaultNotification,
  FormatService,
  StRemoveDialog,
  StTablePage,
  useProcessUnaryCall,
  useTableSettings,
  useWatchOneOfKindResult,
  StRouterLink,
  Button,
  Column,
  StInfButton,
  type MenuItem,
} from "crm-core";
import { RouteName } from "@/constants/route-name";
import { ref } from "vue";
import StEditTemplateSetting from "@/components/st-edit-template-setting.vue";
import { NavMenuLabel } from "@/constants/nav-menu-label";

type RowData = TemplateSettings;

const pageNum = ref(0);
const settings = useTableSettings("template-settings-list");
const showAdd = ref(false);
const editData = ref<RowData>();
const removeData = ref<RowData>();

const { data, loading, update } = useWatchOneOfKindResult(() =>
  tmsGateTsApiService.templateSettingsList({
    pagination: {
      limit: settings.rowsPerPage,
      page: pageNum.value,
    },
  }),
);
const { process } = useProcessUnaryCall();

function onRemoveClick(data: RowData) {
  removeData.value = data;
}

function onDialogClose() {
  removeData.value = undefined;
}

function confirmRemove() {
  process(tmsGateTsApiService.deleteTemplateSettings({ id: removeData.value!.id! }), () => {
    update();
    onDialogClose();
    return DefaultNotification.REMOVE;
  });
}

function getRowActions(data: RowData): MenuItem[] {
  return [
    {
      label: "Редактировать",
      command: () => (editData.value = data),
    },
    {
      label: "Удалить",
      command: () => (removeData.value = data),
    },
  ];
}
</script>

<template>
  <StTablePage
    :header="NavMenuLabel.TEMPLATE_SETTINGS_LIST"
    :table-value="data?.templateSettings"
    :pagination="data?.pagination"
    v-model:page-num="pageNum"
    :table-settings="settings"
    :loading="loading"
    :get-row-actions="getRowActions"
  >
    <template #actions>
      <Button
        icon="pi pi-plus-circle"
        label="Добавить"
        @click="showAdd = true"
      />
    </template>
    <Column
      field="name"
      header="Название"
    />
    <Column
      header="Псевдоним"
      field="slug"
    />
    <Column header="Тип">
      <template #body="{ data }">
        {{ TEMPLATE_SETTINGS_TYPE_MAP[data.type as TemplateSettingsType] }}
      </template>
    </Column>
    <Column header="Обязательное">
      <template #body="{ data }">
        {{ FormatService.bool(data.isRequired) }}
      </template>
    </Column>
    <Column
      header="Комментарий"
      field="comment"
    />
    <Column
      header="Валидный FN"
      field="validFN"
    />
    <Column
      header="Значение умолчанию"
      field="defaultValue"
    />
  </StTablePage>
  <StEditTemplateSetting
    v-model:visible="showAdd"
    v-model:data="editData"
    @saved="update"
  />
  <StRemoveDialog
    v-if="removeData"
    :name="removeData.name"
    @ok="confirmRemove"
    @cancel="onDialogClose"
  />
</template>
