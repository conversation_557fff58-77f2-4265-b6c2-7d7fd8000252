<script setup lang="ts">
import { tmsGateTsApiService } from "@/service/api-instances/tms-gate-ts-api-service";
import { Settings } from "@/service/__generated-api__/tms-gate-private-ts";
import StEditSettings from "../components/st-edit-settings.vue";
import {
  Button,
  Column,
  DefaultNotification,
  StRemoveDialog,
  StTablePage,
  useProcessUnaryCall,
  useTableSettings,
  useWatchOneOfKindResult,
  type MenuItem,
} from "crm-core";
import { ref } from "vue";

type RowData = Settings;

const pageNum = ref(0);
const settings = useTableSettings("settings-list");
const showAdd = ref(false);
const editData = ref<RowData>();
const removeData = ref<RowData>();

const { data, loading, update } = useWatchOneOfKindResult(() =>
  tmsGateTsApiService.settingsList({
    pagination: {
      limit: settings.rowsPerPage,
      page: pageNum.value,
    },
  }),
);
const { process } = useProcessUnaryCall();

function onDialogClose() {
  showAdd.value = false;
  editData.value = undefined;
  removeData.value = undefined;
}

function onConfirmRemove() {
  process(tmsGateTsApiService.deleteSettings({ id: removeData.value!.id! }), () => {
    update();
    onDialogClose();
    return DefaultNotification.REMOVE;
  });
}

function getRowActions(data: RowData): MenuItem[] {
  return [
    {
      label: "Редактировать",
      command: () => (editData.value = data),
    },
    {
      label: "Удалить",
      command: () => (removeData.value = data),
    },
  ];
}
</script>

<template>
  <StTablePage
    header="Настройки"
    :table-value="data?.settings"
    :pagination="data?.pagination"
    v-model:page-num="pageNum"
    :table-settings="settings"
    :loading="loading"
    :get-row-actions="getRowActions"
  >
    <template #actions>
      <Button
        icon="pi pi-plus-circle"
        label="Добавить"
        @click="showAdd = true"
      />
    </template>
    <Column
      field="name"
      header="Название"
    />
    <Column
      field="alias"
      header="Псевдоним"
    />
    <Column
      field="value"
      header="Значение"
    />
  </StTablePage>
  <StEditSettings
    v-model:visible="showAdd"
    v-model:data="editData"
    @saved="update"
  />
  <StRemoveDialog
    v-if="removeData"
    :name="removeData.name"
    @ok="onConfirmRemove"
    @cancel="onDialogClose"
  />
</template>
