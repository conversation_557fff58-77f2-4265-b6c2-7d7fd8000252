<script setup lang="ts">
import { TerminalUser, TerminalUserFilters } from "@/service/__generated-api__/tms-gate-private";
import { tmsGateApiService } from "@/service/api-instances/tms-gate-api-service";
import {
  Column,
  InputText,
  StDialog,
  StFilterPanel,
  StTablePage,
  useCachedResult,
  useProcessUnaryCall,
  useTableSettings,
  useWatchOneOfKindResult,
  type MenuItem,
  StSearchInput,
} from "crm-core";
import { TmsCacheName } from "../constants/tms-cache-name";
import { Empty } from "@/service/__generated-api__/google/protobuf/empty";
import { defineAsyncComponent, reactive, ref, watch } from "vue";
import StChangePassword from "@/components/st-change-password.vue";
import { NavMenuLabel } from "@/constants/nav-menu-label";

const StEmployeeName = defineAsyncComponent(() => import("pro-console/st-employee-name"));

type Data = TerminalUser;

const pageNum = ref(0);
const filter = reactive<TerminalUserFilters>({
  userId: [],
  login: "",
  surname: "",
  personalNumber: "",
});
const settings = useTableSettings("user-list", filter, pageNum);

const changePassUser = ref<Data>();
const blockUser = ref<Data>();

const { data: roleListResp } = useCachedResult(() => tmsGateApiService.roles(Empty), TmsCacheName.ROLES);

const { error, process } = useProcessUnaryCall();

const { data, loading, update } = useWatchOneOfKindResult(() =>
  tmsGateApiService.terminalUserList({
    pagination: {
      limit: settings.rowsPerPage,
      page: pageNum.value,
    },
    filters: settings.filter,
  }),
);

watch(
  () => filter.userId,
  () => settings.applyFilter(),
);

async function blockUserExec() {
  if (!blockUser.value) {
    return;
  }

  await process(
    tmsGateApiService.blockUser({
      userId: blockUser.value.userId,
      block: blockUser.value.enabled,
    }),
  );
  if (!error.value) {
    blockUser.value = undefined;
    update();
  }
}

function getRowClass(row: Data) {
  return row.enabled ? undefined : ["table__disabledRow"];
}

function getBlockUserWord(user: Data, header: boolean = false) {
  let word = user.enabled ? "заблокировать" : "разблокировать";
  if (header) {
    word = word[0].toLocaleUpperCase() + word.slice(1);
  }
  return word;
}

function getUserRoles(user: Data) {
  return user.roles?.map((v) => roleListResp.value?.roles.find((r) => r.name === v)?.description || v).join(", ");
}

function getRowActions(data: Data): MenuItem[] {
  return [
    {
      label: "Сменить пароль",
      command: () => (changePassUser.value = data),
    },
    {
      label: getBlockUserWord(data, true),
      command: () => (blockUser.value = data),
    },
  ];
}
</script>

<template>
  <StTablePage
    :header="NavMenuLabel.USER_LIST"
    :table-value="data?.user"
    :pagination="data?.pagination"
    v-model:page-num="pageNum"
    :table-settings="settings"
    :loading="loading"
    :row-class="getRowClass"
    :get-row-actions="getRowActions"
  >
    <template #filters>
      <StFilterPanel
        :filter="filter"
        :saved-filter="settings.filter"
        primary-key="userId"
        @submit="settings.applyFilter"
      >
        <StSearchInput>
          <StEmployeeName
            v-model:employee-id="filter.userId"
            placeholder="ФИО"
            multiple
          />
        </StSearchInput>
        <template #more>
          <InputText
            v-model.trim="filter.login"
            placeholder="Логин"
          />
          <InputText
            v-model.trim="filter.personalNumber"
            placeholder="Табельный номер"
          />
        </template>
      </StFilterPanel>
    </template>

    <Column
      header="Логин"
      field="login"
    />
    <Column
      header="Фамилия"
      field="surname"
    />
    <Column
      header="Имя"
      field="name"
    />
    <Column
      header="Отчество"
      field="middleName"
    />
    <Column
      header="Табельный номер"
      field="personalNumber"
    />
    <Column header="Роли">
      <template #body="{ data }">{{ getUserRoles(data) }}</template>
    </Column>
  </StTablePage>
  <StChangePassword v-model:data="changePassUser" />
  <StDialog
    v-if="blockUser"
    :header="getBlockUserWord(blockUser, true) + ' пользователя'"
    @ok="blockUserExec"
    @cancel="blockUser = undefined"
  >
    <h3>Вы уверены, что хотите {{ getBlockUserWord(blockUser) }} {{ blockUser.surname }} {{ blockUser.name }}?</h3>
  </StDialog>
</template>
