<script setup lang="ts">
import { tmsGateTsApiService } from "@/service/api-instances/tms-gate-ts-api-service";
import { TemplateSettings } from "@/service/__generated-api__/tms-gate-private-ts";
import {
  type IdRouteParams,
  StTablePage,
  useOneOfKindResult,
  useProcessUnaryCall,
  useRouteParams,
  useWatchOneOfKindResult,
  DefaultNotification,
  MultiSelect,
  Button,
  Column,
} from "crm-core";
import { computed, ref } from "vue";

const routeParams = useRouteParams<IdRouteParams>();

const { data: allSettings, loading: allSettingsLoading } = useOneOfKindResult(tmsGateTsApiService.templateSettingsList({}));
const { data, loading, update } = useWatchOneOfKindResult(() => tmsGateTsApiService.getTemplateSettingsForSettingGroup({ id: routeParams.id! }));
const { process } = useProcessUnaryCall();

const allowedSettings = computed(() => {
  const settings = data.value?.templateSettings;
  if (settings?.length) {
    return allSettings.value?.templateSettings.filter((all) => settings.every((g) => g.id !== all.id));
  }
  return allSettings.value?.templateSettings;
});

const selectedSettings = ref<TemplateSettings[]>([]);

function onAddClick() {
  if (!selectedSettings.value.length) {
    return;
  }
  const settings = selectedSettings.value;
  selectedSettings.value = [];
  process(
    tmsGateTsApiService.assignTemplateSettingsToSettingGroup({
      groupId: routeParams.id!,
      templateSettings: settings,
    }),
    () => {
      update();
      return DefaultNotification.SAVE;
    },
  );
}
</script>

<template>
  <StTablePage
    header="Настройки группы"
    :table-value="data?.templateSettings"
    :loading="loading"
  >
    <template #actions>
      <MultiSelect
        v-model="selectedSettings"
        :loading="allSettingsLoading"
        :options="allowedSettings"
        option-label="name"
        filter
      />
      <Button @click="onAddClick">Добавить</Button>
    </template>
    <Column
      field="id"
      header="Идентификатор"
    />
    <Column
      field="name"
      header="Название"
    />
    <Column
      field="alias"
      header="Псевдоним"
    />
    <Column
      field="value"
      header="Значение"
    />
  </StTablePage>
</template>
