<script setup lang="ts">
import { tmsGateApiService } from "@/service/api-instances/tms-gate-api-service";
import { Empty } from "@/service/__generated-api__/google/protobuf/empty";
import { Button, Column, StTablePage, useWatchOneOfKindResult, type MenuItem, StFilterPanel, useTableSettings } from "crm-core";
import { reactive, ref } from "vue";
import type { TerminalType, TerminalTypeFilters } from "@/service/__generated-api__/tms-gate-private";
import StEditTerminalType from "@/components/st-edit-terminal-type.vue";
import { NavMenuLabel } from "@/constants/nav-menu-label";

type Data = TerminalType;

const filter = reactive<TerminalTypeFilters>({
  name: "",
});
const settings = useTableSettings("terminal-type-list", filter);

const { data, loading, update } = useWatchOneOfKindResult(() =>
  tmsGateApiService.terminalTypeList({
    filters: settings.filter,
  }),
);

const showAdd = ref(false);
const editData = ref<Data>();

function getRowActions(data: Data): MenuItem[] {
  return [
    {
      label: "Редактировать",
      command: () => (editData.value = data),
    },
  ];
}
</script>

<template>
  <StTablePage
    :header="NavMenuLabel.TERMINAL_TYPE_LIST"
    :table-value="data?.terminalType"
    :loading="loading"
    :get-row-actions="getRowActions"
  >
    <template #filters>
      <StFilterPanel
        v-model:primary-value="filter.name"
        placeholder="Наименование типа"
        @submit="settings.applyFilter"
      />
    </template>
    <template #actions>
      <Button
        icon="pi pi-plus-circle"
        label="Добавить"
        @click="showAdd = true"
      />
    </template>
    <Column
      header="Наименование типа"
      field="name"
    />
    <Column
      header="Комментарий"
      field="comment"
    />
  </StTablePage>
  <StEditTerminalType
    v-model:visible="showAdd"
    v-model:data="editData"
    @saved="update"
  />
</template>
