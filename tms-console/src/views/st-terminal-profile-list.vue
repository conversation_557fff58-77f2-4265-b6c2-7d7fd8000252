<script setup lang="ts">
import { tmsGateTsApiService } from "@/service/api-instances/tms-gate-ts-api-service";
import { TerminalProfile, TerminalProfileStatus } from "@/service/__generated-api__/tms-gate-private-ts";
import StEditTerminalProfile from "../components/st-edit-terminal-profile.vue";
import { TERMINAL_PROFILE_STATUS_MAP } from "../constants/terminal-profile-status-map";
import {
  Button,
  Column,
  DefaultNotification,
  StRemoveDialog,
  StTablePage,
  useProcessUnaryCall,
  useRouter,
  useTableSettings,
  useWatchOneOfKindResult,
  type MenuItem,
} from "crm-core";
import { RouteName } from "@/constants/route-name";
import { ref } from "vue";
import { NavMenuLabel } from "@/constants/nav-menu-label";

type RowData = TerminalProfile;

const router = useRouter();

const pageNum = ref(0);
const settings = useTableSettings("terminal-profile-list");
const showAdd = ref(false);
const editData = ref<RowData>();
const removeData = ref<RowData>();

const { data, loading, update } = useWatchOneOfKindResult(() =>
  tmsGateTsApiService.terminalProfileList({
    pagination: {
      limit: settings.rowsPerPage,
      page: pageNum.value,
    },
  }),
);
const { process } = useProcessUnaryCall();

function onRemoveClick(data: RowData) {
  removeData.value = data;
}

function onDialogClose() {
  showAdd.value = false;
  editData.value = undefined;
  removeData.value = undefined;
}

function confirmRemove() {
  process(tmsGateTsApiService.deleteTerminalProfile({ id: removeData.value!.id! }), () => {
    update();
    onDialogClose();
    return DefaultNotification.REMOVE;
  });
}

function getRowActions(data: RowData): MenuItem[] {
  return [
    {
      label: "Редактировать",
      command: () => (editData.value = data),
    },
    {
      label: "Настроить",
      command: () => router.push({ name: RouteName.TERMINAL_PROFILE, params: { id: data.id } }),
    },
    {
      label: "Удалить",
      command: () => (removeData.value = data),
    },
  ];
}
</script>

<template>
  <StTablePage
    :header="NavMenuLabel.TERMINAL_PROFILE_LIST"
    :table-value="data?.terminalProfile"
    :pagination="data?.pagination"
    v-model:page-num="pageNum"
    :table-settings="settings"
    :loading="loading"
    :get-row-actions="getRowActions"
  >
    <template #actions>
      <Button
        icon="pi pi-plus-circle"
        label="Добавить"
        @click="showAdd = true"
      />
    </template>
    <Column
      field="name"
      header="Название"
    />
    <Column header="Статус">
      <template #body="{ data }">
        {{ TERMINAL_PROFILE_STATUS_MAP[data.status as TerminalProfileStatus] }}
      </template>
    </Column>
  </StTablePage>
  <StEditTerminalProfile
    v-model:visible="showAdd"
    v-model:data="editData"
    @saved="update"
  />
  <StRemoveDialog
    v-if="removeData"
    :name="removeData.name"
    @ok="confirmRemove"
    @cancel="onDialogClose"
  />
</template>
