import { AuthService, StNavEntities } from "crm-core";
import { RouteName } from "./constants/route-name";
import { AUTH_ROLE } from "./constants/auth-role";
import NavSvg from "@/assets/img/nav.svg";
import { NavMenuLabel } from "./constants/nav-menu-label";

export function getNavigationMenu(): Promise<StNavEntities.InjectionMenu> {
  return Promise.resolve([
    AuthService.hasRoleOrSuperAdmin(AUTH_ROLE) && {
      label: "TMS",
      key: "tms",
      svg: NavSvg,
      items: [
        {
          label: NavMenuLabel.USER_LIST,
          to: { name: RouteName.USER_LIST },
        },
        {
          label: NavMenuLabel.TERMINAL_LIST,
          to: { name: RouteName.TERMINAL_LIST },
        },
        {
          label: NavMenuLabel.TERMINAL_TYPE_LIST,
          to: { name: RouteName.TERMINAL_TYPE_LIST },
        },
        {
          label: NavMenuLabel.EVENT_LIST,
          to: { name: RouteName.EVENT_LIST },
        },
        {
          label: NavMenuLabel.SETTINGS_GROUP_LIST,
          to: { name: RouteName.SETTINGS_GROUP_LIST },
        },
        {
          label: NavMenuLabel.TERMINAL_PROFILE_LIST,
          to: { name: RouteName.TERMINAL_PROFILE_LIST },
        },
        {
          label: NavMenuLabel.TEMPLATE_SETTINGS_LIST,
          to: { name: RouteName.TEMPLATE_SETTINGS_LIST },
        },
      ],
    },
  ]);
}
