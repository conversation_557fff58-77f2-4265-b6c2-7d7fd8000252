import { RouterService, type RouteRecordSingleViewWithChildren } from "crm-core";
import StEventList from "@/views/st-event-list.vue";
import StSettingsList from "@/views/st-settings-list.vue";
import StSettingsGroupList from "@/views/st-settings-group-list.vue";
import StSettingsGroup from "@/views/st-settings-group.vue";
import StTerminalProfileList from "@/views/st-terminal-profile-list.vue";
import StTerminalProfile from "@/views/st-terminal-profile.vue";
import StUserList from "@/views/st-user-list.vue";
import StTerminalList from "@/views/st-terminal-list.vue";
import StTerminalTypeList from "@/views/st-terminal-type-list.vue";
import StTemplateSettingsList from "@/views/st-template-settings-list.vue";
import { RouteName } from "./constants/route-name";
import { AUTH_ROLE } from "./constants/auth-role";

export function getRootRoute(): Promise<RouteRecordSingleViewWithChildren> {
  return Promise.resolve({
    path: "/",
    children: [
      {
        path: "user-list",
        name: RouteName.USER_LIST,
        component: StUserList,
      },
      {
        path: "terminal-list",
        name: RouteName.TERMINAL_LIST,
        component: StTerminalList,
      },
      {
        path: "terminal-type-list",
        name: RouteName.TERMINAL_TYPE_LIST,
        component: StTerminalTypeList,
      },

      {
        path: "event-list",
        name: RouteName.EVENT_LIST,
        component: StEventList,
      },
      {
        path: "settings-list",
        name: RouteName.SETTINGS_LIST,
        component: StSettingsList,
      },

      {
        path: "settings-group-list",
        name: RouteName.SETTINGS_GROUP_LIST,
        component: StSettingsGroupList,
      },
      {
        path: "settings-group/:id",
        name: RouteName.SETTINGS_GROUP,
        component: StSettingsGroup,
      },

      {
        path: "terminal-profile-list",
        name: RouteName.TERMINAL_PROFILE_LIST,
        component: StTerminalProfileList,
      },
      {
        path: "terminal-profile/:id",
        name: RouteName.TERMINAL_PROFILE,
        component: StTerminalProfile,
      },

      {
        path: "template-settings-list",
        name: RouteName.TEMPLATE_SETTINGS_LIST,
        component: StTemplateSettingsList,
      },
    ],
    beforeEnter: RouterService.checkRole(AUTH_ROLE),
  });
}
