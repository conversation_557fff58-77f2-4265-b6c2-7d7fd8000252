<script setup lang="ts">
import { SettingsGroup } from "@/service/__generated-api__/tms-gate-private-ts";
import { tmsGateTsApiService } from "@/service/api-instances/tms-gate-ts-api-service";
import {
  type KeysOfEntity,
  StFormField,
  StValidationInputText,
  z,
  type StZodType,
  zRequiredString,
  _,
  type StEditSidePanelEntity,
  useStEditSidePanel,
  StEditSidePanel,
} from "crm-core";

type Data = SettingsGroup;

const props = defineProps<StEditSidePanelEntity.ParentDataProps<Data>>();
const emit = defineEmits<StEditSidePanelEntity.ParentEmits>();

const { visible, onClose, onSaved } = useStEditSidePanel(props, emit);

const schemeProps: KeysOfEntity<Data> = {
  id: "id",
  name: "name",
  comment: "comment",
  isDeleted: "isDeleted",
};

const validationSchema: StZodType<Data> = z.object({
  [schemeProps.id]: z.string().optional(),
  [schemeProps.name]: zRequiredString(),
  [schemeProps.comment]: z.string().optional(),
});

const saveFn = (values: Data) => (props.data ? tmsGateTsApiService.updateSettingsGroup(values) : tmsGateTsApiService.createSettingsGroup(values));
</script>

<template>
  <StEditSidePanel
    :header="data ? data.name : 'Добавить группу'"
    :save-fn="saveFn"
    :validation-schema="validationSchema"
    :data="data"
    :visible="visible"
    @saved="onSaved"
    @update:visible="onClose"
  >
    <StFormField label="Название">
      <StValidationInputText :name="schemeProps.name" />
    </StFormField>

    <StFormField label="Комментарий">
      <StValidationInputText :name="schemeProps.comment" />
    </StFormField>
  </StEditSidePanel>
</template>
