<script setup lang="ts">
import { TerminalProfile, TerminalProfileStatus } from "@/service/__generated-api__/tms-gate-private-ts";
import { tmsGateTsApiService } from "@/service/api-instances/tms-gate-ts-api-service";
import { TERMINAL_PROFILE_STATUS_MAP } from "../constants/terminal-profile-status-map";
import {
  ConvertService,
  type KeysOfEntity,
  StFormField,
  StValidationDropdown,
  StValidationInputText,
  z,
  type StZodType,
  zRequiredString,
  _,
  type StEditSidePanelEntity,
  useStEditSidePanel,
  StEditSidePanel,
} from "crm-core";

type Data = TerminalProfile;

const props = defineProps<StEditSidePanelEntity.ParentDataProps<Data>>();
const emit = defineEmits<StEditSidePanelEntity.ParentEmits>();

const { visible, onClose, onSaved } = useStEditSidePanel(props, emit);

const schemeProps: KeysOfEntity<Data> = {
  id: "id",
  name: "name",
  activeFrom: "activeFrom",
  activeTill: "activeTill",
  status: "status",
};

const validationSchema: StZodType<Data> = z.object({
  [schemeProps.id]: z.string().optional(),
  [schemeProps.name]: zRequiredString(),
  [schemeProps.activeFrom]: z.any().optional(),
  [schemeProps.activeTill]: z.any().optional(),
  [schemeProps.status]: z.nativeEnum(TerminalProfileStatus),
});

const statusOptions = ConvertService.mapToOptions(TERMINAL_PROFILE_STATUS_MAP);

const saveFn = (values: Data) => (props.data ? tmsGateTsApiService.updateTerminalProfile(values) : tmsGateTsApiService.createTerminalProfile(values));
</script>

<template>
  <StEditSidePanel
    :header="data ? data.name : 'Добавить профиль'"
    :save-fn="saveFn"
    :data="data"
    :validation-schema="validationSchema"
    :visible="visible"
    @saved="onSaved"
    @update:visible="onClose"
  >
    <StFormField label="Название">
      <StValidationInputText :name="schemeProps.name" />
    </StFormField>

    <StFormField label="Status">
      <StValidationDropdown
        :name="schemeProps.status"
        :options="statusOptions"
        default-option-props
      />
    </StFormField>
  </StEditSidePanel>
</template>
