<script setup lang="ts">
import { TerminalType } from "@/service/__generated-api__/tms-gate-private";
import { tmsGateApiService } from "@/service/api-instances/tms-gate-api-service";
import {
  type KeysOfEntity,
  StValidationInputText,
  type StZodType,
  z,
  StEditSidePanel,
  StFormField,
  type StEditSidePanelEntity,
  useStEditSidePanel,
} from "crm-core";

type Data = TerminalType;

const props = defineProps<StEditSidePanelEntity.ParentDataProps<TerminalType>>();
const emit = defineEmits<StEditSidePanelEntity.ParentEmits>();

const { visible, onClose, onSaved } = useStEditSidePanel(props, emit);

const schemeProps: KeysOfEntity<Data> = {
  id: "id",
  name: "name",
  slug: "slug",
  comment: "comment",
};

const validationSchema: StZodType<Data> = z.object({
  [schemeProps.id]: z.string().optional(),
  [schemeProps.name]: z.string(),
  [schemeProps.slug]: z.string().default(""),
  [schemeProps.comment]: z.string().default(""),
});

const saveFn = (value: Data) => (props.data ? tmsGateApiService.updateTerminalType(value) : tmsGateApiService.createTerminalType(value));
</script>

<template>
  <StEditSidePanel
    :header="data ? data?.name : 'Добавить тип терминала'"
    :validationSchema="validationSchema"
    :data="data"
    :save-fn="saveFn"
    :visible="visible"
    @update:visible="onClose"
    @saved="onSaved"
  >
    <StFormField label="Наименование типа">
      <StValidationInputText :name="schemeProps.name" />
    </StFormField>

    <StFormField label="Комментарий">
      <StValidationInputText :name="schemeProps.comment" />
    </StFormField>
  </StEditSidePanel>
</template>
