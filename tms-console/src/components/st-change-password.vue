<script setup lang="ts">
import type { UserSidePanelEntity } from "@/entities/user-side-panel-entity";
import { ChangePasswordRequest } from "@/service/__generated-api__/tms-gate-private";
import { tmsGateApiService } from "@/service/api-instances/tms-gate-api-service";
import { StFormField, StValidationInputText, type KeysOfEntity, z, type StZodType, StEditSidePanel, StSidePanelSection } from "crm-core";

type Data = Omit<ChangePasswordRequest, "userId">;

const props = defineProps<UserSidePanelEntity.Props>();
defineEmits<UserSidePanelEntity.Emits>();

const schemeProps: KeysOfEntity<Data> = {
  newPassword: "newPassword",
  confirmPassword: "confirmPassword",
};

const validationSchema: StZodType<Data> = z
  .object({
    [schemeProps.newPassword]: z.string(),
    [schemeProps.confirmPassword]: z.string(),
  })
  .refine((value) => value[schemeProps.newPassword] === value[schemeProps.confirmPassword], {
    message: "пароли не совпадают",
    path: [schemeProps.confirmPassword],
  });

function onSave(values: Data) {
  return tmsGateApiService.changePasswordUser({
    userId: props.data!.userId,
    ...values,
  });
}
</script>

<template>
  <StEditSidePanel
    header="Смена пароля"
    :save-fn="onSave"
    :validation-schema="validationSchema"
    :visible="!!data"
    @update:visible="$emit('update:data')"
  >
    <StSidePanelSection :header="`${data?.surname} ${data?.name}`">
      <StFormField label="Новый пароль">
        <StValidationInputText
          :name="schemeProps.newPassword"
          password
        />
      </StFormField>

      <StFormField label="Подтверждение пароля">
        <StValidationInputText
          :name="schemeProps.confirmPassword"
          password
        />
      </StFormField>
    </StSidePanelSection>
  </StEditSidePanel>
</template>
