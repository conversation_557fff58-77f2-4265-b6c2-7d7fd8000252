<script setup lang="ts">
import { Settings } from "@/service/__generated-api__/tms-gate-private-ts";
import { tmsGateTsApiService } from "@/service/api-instances/tms-gate-ts-api-service";
import {
  type KeysOfEntity,
  StEditDialog,
  StFormField,
  StValidationInputText,
  z,
  zRequiredString,
  _,
  toTypedSchema,
  type StZodType,
  useForm,
  type StEditSidePanelEntity,
  StEditSidePanel,
  useStEditSidePanel,
} from "crm-core";

type Data = Settings;

const props = defineProps<StEditSidePanelEntity.ParentDataProps<Data>>();
const emit = defineEmits<StEditSidePanelEntity.ParentEmits>();

const { visible, onClose, onSaved } = useStEditSidePanel(props, emit);

const schemeProps: KeysOfEntity<Data> = {
  id: "id",
  name: "name",
  alias: "alias",
  value: "value",
  isDeleted: "isDeleted",
};

const validationSchema: StZodType<Data> = z.object({
  [schemeProps.id]: z.string().optional(),
  [schemeProps.name]: zRequiredString(),
  [schemeProps.alias]: zRequiredString(),
  [schemeProps.value]: zRequiredString(),
});

const saveFn = (values: Data) => (props.data ? tmsGateTsApiService.updateSettings(values) : tmsGateTsApiService.createSettings(values));
</script>

<template>
  <StEditSidePanel
    :header="data ? data.name : 'Добавить настройки'"
    :save-fn="saveFn"
    :data="data"
    :validation-schema="validationSchema"
    :visible="visible"
    @saved="onSaved"
    @update:visible="onClose"
  >
    <StFormField label="Название">
      <StValidationInputText :name="schemeProps.name" />
    </StFormField>

    <StFormField label="Псевдоним">
      <StValidationInputText :name="schemeProps.alias" />
    </StFormField>

    <StFormField label="Значение">
      <StValidationInputText :name="schemeProps.value" />
    </StFormField>
  </StEditSidePanel>
</template>
