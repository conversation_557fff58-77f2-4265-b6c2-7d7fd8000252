<script setup lang="tsx">
import { Terminal, TerminalRequest, TerminalStatus } from "@/service/__generated-api__/tms-gate-private";
import { tmsGateApiService } from "@/service/api-instances/tms-gate-api-service";
import {
  type KeysOfEntity,
  StValidationDropdown,
  StValidationInputText,
  useOneOfKindResult,
  useRunIfSingle,
  z,
  toTypedSchema,
  type StZodType,
  useForm,
  _,
  StEditSidePanel,
  type StEditSidePanelEntity,
  StFormField,
  useStEditSidePanel,
} from "crm-core";
import { TERMINAL_STATUS_MAP } from "../constants/terminal-status-map";
import { Empty } from "@/service/__generated-api__/google/protobuf/empty";
import { defineAsyncComponent, watchEffect } from "vue";
import StValidationMacAddress from "@/components/st-validation-mac-address.vue";
import { TERMINAL_KEY_MAP } from "@/constants/terminal-key-map";

type Data = TerminalRequest;

const StOrgName = defineAsyncComponent(() => import("pasiv-console/st-org-name"));
const StProjectName = defineAsyncComponent(() => import("crm-console/st-project-name"));

const props = defineProps<StEditSidePanelEntity.ParentDataProps<Terminal>>();
const emit = defineEmits<StEditSidePanelEntity.ParentEmits>();

const { visible, onClose, onSaved } = useStEditSidePanel(props, emit);

const { data: typeList } = useOneOfKindResult(tmsGateApiService.terminalTypeList(Empty));

const schemeProps: KeysOfEntity<Data> = {
  serialNumber: "serialNumber",
  title: "title",
  typeId: "typeId",
  organizationId: "organizationId",
  wifiMac: "wifiMac",
  bluetoothMac: "bluetoothMac",
  ethernetMac: "ethernetMac",
  status: "status",
  tid: "tid",
  projectId: "projectId",
};

const MAC_SCHEMA = z.string().regex(/[0-9a-f]{12}/i);

const formCtx = useForm<Data>({
  validationSchema: toTypedSchema<StZodType<Data>>(
    z.object({
      [schemeProps.serialNumber]: z.string(),
      [schemeProps.title]: z.string(),
      [schemeProps.typeId]: z.string(),
      [schemeProps.organizationId]: z.string(),
      [schemeProps.projectId]: z.string(),
      [schemeProps.wifiMac]: MAC_SCHEMA.optional(),
      [schemeProps.bluetoothMac]: MAC_SCHEMA.optional(),
      [schemeProps.ethernetMac]: MAC_SCHEMA.optional(),
      [schemeProps.tid]: z.string().optional(),
      [schemeProps.status]: z.nativeEnum(TerminalStatus).optional(),
    }),
  ),
});
const { values, setFieldValue, setValues } = formCtx;

useRunIfSingle(
  () => typeList?.value?.terminalType,
  (v) => setFieldValue(schemeProps.typeId, v.id!),
);

watchEffect(() =>
  setValues({
    ..._.cloneDeep(_.omit(props.data, "type")),
    typeId: props.data?.type?.id,
  }),
);

function saveFn() {
  return props.data ? tmsGateApiService.updateTerminal(values) : tmsGateApiService.createTerminal(values);
}
</script>

<template>
  <StEditSidePanel
    :header="data ? data?.serialNumber : 'Добавить терминал'"
    :form-context="formCtx"
    :save-fn="saveFn"
    :visible="visible"
    @saved="onSaved"
    @update:visible="onClose"
  >
    <StFormField :label="TERMINAL_KEY_MAP.serialNumber">
      <StValidationInputText
        :name="schemeProps.serialNumber"
        :disabled="!!props.data"
      />
    </StFormField>

    <StFormField :label="TERMINAL_KEY_MAP.title">
      <StValidationInputText :name="schemeProps.title" />
    </StFormField>

    <StFormField :label="TERMINAL_KEY_MAP.type">
      <StValidationDropdown
        :name="schemeProps.typeId"
        :options="typeList?.terminalType"
        optionLabel="name"
        optionValue="id"
      />
    </StFormField>

    <StFormField :label="TERMINAL_KEY_MAP.organizationId">
      <StOrgName
        :fieldName="schemeProps.organizationId"
        edit-mode
      />
    </StFormField>

    <StFormField label="Проект">
      <StProjectName
        :fieldName="schemeProps.projectId"
        edit-mode
      />
    </StFormField>

    <div class="sidePanelDoubleCol">
      <StFormField :label="TERMINAL_KEY_MAP.wifiMac">
        <StValidationMacAddress :name="schemeProps.wifiMac" />
      </StFormField>
      <StFormField :label="TERMINAL_KEY_MAP.bluetoothMac">
        <StValidationMacAddress :name="schemeProps.bluetoothMac" />
      </StFormField>
      <StFormField :label="TERMINAL_KEY_MAP.ethernetMac">
        <StValidationMacAddress :name="schemeProps.ethernetMac" />
      </StFormField>
      <StFormField :label="TERMINAL_KEY_MAP.tid">
        <StValidationInputText :name="schemeProps.tid" />
      </StFormField>
      <StFormField
        v-if="data"
        :label="TERMINAL_KEY_MAP.versionPO"
      >
        {{ data?.versionPO }}
      </StFormField>
      <StFormField
        v-if="data"
        :label="TERMINAL_KEY_MAP.status"
      >
        {{ TERMINAL_STATUS_MAP[data?.status!] }}
      </StFormField>
    </div>
  </StEditSidePanel>
</template>
