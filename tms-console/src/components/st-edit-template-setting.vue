<script setup lang="tsx">
import { TemplateSettings, TemplateSettingsType } from "@/service/__generated-api__/tms-gate-private-ts";
import { tmsGateTsApiService } from "@/service/api-instances/tms-gate-ts-api-service";
import { TEMPLATE_SETTINGS_TYPE_MAP } from "../constants/terminal-settings-type-map";
import {
  ConvertService,
  type KeysOfEntity,
  StValidationCheckbox,
  StValidationDropdown,
  StValidationInputText,
  toTypedSchema,
  z,
  zRequiredString,
  type StZodType,
  useForm,
  _,
  type StEditSidePanelEntity,
  useStEditSidePanel,
  StEditSidePanel,
  StFormField,
} from "crm-core";
import { computed, watchEffect } from "vue";

type Data = TemplateSettings;

const props = defineProps<StEditSidePanelEntity.ParentDataProps<Data>>();
const emit = defineEmits<StEditSidePanelEntity.ParentEmits>();

const { visible, onClose, onSaved } = useStEditSidePanel(props, emit);

const schemeProps: KeysOfEntity<Data> = {
  id: "id",
  name: "name",
  comment: "comment",
  defaultValue: "defaultValue",
  isRequired: "isRequired",
  slug: "slug",
  type: "type",
  validFN: "validFN",
  isDeleted: "isDeleted",
};

const formCtx = useForm<Data>({
  validationSchema: toTypedSchema<StZodType<Data>>(
    z.object({
      [schemeProps.id]: z.string().optional(),
      [schemeProps.name]: z.string(),
      [schemeProps.slug]: z.string(),
      [schemeProps.type]: z.nativeEnum(TemplateSettingsType),
      [schemeProps.isRequired]: z.boolean().default(false),
      [schemeProps.comment]: z.string().optional(),
      [schemeProps.defaultValue]: z
        .string()
        .refine(
          (val) => {
            if (
              formCtx.values.type == null ||
              formCtx.values.type === TemplateSettingsType.BOOLEAN ||
              formCtx.values.type === TemplateSettingsType.STRING
            ) {
              return true;
            }
            let schema = z.number();
            if (formCtx.values.type === TemplateSettingsType.UINT) {
              schema = schema.positive();
            }
            return schema.parse(Number(val));
          },
          {
            message: "не соответствует выбранному типу",
          },
        )
        .optional(),
      [schemeProps.validFN]: z.string().optional(),
    }),
  ),
});

watchEffect(() => props.data && formCtx.setValues(_.cloneDeep(props.data)));

const typeOptions = ConvertService.mapToOptions(TEMPLATE_SETTINGS_TYPE_MAP);

const isBoolType = computed(() => formCtx.values.type === TemplateSettingsType.BOOLEAN);

const saveFn = (value: Data) => (value.id ? tmsGateTsApiService.updateTemplateSettings(value) : tmsGateTsApiService.createTemplateSettings(value));
</script>

<template>
  <StEditSidePanel
    :header="data ? data.name : 'Добавить настройки'"
    :form-context="formCtx"
    :save-fn="saveFn"
    :visible="visible"
    @saved="onSaved"
    @update:visible="onClose"
  >
    <StFormField label="Название">
      <StValidationInputText :name="schemeProps.name" />
    </StFormField>
    <StFormField label="Тип">
      <StValidationDropdown
        :name="schemeProps.type"
        :options="typeOptions"
        default-option-props
      />
    </StFormField>
    <StFormField label="Псевдоним">
      <StValidationInputText :name="schemeProps.slug" />
    </StFormField>
    <StFormField label="Комментарий">
      <StValidationInputText :name="schemeProps.comment" />
    </StFormField>
    <StFormField label="Валидный FN">
      <StValidationInputText :name="schemeProps.validFN" />
    </StFormField>
    <StFormField label="Значение по умолчанию">
      <StValidationCheckbox
        v-if="isBoolType"
        :name="schemeProps.defaultValue"
      />
      <StValidationInputText
        v-else
        :name="schemeProps.defaultValue"
      />
    </StFormField>
    <StFormField label="Обязательное поле">
      <StValidationCheckbox :name="schemeProps.isRequired" />
    </StFormField>
  </StEditSidePanel>
</template>
