// @generated by protobuf-ts 2.9.3
// @generated from protobuf file "tms-gate-private-ts.proto" (package "ru.sbertroika.tms.gate.v1", syntax proto3)
// tslint:disable
import { ByIdRequest } from "./common";
import { EmptyResponse } from "./common";
import { ServiceType } from "@protobuf-ts/runtime-rpc";
import type { BinaryWriteOptions } from "@protobuf-ts/runtime";
import type { IBinaryWriter } from "@protobuf-ts/runtime";
import { WireType } from "@protobuf-ts/runtime";
import type { BinaryReadOptions } from "@protobuf-ts/runtime";
import type { IB<PERSON>ryReader } from "@protobuf-ts/runtime";
import { UnknownFieldHandler } from "@protobuf-ts/runtime";
import type { PartialMessage } from "@protobuf-ts/runtime";
import { reflectionMergePartial } from "@protobuf-ts/runtime";
import { MessageType } from "@protobuf-ts/runtime";
import { Timestamp } from "./google/protobuf/timestamp";
import { PaginationResponse } from "./common";
import { OperationError } from "./common";
import { PaginationRequest } from "./common";
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.SettingsListRequest
 */
export interface SettingsListRequest {
    /**
     * @generated from protobuf field: optional ru.sbertroika.common.v1.PaginationRequest pagination = 1;
     */
    pagination?: PaginationRequest;
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.SettingsGroupListRequest
 */
export interface SettingsGroupListRequest {
    /**
     * @generated from protobuf field: optional ru.sbertroika.common.v1.PaginationRequest pagination = 1;
     */
    pagination?: PaginationRequest;
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.TerminalProfileListRequest
 */
export interface TerminalProfileListRequest {
    /**
     * @generated from protobuf field: optional ru.sbertroika.common.v1.PaginationRequest pagination = 1;
     */
    pagination?: PaginationRequest;
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.TemplateSettingsListRequest
 */
export interface TemplateSettingsListRequest {
    /**
     * @generated from protobuf field: optional ru.sbertroika.common.v1.PaginationRequest pagination = 1;
     */
    pagination?: PaginationRequest;
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.AssignTemplateSettingsToSettingGroupRequest
 */
export interface AssignTemplateSettingsToSettingGroupRequest {
    /**
     * @generated from protobuf field: repeated ru.sbertroika.tms.gate.v1.TemplateSettings templateSettings = 1;
     */
    templateSettings: TemplateSettings[];
    /**
     * @generated from protobuf field: string groupId = 2;
     */
    groupId: string;
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.AssignSettingsToTerminalProfileRequest
 */
export interface AssignSettingsToTerminalProfileRequest {
    /**
     * @generated from protobuf field: repeated ru.sbertroika.tms.gate.v1.Settings settings = 1;
     */
    settings: Settings[];
    /**
     * @generated from protobuf field: string terminalProfileId = 2;
     */
    terminalProfileId: string;
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.AssignSettingGroupToTerminalProfileRequest
 */
export interface AssignSettingGroupToTerminalProfileRequest {
    /**
     * @generated from protobuf field: repeated ru.sbertroika.tms.gate.v1.SettingsGroup settingsGroup = 1;
     */
    settingsGroup: SettingsGroup[];
    /**
     * @generated from protobuf field: string terminalProfileId = 2;
     */
    terminalProfileId: string;
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.SettingsListResponse
 */
export interface SettingsListResponse {
    /**
     * @generated from protobuf oneof: response
     */
    response: {
        oneofKind: "error";
        /**
         * @generated from protobuf field: ru.sbertroika.common.v1.OperationError error = 1;
         */
        error: OperationError;
    } | {
        oneofKind: "result";
        /**
         * @generated from protobuf field: ru.sbertroika.tms.gate.v1.SettingsList result = 2;
         */
        result: SettingsList;
    } | {
        oneofKind: undefined;
    };
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.SettingsGroupListResponse
 */
export interface SettingsGroupListResponse {
    /**
     * @generated from protobuf oneof: response
     */
    response: {
        oneofKind: "error";
        /**
         * @generated from protobuf field: ru.sbertroika.common.v1.OperationError error = 1;
         */
        error: OperationError;
    } | {
        oneofKind: "result";
        /**
         * @generated from protobuf field: ru.sbertroika.tms.gate.v1.SettingsGroupList result = 2;
         */
        result: SettingsGroupList;
    } | {
        oneofKind: undefined;
    };
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.TerminalProfileListResponse
 */
export interface TerminalProfileListResponse {
    /**
     * @generated from protobuf oneof: response
     */
    response: {
        oneofKind: "error";
        /**
         * @generated from protobuf field: ru.sbertroika.common.v1.OperationError error = 1;
         */
        error: OperationError;
    } | {
        oneofKind: "result";
        /**
         * @generated from protobuf field: ru.sbertroika.tms.gate.v1.TerminalProfileList result = 2;
         */
        result: TerminalProfileList;
    } | {
        oneofKind: undefined;
    };
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.TemplateSettingsListResponse
 */
export interface TemplateSettingsListResponse {
    /**
     * @generated from protobuf oneof: response
     */
    response: {
        oneofKind: "error";
        /**
         * @generated from protobuf field: ru.sbertroika.common.v1.OperationError error = 1;
         */
        error: OperationError;
    } | {
        oneofKind: "result";
        /**
         * @generated from protobuf field: ru.sbertroika.tms.gate.v1.TemplateSettingsList result = 2;
         */
        result: TemplateSettingsList;
    } | {
        oneofKind: undefined;
    };
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.SettingsList
 */
export interface SettingsList {
    /**
     * @generated from protobuf field: optional ru.sbertroika.common.v1.PaginationResponse pagination = 1;
     */
    pagination?: PaginationResponse;
    /**
     * @generated from protobuf field: repeated ru.sbertroika.tms.gate.v1.Settings settings = 2;
     */
    settings: Settings[];
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.SettingsGroupList
 */
export interface SettingsGroupList {
    /**
     * @generated from protobuf field: optional ru.sbertroika.common.v1.PaginationResponse pagination = 1;
     */
    pagination?: PaginationResponse;
    /**
     * @generated from protobuf field: repeated ru.sbertroika.tms.gate.v1.SettingsGroup settingsGroup = 2;
     */
    settingsGroup: SettingsGroup[];
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.TerminalProfileList
 */
export interface TerminalProfileList {
    /**
     * @generated from protobuf field: optional ru.sbertroika.common.v1.PaginationResponse pagination = 1;
     */
    pagination?: PaginationResponse;
    /**
     * @generated from protobuf field: repeated ru.sbertroika.tms.gate.v1.TerminalProfile terminalProfile = 2;
     */
    terminalProfile: TerminalProfile[];
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.TemplateSettingsList
 */
export interface TemplateSettingsList {
    /**
     * @generated from protobuf field: optional ru.sbertroika.common.v1.PaginationResponse pagination = 1;
     */
    pagination?: PaginationResponse;
    /**
     * @generated from protobuf field: repeated ru.sbertroika.tms.gate.v1.TemplateSettings templateSettings = 2;
     */
    templateSettings: TemplateSettings[];
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.Settings
 */
export interface Settings {
    /**
     * @generated from protobuf field: optional string id = 1;
     */
    id?: string;
    /**
     * @generated from protobuf field: string name = 2;
     */
    name: string;
    /**
     * @generated from protobuf field: string alias = 3;
     */
    alias: string;
    /**
     * @generated from protobuf field: string value = 4;
     */
    value: string;
    /**
     * @generated from protobuf field: optional bool isDeleted = 5;
     */
    isDeleted?: boolean;
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.SettingsGroup
 */
export interface SettingsGroup {
    /**
     * @generated from protobuf field: optional string id = 1;
     */
    id?: string;
    /**
     * @generated from protobuf field: string name = 2;
     */
    name: string;
    /**
     * @generated from protobuf field: optional string comment = 3;
     */
    comment?: string;
    /**
     * @generated from protobuf field: optional bool isDeleted = 4;
     */
    isDeleted?: boolean;
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.TerminalProfile
 */
export interface TerminalProfile {
    /**
     * @generated from protobuf field: optional string id = 1;
     */
    id?: string;
    /**
     * @generated from protobuf field: string name = 2;
     */
    name: string;
    /**
     * @generated from protobuf field: optional google.protobuf.Timestamp activeFrom = 3;
     */
    activeFrom?: Timestamp;
    /**
     * @generated from protobuf field: optional google.protobuf.Timestamp activeTill = 4;
     */
    activeTill?: Timestamp;
    /**
     * @generated from protobuf field: ru.sbertroika.tms.gate.v1.TerminalProfileStatus status = 5;
     */
    status: TerminalProfileStatus;
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.TemplateSettings
 */
export interface TemplateSettings {
    /**
     * @generated from protobuf field: optional string id = 1;
     */
    id?: string;
    /**
     * @generated from protobuf field: string name = 2;
     */
    name: string;
    /**
     * @generated from protobuf field: string slug = 3;
     */
    slug: string;
    /**
     * @generated from protobuf field: ru.sbertroika.tms.gate.v1.TemplateSettingsType type = 4;
     */
    type: TemplateSettingsType;
    /**
     * @generated from protobuf field: bool isRequired = 5;
     */
    isRequired: boolean;
    /**
     * @generated from protobuf field: optional string comment = 6;
     */
    comment?: string;
    /**
     * @generated from protobuf field: optional string validFN = 7;
     */
    validFN?: string;
    /**
     * @generated from protobuf field: optional string defaultValue = 8;
     */
    defaultValue?: string;
    /**
     * @generated from protobuf field: optional bool isDeleted = 9;
     */
    isDeleted?: boolean;
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.SettingsResponse
 */
export interface SettingsResponse {
    /**
     * @generated from protobuf oneof: response
     */
    response: {
        oneofKind: "error";
        /**
         * @generated from protobuf field: ru.sbertroika.common.v1.OperationError error = 1;
         */
        error: OperationError;
    } | {
        oneofKind: "result";
        /**
         * @generated from protobuf field: ru.sbertroika.tms.gate.v1.Settings result = 2;
         */
        result: Settings;
    } | {
        oneofKind: undefined;
    };
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.SettingsGroupResponse
 */
export interface SettingsGroupResponse {
    /**
     * @generated from protobuf oneof: response
     */
    response: {
        oneofKind: "error";
        /**
         * @generated from protobuf field: ru.sbertroika.common.v1.OperationError error = 1;
         */
        error: OperationError;
    } | {
        oneofKind: "result";
        /**
         * @generated from protobuf field: ru.sbertroika.tms.gate.v1.SettingsGroup result = 2;
         */
        result: SettingsGroup;
    } | {
        oneofKind: undefined;
    };
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.TerminalProfileResponse
 */
export interface TerminalProfileResponse {
    /**
     * @generated from protobuf oneof: response
     */
    response: {
        oneofKind: "error";
        /**
         * @generated from protobuf field: ru.sbertroika.common.v1.OperationError error = 1;
         */
        error: OperationError;
    } | {
        oneofKind: "result";
        /**
         * @generated from protobuf field: ru.sbertroika.tms.gate.v1.TerminalProfile result = 2;
         */
        result: TerminalProfile;
    } | {
        oneofKind: undefined;
    };
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.TemplateSettingsResponse
 */
export interface TemplateSettingsResponse {
    /**
     * @generated from protobuf oneof: response
     */
    response: {
        oneofKind: "error";
        /**
         * @generated from protobuf field: ru.sbertroika.common.v1.OperationError error = 1;
         */
        error: OperationError;
    } | {
        oneofKind: "result";
        /**
         * @generated from protobuf field: ru.sbertroika.tms.gate.v1.TemplateSettings result = 2;
         */
        result: TemplateSettings;
    } | {
        oneofKind: undefined;
    };
}
/**
 * @generated from protobuf enum ru.sbertroika.tms.gate.v1.TerminalProfileStatus
 */
export enum TerminalProfileStatus {
    /**
     * @generated from protobuf enum value: TP_ACTIVE = 0;
     */
    TP_ACTIVE = 0,
    /**
     * @generated from protobuf enum value: TP_DRAFT = 1;
     */
    TP_DRAFT = 1,
    /**
     * @generated from protobuf enum value: TP_DELETED = 2;
     */
    TP_DELETED = 2
}
/**
 * @generated from protobuf enum ru.sbertroika.tms.gate.v1.TemplateSettingsType
 */
export enum TemplateSettingsType {
    /**
     * @generated from protobuf enum value: STRING = 0;
     */
    STRING = 0,
    /**
     * @generated from protobuf enum value: BOOLEAN = 1;
     */
    BOOLEAN = 1,
    /**
     * @generated from protobuf enum value: INT = 2;
     */
    INT = 2,
    /**
     * @generated from protobuf enum value: UINT = 3;
     */
    UINT = 3
}
// @generated message type with reflection information, may provide speed optimized methods
class SettingsListRequest$Type extends MessageType<SettingsListRequest> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.SettingsListRequest", [
            { no: 1, name: "pagination", kind: "message", T: () => PaginationRequest }
        ]);
    }
    create(value?: PartialMessage<SettingsListRequest>): SettingsListRequest {
        const message = globalThis.Object.create((this.messagePrototype!));
        if (value !== undefined)
            reflectionMergePartial<SettingsListRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: SettingsListRequest): SettingsListRequest {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* optional ru.sbertroika.common.v1.PaginationRequest pagination */ 1:
                    message.pagination = PaginationRequest.internalBinaryRead(reader, reader.uint32(), options, message.pagination);
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: SettingsListRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* optional ru.sbertroika.common.v1.PaginationRequest pagination = 1; */
        if (message.pagination)
            PaginationRequest.internalBinaryWrite(message.pagination, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.SettingsListRequest
 */
export const SettingsListRequest = new SettingsListRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class SettingsGroupListRequest$Type extends MessageType<SettingsGroupListRequest> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.SettingsGroupListRequest", [
            { no: 1, name: "pagination", kind: "message", T: () => PaginationRequest }
        ]);
    }
    create(value?: PartialMessage<SettingsGroupListRequest>): SettingsGroupListRequest {
        const message = globalThis.Object.create((this.messagePrototype!));
        if (value !== undefined)
            reflectionMergePartial<SettingsGroupListRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: SettingsGroupListRequest): SettingsGroupListRequest {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* optional ru.sbertroika.common.v1.PaginationRequest pagination */ 1:
                    message.pagination = PaginationRequest.internalBinaryRead(reader, reader.uint32(), options, message.pagination);
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: SettingsGroupListRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* optional ru.sbertroika.common.v1.PaginationRequest pagination = 1; */
        if (message.pagination)
            PaginationRequest.internalBinaryWrite(message.pagination, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.SettingsGroupListRequest
 */
export const SettingsGroupListRequest = new SettingsGroupListRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class TerminalProfileListRequest$Type extends MessageType<TerminalProfileListRequest> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.TerminalProfileListRequest", [
            { no: 1, name: "pagination", kind: "message", T: () => PaginationRequest }
        ]);
    }
    create(value?: PartialMessage<TerminalProfileListRequest>): TerminalProfileListRequest {
        const message = globalThis.Object.create((this.messagePrototype!));
        if (value !== undefined)
            reflectionMergePartial<TerminalProfileListRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: TerminalProfileListRequest): TerminalProfileListRequest {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* optional ru.sbertroika.common.v1.PaginationRequest pagination */ 1:
                    message.pagination = PaginationRequest.internalBinaryRead(reader, reader.uint32(), options, message.pagination);
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: TerminalProfileListRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* optional ru.sbertroika.common.v1.PaginationRequest pagination = 1; */
        if (message.pagination)
            PaginationRequest.internalBinaryWrite(message.pagination, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.TerminalProfileListRequest
 */
export const TerminalProfileListRequest = new TerminalProfileListRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class TemplateSettingsListRequest$Type extends MessageType<TemplateSettingsListRequest> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.TemplateSettingsListRequest", [
            { no: 1, name: "pagination", kind: "message", T: () => PaginationRequest }
        ]);
    }
    create(value?: PartialMessage<TemplateSettingsListRequest>): TemplateSettingsListRequest {
        const message = globalThis.Object.create((this.messagePrototype!));
        if (value !== undefined)
            reflectionMergePartial<TemplateSettingsListRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: TemplateSettingsListRequest): TemplateSettingsListRequest {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* optional ru.sbertroika.common.v1.PaginationRequest pagination */ 1:
                    message.pagination = PaginationRequest.internalBinaryRead(reader, reader.uint32(), options, message.pagination);
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: TemplateSettingsListRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* optional ru.sbertroika.common.v1.PaginationRequest pagination = 1; */
        if (message.pagination)
            PaginationRequest.internalBinaryWrite(message.pagination, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.TemplateSettingsListRequest
 */
export const TemplateSettingsListRequest = new TemplateSettingsListRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class AssignTemplateSettingsToSettingGroupRequest$Type extends MessageType<AssignTemplateSettingsToSettingGroupRequest> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.AssignTemplateSettingsToSettingGroupRequest", [
            { no: 1, name: "templateSettings", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => TemplateSettings },
            { no: 2, name: "groupId", kind: "scalar", T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<AssignTemplateSettingsToSettingGroupRequest>): AssignTemplateSettingsToSettingGroupRequest {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.templateSettings = [];
        message.groupId = "";
        if (value !== undefined)
            reflectionMergePartial<AssignTemplateSettingsToSettingGroupRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: AssignTemplateSettingsToSettingGroupRequest): AssignTemplateSettingsToSettingGroupRequest {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated ru.sbertroika.tms.gate.v1.TemplateSettings templateSettings */ 1:
                    message.templateSettings.push(TemplateSettings.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                case /* string groupId */ 2:
                    message.groupId = reader.string();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: AssignTemplateSettingsToSettingGroupRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated ru.sbertroika.tms.gate.v1.TemplateSettings templateSettings = 1; */
        for (let i = 0; i < message.templateSettings.length; i++)
            TemplateSettings.internalBinaryWrite(message.templateSettings[i], writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* string groupId = 2; */
        if (message.groupId !== "")
            writer.tag(2, WireType.LengthDelimited).string(message.groupId);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.AssignTemplateSettingsToSettingGroupRequest
 */
export const AssignTemplateSettingsToSettingGroupRequest = new AssignTemplateSettingsToSettingGroupRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class AssignSettingsToTerminalProfileRequest$Type extends MessageType<AssignSettingsToTerminalProfileRequest> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.AssignSettingsToTerminalProfileRequest", [
            { no: 1, name: "settings", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => Settings },
            { no: 2, name: "terminalProfileId", kind: "scalar", T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<AssignSettingsToTerminalProfileRequest>): AssignSettingsToTerminalProfileRequest {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.settings = [];
        message.terminalProfileId = "";
        if (value !== undefined)
            reflectionMergePartial<AssignSettingsToTerminalProfileRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: AssignSettingsToTerminalProfileRequest): AssignSettingsToTerminalProfileRequest {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated ru.sbertroika.tms.gate.v1.Settings settings */ 1:
                    message.settings.push(Settings.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                case /* string terminalProfileId */ 2:
                    message.terminalProfileId = reader.string();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: AssignSettingsToTerminalProfileRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated ru.sbertroika.tms.gate.v1.Settings settings = 1; */
        for (let i = 0; i < message.settings.length; i++)
            Settings.internalBinaryWrite(message.settings[i], writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* string terminalProfileId = 2; */
        if (message.terminalProfileId !== "")
            writer.tag(2, WireType.LengthDelimited).string(message.terminalProfileId);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.AssignSettingsToTerminalProfileRequest
 */
export const AssignSettingsToTerminalProfileRequest = new AssignSettingsToTerminalProfileRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class AssignSettingGroupToTerminalProfileRequest$Type extends MessageType<AssignSettingGroupToTerminalProfileRequest> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.AssignSettingGroupToTerminalProfileRequest", [
            { no: 1, name: "settingsGroup", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => SettingsGroup },
            { no: 2, name: "terminalProfileId", kind: "scalar", T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<AssignSettingGroupToTerminalProfileRequest>): AssignSettingGroupToTerminalProfileRequest {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.settingsGroup = [];
        message.terminalProfileId = "";
        if (value !== undefined)
            reflectionMergePartial<AssignSettingGroupToTerminalProfileRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: AssignSettingGroupToTerminalProfileRequest): AssignSettingGroupToTerminalProfileRequest {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated ru.sbertroika.tms.gate.v1.SettingsGroup settingsGroup */ 1:
                    message.settingsGroup.push(SettingsGroup.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                case /* string terminalProfileId */ 2:
                    message.terminalProfileId = reader.string();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: AssignSettingGroupToTerminalProfileRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated ru.sbertroika.tms.gate.v1.SettingsGroup settingsGroup = 1; */
        for (let i = 0; i < message.settingsGroup.length; i++)
            SettingsGroup.internalBinaryWrite(message.settingsGroup[i], writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* string terminalProfileId = 2; */
        if (message.terminalProfileId !== "")
            writer.tag(2, WireType.LengthDelimited).string(message.terminalProfileId);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.AssignSettingGroupToTerminalProfileRequest
 */
export const AssignSettingGroupToTerminalProfileRequest = new AssignSettingGroupToTerminalProfileRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class SettingsListResponse$Type extends MessageType<SettingsListResponse> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.SettingsListResponse", [
            { no: 1, name: "error", kind: "message", oneof: "response", T: () => OperationError },
            { no: 2, name: "result", kind: "message", oneof: "response", T: () => SettingsList }
        ]);
    }
    create(value?: PartialMessage<SettingsListResponse>): SettingsListResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.response = { oneofKind: undefined };
        if (value !== undefined)
            reflectionMergePartial<SettingsListResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: SettingsListResponse): SettingsListResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* ru.sbertroika.common.v1.OperationError error */ 1:
                    message.response = {
                        oneofKind: "error",
                        error: OperationError.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).error)
                    };
                    break;
                case /* ru.sbertroika.tms.gate.v1.SettingsList result */ 2:
                    message.response = {
                        oneofKind: "result",
                        result: SettingsList.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).result)
                    };
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: SettingsListResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* ru.sbertroika.common.v1.OperationError error = 1; */
        if (message.response.oneofKind === "error")
            OperationError.internalBinaryWrite(message.response.error, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* ru.sbertroika.tms.gate.v1.SettingsList result = 2; */
        if (message.response.oneofKind === "result")
            SettingsList.internalBinaryWrite(message.response.result, writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.SettingsListResponse
 */
export const SettingsListResponse = new SettingsListResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class SettingsGroupListResponse$Type extends MessageType<SettingsGroupListResponse> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.SettingsGroupListResponse", [
            { no: 1, name: "error", kind: "message", oneof: "response", T: () => OperationError },
            { no: 2, name: "result", kind: "message", oneof: "response", T: () => SettingsGroupList }
        ]);
    }
    create(value?: PartialMessage<SettingsGroupListResponse>): SettingsGroupListResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.response = { oneofKind: undefined };
        if (value !== undefined)
            reflectionMergePartial<SettingsGroupListResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: SettingsGroupListResponse): SettingsGroupListResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* ru.sbertroika.common.v1.OperationError error */ 1:
                    message.response = {
                        oneofKind: "error",
                        error: OperationError.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).error)
                    };
                    break;
                case /* ru.sbertroika.tms.gate.v1.SettingsGroupList result */ 2:
                    message.response = {
                        oneofKind: "result",
                        result: SettingsGroupList.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).result)
                    };
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: SettingsGroupListResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* ru.sbertroika.common.v1.OperationError error = 1; */
        if (message.response.oneofKind === "error")
            OperationError.internalBinaryWrite(message.response.error, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* ru.sbertroika.tms.gate.v1.SettingsGroupList result = 2; */
        if (message.response.oneofKind === "result")
            SettingsGroupList.internalBinaryWrite(message.response.result, writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.SettingsGroupListResponse
 */
export const SettingsGroupListResponse = new SettingsGroupListResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class TerminalProfileListResponse$Type extends MessageType<TerminalProfileListResponse> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.TerminalProfileListResponse", [
            { no: 1, name: "error", kind: "message", oneof: "response", T: () => OperationError },
            { no: 2, name: "result", kind: "message", oneof: "response", T: () => TerminalProfileList }
        ]);
    }
    create(value?: PartialMessage<TerminalProfileListResponse>): TerminalProfileListResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.response = { oneofKind: undefined };
        if (value !== undefined)
            reflectionMergePartial<TerminalProfileListResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: TerminalProfileListResponse): TerminalProfileListResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* ru.sbertroika.common.v1.OperationError error */ 1:
                    message.response = {
                        oneofKind: "error",
                        error: OperationError.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).error)
                    };
                    break;
                case /* ru.sbertroika.tms.gate.v1.TerminalProfileList result */ 2:
                    message.response = {
                        oneofKind: "result",
                        result: TerminalProfileList.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).result)
                    };
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: TerminalProfileListResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* ru.sbertroika.common.v1.OperationError error = 1; */
        if (message.response.oneofKind === "error")
            OperationError.internalBinaryWrite(message.response.error, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* ru.sbertroika.tms.gate.v1.TerminalProfileList result = 2; */
        if (message.response.oneofKind === "result")
            TerminalProfileList.internalBinaryWrite(message.response.result, writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.TerminalProfileListResponse
 */
export const TerminalProfileListResponse = new TerminalProfileListResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class TemplateSettingsListResponse$Type extends MessageType<TemplateSettingsListResponse> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.TemplateSettingsListResponse", [
            { no: 1, name: "error", kind: "message", oneof: "response", T: () => OperationError },
            { no: 2, name: "result", kind: "message", oneof: "response", T: () => TemplateSettingsList }
        ]);
    }
    create(value?: PartialMessage<TemplateSettingsListResponse>): TemplateSettingsListResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.response = { oneofKind: undefined };
        if (value !== undefined)
            reflectionMergePartial<TemplateSettingsListResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: TemplateSettingsListResponse): TemplateSettingsListResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* ru.sbertroika.common.v1.OperationError error */ 1:
                    message.response = {
                        oneofKind: "error",
                        error: OperationError.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).error)
                    };
                    break;
                case /* ru.sbertroika.tms.gate.v1.TemplateSettingsList result */ 2:
                    message.response = {
                        oneofKind: "result",
                        result: TemplateSettingsList.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).result)
                    };
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: TemplateSettingsListResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* ru.sbertroika.common.v1.OperationError error = 1; */
        if (message.response.oneofKind === "error")
            OperationError.internalBinaryWrite(message.response.error, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* ru.sbertroika.tms.gate.v1.TemplateSettingsList result = 2; */
        if (message.response.oneofKind === "result")
            TemplateSettingsList.internalBinaryWrite(message.response.result, writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.TemplateSettingsListResponse
 */
export const TemplateSettingsListResponse = new TemplateSettingsListResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class SettingsList$Type extends MessageType<SettingsList> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.SettingsList", [
            { no: 1, name: "pagination", kind: "message", T: () => PaginationResponse },
            { no: 2, name: "settings", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => Settings }
        ]);
    }
    create(value?: PartialMessage<SettingsList>): SettingsList {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.settings = [];
        if (value !== undefined)
            reflectionMergePartial<SettingsList>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: SettingsList): SettingsList {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* optional ru.sbertroika.common.v1.PaginationResponse pagination */ 1:
                    message.pagination = PaginationResponse.internalBinaryRead(reader, reader.uint32(), options, message.pagination);
                    break;
                case /* repeated ru.sbertroika.tms.gate.v1.Settings settings */ 2:
                    message.settings.push(Settings.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: SettingsList, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* optional ru.sbertroika.common.v1.PaginationResponse pagination = 1; */
        if (message.pagination)
            PaginationResponse.internalBinaryWrite(message.pagination, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* repeated ru.sbertroika.tms.gate.v1.Settings settings = 2; */
        for (let i = 0; i < message.settings.length; i++)
            Settings.internalBinaryWrite(message.settings[i], writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.SettingsList
 */
export const SettingsList = new SettingsList$Type();
// @generated message type with reflection information, may provide speed optimized methods
class SettingsGroupList$Type extends MessageType<SettingsGroupList> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.SettingsGroupList", [
            { no: 1, name: "pagination", kind: "message", T: () => PaginationResponse },
            { no: 2, name: "settingsGroup", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => SettingsGroup }
        ]);
    }
    create(value?: PartialMessage<SettingsGroupList>): SettingsGroupList {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.settingsGroup = [];
        if (value !== undefined)
            reflectionMergePartial<SettingsGroupList>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: SettingsGroupList): SettingsGroupList {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* optional ru.sbertroika.common.v1.PaginationResponse pagination */ 1:
                    message.pagination = PaginationResponse.internalBinaryRead(reader, reader.uint32(), options, message.pagination);
                    break;
                case /* repeated ru.sbertroika.tms.gate.v1.SettingsGroup settingsGroup */ 2:
                    message.settingsGroup.push(SettingsGroup.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: SettingsGroupList, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* optional ru.sbertroika.common.v1.PaginationResponse pagination = 1; */
        if (message.pagination)
            PaginationResponse.internalBinaryWrite(message.pagination, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* repeated ru.sbertroika.tms.gate.v1.SettingsGroup settingsGroup = 2; */
        for (let i = 0; i < message.settingsGroup.length; i++)
            SettingsGroup.internalBinaryWrite(message.settingsGroup[i], writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.SettingsGroupList
 */
export const SettingsGroupList = new SettingsGroupList$Type();
// @generated message type with reflection information, may provide speed optimized methods
class TerminalProfileList$Type extends MessageType<TerminalProfileList> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.TerminalProfileList", [
            { no: 1, name: "pagination", kind: "message", T: () => PaginationResponse },
            { no: 2, name: "terminalProfile", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => TerminalProfile }
        ]);
    }
    create(value?: PartialMessage<TerminalProfileList>): TerminalProfileList {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.terminalProfile = [];
        if (value !== undefined)
            reflectionMergePartial<TerminalProfileList>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: TerminalProfileList): TerminalProfileList {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* optional ru.sbertroika.common.v1.PaginationResponse pagination */ 1:
                    message.pagination = PaginationResponse.internalBinaryRead(reader, reader.uint32(), options, message.pagination);
                    break;
                case /* repeated ru.sbertroika.tms.gate.v1.TerminalProfile terminalProfile */ 2:
                    message.terminalProfile.push(TerminalProfile.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: TerminalProfileList, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* optional ru.sbertroika.common.v1.PaginationResponse pagination = 1; */
        if (message.pagination)
            PaginationResponse.internalBinaryWrite(message.pagination, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* repeated ru.sbertroika.tms.gate.v1.TerminalProfile terminalProfile = 2; */
        for (let i = 0; i < message.terminalProfile.length; i++)
            TerminalProfile.internalBinaryWrite(message.terminalProfile[i], writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.TerminalProfileList
 */
export const TerminalProfileList = new TerminalProfileList$Type();
// @generated message type with reflection information, may provide speed optimized methods
class TemplateSettingsList$Type extends MessageType<TemplateSettingsList> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.TemplateSettingsList", [
            { no: 1, name: "pagination", kind: "message", T: () => PaginationResponse },
            { no: 2, name: "templateSettings", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => TemplateSettings }
        ]);
    }
    create(value?: PartialMessage<TemplateSettingsList>): TemplateSettingsList {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.templateSettings = [];
        if (value !== undefined)
            reflectionMergePartial<TemplateSettingsList>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: TemplateSettingsList): TemplateSettingsList {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* optional ru.sbertroika.common.v1.PaginationResponse pagination */ 1:
                    message.pagination = PaginationResponse.internalBinaryRead(reader, reader.uint32(), options, message.pagination);
                    break;
                case /* repeated ru.sbertroika.tms.gate.v1.TemplateSettings templateSettings */ 2:
                    message.templateSettings.push(TemplateSettings.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: TemplateSettingsList, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* optional ru.sbertroika.common.v1.PaginationResponse pagination = 1; */
        if (message.pagination)
            PaginationResponse.internalBinaryWrite(message.pagination, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* repeated ru.sbertroika.tms.gate.v1.TemplateSettings templateSettings = 2; */
        for (let i = 0; i < message.templateSettings.length; i++)
            TemplateSettings.internalBinaryWrite(message.templateSettings[i], writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.TemplateSettingsList
 */
export const TemplateSettingsList = new TemplateSettingsList$Type();
// @generated message type with reflection information, may provide speed optimized methods
class Settings$Type extends MessageType<Settings> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.Settings", [
            { no: 1, name: "id", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "name", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 3, name: "alias", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 4, name: "value", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 5, name: "isDeleted", kind: "scalar", opt: true, T: 8 /*ScalarType.BOOL*/ }
        ]);
    }
    create(value?: PartialMessage<Settings>): Settings {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.name = "";
        message.alias = "";
        message.value = "";
        if (value !== undefined)
            reflectionMergePartial<Settings>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: Settings): Settings {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* optional string id */ 1:
                    message.id = reader.string();
                    break;
                case /* string name */ 2:
                    message.name = reader.string();
                    break;
                case /* string alias */ 3:
                    message.alias = reader.string();
                    break;
                case /* string value */ 4:
                    message.value = reader.string();
                    break;
                case /* optional bool isDeleted */ 5:
                    message.isDeleted = reader.bool();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: Settings, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* optional string id = 1; */
        if (message.id !== undefined)
            writer.tag(1, WireType.LengthDelimited).string(message.id);
        /* string name = 2; */
        if (message.name !== "")
            writer.tag(2, WireType.LengthDelimited).string(message.name);
        /* string alias = 3; */
        if (message.alias !== "")
            writer.tag(3, WireType.LengthDelimited).string(message.alias);
        /* string value = 4; */
        if (message.value !== "")
            writer.tag(4, WireType.LengthDelimited).string(message.value);
        /* optional bool isDeleted = 5; */
        if (message.isDeleted !== undefined)
            writer.tag(5, WireType.Varint).bool(message.isDeleted);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.Settings
 */
export const Settings = new Settings$Type();
// @generated message type with reflection information, may provide speed optimized methods
class SettingsGroup$Type extends MessageType<SettingsGroup> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.SettingsGroup", [
            { no: 1, name: "id", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "name", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 3, name: "comment", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 4, name: "isDeleted", kind: "scalar", opt: true, T: 8 /*ScalarType.BOOL*/ }
        ]);
    }
    create(value?: PartialMessage<SettingsGroup>): SettingsGroup {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.name = "";
        if (value !== undefined)
            reflectionMergePartial<SettingsGroup>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: SettingsGroup): SettingsGroup {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* optional string id */ 1:
                    message.id = reader.string();
                    break;
                case /* string name */ 2:
                    message.name = reader.string();
                    break;
                case /* optional string comment */ 3:
                    message.comment = reader.string();
                    break;
                case /* optional bool isDeleted */ 4:
                    message.isDeleted = reader.bool();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: SettingsGroup, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* optional string id = 1; */
        if (message.id !== undefined)
            writer.tag(1, WireType.LengthDelimited).string(message.id);
        /* string name = 2; */
        if (message.name !== "")
            writer.tag(2, WireType.LengthDelimited).string(message.name);
        /* optional string comment = 3; */
        if (message.comment !== undefined)
            writer.tag(3, WireType.LengthDelimited).string(message.comment);
        /* optional bool isDeleted = 4; */
        if (message.isDeleted !== undefined)
            writer.tag(4, WireType.Varint).bool(message.isDeleted);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.SettingsGroup
 */
export const SettingsGroup = new SettingsGroup$Type();
// @generated message type with reflection information, may provide speed optimized methods
class TerminalProfile$Type extends MessageType<TerminalProfile> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.TerminalProfile", [
            { no: 1, name: "id", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "name", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 3, name: "activeFrom", kind: "message", T: () => Timestamp },
            { no: 4, name: "activeTill", kind: "message", T: () => Timestamp },
            { no: 5, name: "status", kind: "enum", T: () => ["ru.sbertroika.tms.gate.v1.TerminalProfileStatus", TerminalProfileStatus] }
        ]);
    }
    create(value?: PartialMessage<TerminalProfile>): TerminalProfile {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.name = "";
        message.status = 0;
        if (value !== undefined)
            reflectionMergePartial<TerminalProfile>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: TerminalProfile): TerminalProfile {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* optional string id */ 1:
                    message.id = reader.string();
                    break;
                case /* string name */ 2:
                    message.name = reader.string();
                    break;
                case /* optional google.protobuf.Timestamp activeFrom */ 3:
                    message.activeFrom = Timestamp.internalBinaryRead(reader, reader.uint32(), options, message.activeFrom);
                    break;
                case /* optional google.protobuf.Timestamp activeTill */ 4:
                    message.activeTill = Timestamp.internalBinaryRead(reader, reader.uint32(), options, message.activeTill);
                    break;
                case /* ru.sbertroika.tms.gate.v1.TerminalProfileStatus status */ 5:
                    message.status = reader.int32();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: TerminalProfile, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* optional string id = 1; */
        if (message.id !== undefined)
            writer.tag(1, WireType.LengthDelimited).string(message.id);
        /* string name = 2; */
        if (message.name !== "")
            writer.tag(2, WireType.LengthDelimited).string(message.name);
        /* optional google.protobuf.Timestamp activeFrom = 3; */
        if (message.activeFrom)
            Timestamp.internalBinaryWrite(message.activeFrom, writer.tag(3, WireType.LengthDelimited).fork(), options).join();
        /* optional google.protobuf.Timestamp activeTill = 4; */
        if (message.activeTill)
            Timestamp.internalBinaryWrite(message.activeTill, writer.tag(4, WireType.LengthDelimited).fork(), options).join();
        /* ru.sbertroika.tms.gate.v1.TerminalProfileStatus status = 5; */
        if (message.status !== 0)
            writer.tag(5, WireType.Varint).int32(message.status);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.TerminalProfile
 */
export const TerminalProfile = new TerminalProfile$Type();
// @generated message type with reflection information, may provide speed optimized methods
class TemplateSettings$Type extends MessageType<TemplateSettings> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.TemplateSettings", [
            { no: 1, name: "id", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "name", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 3, name: "slug", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 4, name: "type", kind: "enum", T: () => ["ru.sbertroika.tms.gate.v1.TemplateSettingsType", TemplateSettingsType] },
            { no: 5, name: "isRequired", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 6, name: "comment", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 7, name: "validFN", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 8, name: "defaultValue", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 9, name: "isDeleted", kind: "scalar", opt: true, T: 8 /*ScalarType.BOOL*/ }
        ]);
    }
    create(value?: PartialMessage<TemplateSettings>): TemplateSettings {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.name = "";
        message.slug = "";
        message.type = 0;
        message.isRequired = false;
        if (value !== undefined)
            reflectionMergePartial<TemplateSettings>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: TemplateSettings): TemplateSettings {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* optional string id */ 1:
                    message.id = reader.string();
                    break;
                case /* string name */ 2:
                    message.name = reader.string();
                    break;
                case /* string slug */ 3:
                    message.slug = reader.string();
                    break;
                case /* ru.sbertroika.tms.gate.v1.TemplateSettingsType type */ 4:
                    message.type = reader.int32();
                    break;
                case /* bool isRequired */ 5:
                    message.isRequired = reader.bool();
                    break;
                case /* optional string comment */ 6:
                    message.comment = reader.string();
                    break;
                case /* optional string validFN */ 7:
                    message.validFN = reader.string();
                    break;
                case /* optional string defaultValue */ 8:
                    message.defaultValue = reader.string();
                    break;
                case /* optional bool isDeleted */ 9:
                    message.isDeleted = reader.bool();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: TemplateSettings, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* optional string id = 1; */
        if (message.id !== undefined)
            writer.tag(1, WireType.LengthDelimited).string(message.id);
        /* string name = 2; */
        if (message.name !== "")
            writer.tag(2, WireType.LengthDelimited).string(message.name);
        /* string slug = 3; */
        if (message.slug !== "")
            writer.tag(3, WireType.LengthDelimited).string(message.slug);
        /* ru.sbertroika.tms.gate.v1.TemplateSettingsType type = 4; */
        if (message.type !== 0)
            writer.tag(4, WireType.Varint).int32(message.type);
        /* bool isRequired = 5; */
        if (message.isRequired !== false)
            writer.tag(5, WireType.Varint).bool(message.isRequired);
        /* optional string comment = 6; */
        if (message.comment !== undefined)
            writer.tag(6, WireType.LengthDelimited).string(message.comment);
        /* optional string validFN = 7; */
        if (message.validFN !== undefined)
            writer.tag(7, WireType.LengthDelimited).string(message.validFN);
        /* optional string defaultValue = 8; */
        if (message.defaultValue !== undefined)
            writer.tag(8, WireType.LengthDelimited).string(message.defaultValue);
        /* optional bool isDeleted = 9; */
        if (message.isDeleted !== undefined)
            writer.tag(9, WireType.Varint).bool(message.isDeleted);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.TemplateSettings
 */
export const TemplateSettings = new TemplateSettings$Type();
// @generated message type with reflection information, may provide speed optimized methods
class SettingsResponse$Type extends MessageType<SettingsResponse> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.SettingsResponse", [
            { no: 1, name: "error", kind: "message", oneof: "response", T: () => OperationError },
            { no: 2, name: "result", kind: "message", oneof: "response", T: () => Settings }
        ]);
    }
    create(value?: PartialMessage<SettingsResponse>): SettingsResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.response = { oneofKind: undefined };
        if (value !== undefined)
            reflectionMergePartial<SettingsResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: SettingsResponse): SettingsResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* ru.sbertroika.common.v1.OperationError error */ 1:
                    message.response = {
                        oneofKind: "error",
                        error: OperationError.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).error)
                    };
                    break;
                case /* ru.sbertroika.tms.gate.v1.Settings result */ 2:
                    message.response = {
                        oneofKind: "result",
                        result: Settings.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).result)
                    };
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: SettingsResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* ru.sbertroika.common.v1.OperationError error = 1; */
        if (message.response.oneofKind === "error")
            OperationError.internalBinaryWrite(message.response.error, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* ru.sbertroika.tms.gate.v1.Settings result = 2; */
        if (message.response.oneofKind === "result")
            Settings.internalBinaryWrite(message.response.result, writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.SettingsResponse
 */
export const SettingsResponse = new SettingsResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class SettingsGroupResponse$Type extends MessageType<SettingsGroupResponse> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.SettingsGroupResponse", [
            { no: 1, name: "error", kind: "message", oneof: "response", T: () => OperationError },
            { no: 2, name: "result", kind: "message", oneof: "response", T: () => SettingsGroup }
        ]);
    }
    create(value?: PartialMessage<SettingsGroupResponse>): SettingsGroupResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.response = { oneofKind: undefined };
        if (value !== undefined)
            reflectionMergePartial<SettingsGroupResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: SettingsGroupResponse): SettingsGroupResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* ru.sbertroika.common.v1.OperationError error */ 1:
                    message.response = {
                        oneofKind: "error",
                        error: OperationError.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).error)
                    };
                    break;
                case /* ru.sbertroika.tms.gate.v1.SettingsGroup result */ 2:
                    message.response = {
                        oneofKind: "result",
                        result: SettingsGroup.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).result)
                    };
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: SettingsGroupResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* ru.sbertroika.common.v1.OperationError error = 1; */
        if (message.response.oneofKind === "error")
            OperationError.internalBinaryWrite(message.response.error, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* ru.sbertroika.tms.gate.v1.SettingsGroup result = 2; */
        if (message.response.oneofKind === "result")
            SettingsGroup.internalBinaryWrite(message.response.result, writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.SettingsGroupResponse
 */
export const SettingsGroupResponse = new SettingsGroupResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class TerminalProfileResponse$Type extends MessageType<TerminalProfileResponse> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.TerminalProfileResponse", [
            { no: 1, name: "error", kind: "message", oneof: "response", T: () => OperationError },
            { no: 2, name: "result", kind: "message", oneof: "response", T: () => TerminalProfile }
        ]);
    }
    create(value?: PartialMessage<TerminalProfileResponse>): TerminalProfileResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.response = { oneofKind: undefined };
        if (value !== undefined)
            reflectionMergePartial<TerminalProfileResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: TerminalProfileResponse): TerminalProfileResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* ru.sbertroika.common.v1.OperationError error */ 1:
                    message.response = {
                        oneofKind: "error",
                        error: OperationError.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).error)
                    };
                    break;
                case /* ru.sbertroika.tms.gate.v1.TerminalProfile result */ 2:
                    message.response = {
                        oneofKind: "result",
                        result: TerminalProfile.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).result)
                    };
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: TerminalProfileResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* ru.sbertroika.common.v1.OperationError error = 1; */
        if (message.response.oneofKind === "error")
            OperationError.internalBinaryWrite(message.response.error, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* ru.sbertroika.tms.gate.v1.TerminalProfile result = 2; */
        if (message.response.oneofKind === "result")
            TerminalProfile.internalBinaryWrite(message.response.result, writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.TerminalProfileResponse
 */
export const TerminalProfileResponse = new TerminalProfileResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class TemplateSettingsResponse$Type extends MessageType<TemplateSettingsResponse> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.TemplateSettingsResponse", [
            { no: 1, name: "error", kind: "message", oneof: "response", T: () => OperationError },
            { no: 2, name: "result", kind: "message", oneof: "response", T: () => TemplateSettings }
        ]);
    }
    create(value?: PartialMessage<TemplateSettingsResponse>): TemplateSettingsResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.response = { oneofKind: undefined };
        if (value !== undefined)
            reflectionMergePartial<TemplateSettingsResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: TemplateSettingsResponse): TemplateSettingsResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* ru.sbertroika.common.v1.OperationError error */ 1:
                    message.response = {
                        oneofKind: "error",
                        error: OperationError.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).error)
                    };
                    break;
                case /* ru.sbertroika.tms.gate.v1.TemplateSettings result */ 2:
                    message.response = {
                        oneofKind: "result",
                        result: TemplateSettings.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).result)
                    };
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: TemplateSettingsResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* ru.sbertroika.common.v1.OperationError error = 1; */
        if (message.response.oneofKind === "error")
            OperationError.internalBinaryWrite(message.response.error, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* ru.sbertroika.tms.gate.v1.TemplateSettings result = 2; */
        if (message.response.oneofKind === "result")
            TemplateSettings.internalBinaryWrite(message.response.result, writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.TemplateSettingsResponse
 */
export const TemplateSettingsResponse = new TemplateSettingsResponse$Type();
/**
 * @generated ServiceType for protobuf service ru.sbertroika.tms.gate.v1.TMSGatePrivateTSService
 */
export const TMSGatePrivateTSService = new ServiceType("ru.sbertroika.tms.gate.v1.TMSGatePrivateTSService", [
    { name: "settingsList", options: {}, I: SettingsListRequest, O: SettingsListResponse },
    { name: "settingsGroupList", options: {}, I: SettingsGroupListRequest, O: SettingsGroupListResponse },
    { name: "terminalProfileList", options: {}, I: TerminalProfileListRequest, O: TerminalProfileListResponse },
    { name: "templateSettingsList", options: {}, I: TemplateSettingsListRequest, O: TemplateSettingsListResponse },
    { name: "createSettings", options: {}, I: Settings, O: EmptyResponse },
    { name: "createSettingsGroup", options: {}, I: SettingsGroup, O: EmptyResponse },
    { name: "createTerminalProfile", options: {}, I: TerminalProfile, O: EmptyResponse },
    { name: "createTemplateSettings", options: {}, I: TemplateSettings, O: EmptyResponse },
    { name: "updateSettings", options: {}, I: Settings, O: EmptyResponse },
    { name: "updateSettingsGroup", options: {}, I: SettingsGroup, O: EmptyResponse },
    { name: "updateTerminalProfile", options: {}, I: TerminalProfile, O: EmptyResponse },
    { name: "updateTemplateSettings", options: {}, I: TemplateSettings, O: EmptyResponse },
    { name: "deleteSettings", options: {}, I: ByIdRequest, O: EmptyResponse },
    { name: "deleteSettingsGroup", options: {}, I: ByIdRequest, O: EmptyResponse },
    { name: "deleteTerminalProfile", options: {}, I: ByIdRequest, O: EmptyResponse },
    { name: "deleteTemplateSettings", options: {}, I: ByIdRequest, O: EmptyResponse },
    { name: "getSettingsById", options: {}, I: ByIdRequest, O: SettingsResponse },
    { name: "getSettingsGroupById", options: {}, I: ByIdRequest, O: SettingsGroupResponse },
    { name: "getTerminalProfileById", options: {}, I: ByIdRequest, O: TerminalProfileResponse },
    { name: "getTemplateSettingsById", options: {}, I: ByIdRequest, O: TemplateSettingsResponse },
    { name: "getTemplateSettingsForSettingGroup", options: {}, I: ByIdRequest, O: TemplateSettingsListResponse },
    { name: "getSettingsForTerminalProfile", options: {}, I: ByIdRequest, O: SettingsListResponse },
    { name: "getSettingGroupForTerminalProfile", options: {}, I: ByIdRequest, O: SettingsGroupListResponse },
    { name: "assignTemplateSettingsToSettingGroup", options: {}, I: AssignTemplateSettingsToSettingGroupRequest, O: EmptyResponse },
    { name: "assignSettingsToTerminalProfile", options: {}, I: AssignSettingsToTerminalProfileRequest, O: EmptyResponse },
    { name: "assignSettingGroupToTerminalProfile", options: {}, I: AssignSettingGroupToTerminalProfileRequest, O: EmptyResponse }
]);
