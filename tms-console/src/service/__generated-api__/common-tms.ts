// @generated by protobuf-ts 2.9.3
// @generated from protobuf file "common-tms.proto" (package "ru.sbertroika.common.tms", syntax proto3)
// tslint:disable
/**
 * @generated from protobuf enum ru.sbertroika.common.tms.EventType
 */
export enum EventType {
    /**
     * Открытие смены
     *
     * @generated from protobuf enum value: T_SHIFT_OPEN = 0;
     */
    T_SHIFT_OPEN = 0,
    /**
     * Закрытие смены
     *
     * @generated from protobuf enum value: T_SHIFT_CLOSE = 1;
     */
    T_SHIFT_CLOSE = 1,
    /**
     * Оплата банковской картой
     *
     * @generated from protobuf enum value: T_EMV_PAY_SUCCESS = 2;
     */
    T_EMV_PAY_SUCCESS = 2,
    /**
     * Отказ банковского ядра
     *
     * @generated from protobuf enum value: T_EMV_FAIL = 3;
     */
    T_EMV_FAIL = 3,
    /**
     * Оплата наличными
     *
     * @generated from protobuf enum value: T_CASH_PAY = 4;
     */
    T_CASH_PAY = 4,
    /**
     * Изменение состояния терминала
     *
     * @generated from protobuf enum value: T_SERVICE_STAT = 5;
     */
    T_SERVICE_STAT = 5,
    /**
     * Успешная печать
     *
     * @generated from protobuf enum value: T_PRINT_SUCCESS = 6;
     */
    T_PRINT_SUCCESS = 6,
    /**
     * Неуспешная печать
     *
     * @generated from protobuf enum value: T_PRINT_ERROR = 7;
     */
    T_PRINT_ERROR = 7,
    /**
     * Печать отчета о реализации
     *
     * @generated from protobuf enum value: T_SHIFT_REPORT_PRINT = 8;
     */
    T_SHIFT_REPORT_PRINT = 8,
    /**
     * Отказ в обслуживании (БК заблокирована по списку BIN)
     *
     * @generated from protobuf enum value: T_EMV_BIN_STOP_LIST = 9;
     */
    T_EMV_BIN_STOP_LIST = 9,
    /**
     * Отказ в обслуживании (БК заблокирована по списку PAN)
     *
     * @generated from protobuf enum value: T_EMV_PAN_STOP_LIST = 10;
     */
    T_EMV_PAN_STOP_LIST = 10,
    /**
     * Оплата транспортной картой Простор
     *
     * @generated from protobuf enum value: T_TK_PROSTOR_PAY = 11;
     */
    T_TK_PROSTOR_PAY = 11,
    /**
     * Полинг карты
     *
     * @generated from protobuf enum value: T_POLL_CARD = 12;
     */
    T_POLL_CARD = 12,
    /**
     * Ошибка определения типа карты
     *
     * @generated from protobuf enum value: T_CARD_PROCESS_FAIL = 13;
     */
    T_CARD_PROCESS_FAIL = 13,
    /**
     * Оплата картой Тройка кошелёк
     *
     * @generated from protobuf enum value: T_TROIKA_WALLET_PAY_SUCCESS = 14;
     */
    T_TROIKA_WALLET_PAY_SUCCESS = 14,
    /**
     * Отказ оплаты картой Тройка кошелёк
     *
     * @generated from protobuf enum value: T_TROIKA_WALLET_PAY_FAIL = 15;
     */
    T_TROIKA_WALLET_PAY_FAIL = 15,
    /**
     * Отказ в обслуживании (БК заблокирована по списку PAR)
     *
     * @generated from protobuf enum value: T_EMV_PAR_STOP_LIST = 16;
     */
    T_EMV_PAR_STOP_LIST = 16,
    /**
     * Отказ в обслуживании (Тройка заблокирована по списку UID)
     *
     * @generated from protobuf enum value: T_TROIKA_STOP_LIST = 17;
     */
    T_TROIKA_STOP_LIST = 17,
    /**
     * Выбор маршрута
     *
     * @generated from protobuf enum value: T_ROUTE_SELECT = 18;
     */
    T_ROUTE_SELECT = 18,
    /**
     * Выбор Т/С
     *
     * @generated from protobuf enum value: T_VEHICLE_SELECT = 19;
     */
    T_VEHICLE_SELECT = 19,
    /**
     * Оплата картой Тройка абонементом
     *
     * @generated from protobuf enum value: T_TROIKA_TICKET_PAY_SUCCESS = 20;
     */
    T_TROIKA_TICKET_PAY_SUCCESS = 20,
    /**
     * Отказ оплаты картой Тройка абонементом
     *
     * @generated from protobuf enum value: T_TROIKA_TICKET_PAY_FAIL = 21;
     */
    T_TROIKA_TICKET_PAY_FAIL = 21,
    /**
     * Оплата идентификатором (Кошелек)
     *
     * @generated from protobuf enum value: T_ABT_WALLET_PAY_SUCCESS = 22;
     */
    T_ABT_WALLET_PAY_SUCCESS = 22,
    /**
     * Отказ оплаты идентификатором (Кошелек)
     *
     * @generated from protobuf enum value: T_ABT_WALLET_PAY_FAIL = 23;
     */
    T_ABT_WALLET_PAY_FAIL = 23,
    /**
     * Оплата идентификатором (Билет/Абонемент)
     *
     * @generated from protobuf enum value: T_ABT_TICKET_PAY_SUCCESS = 24;
     */
    T_ABT_TICKET_PAY_SUCCESS = 24,
    /**
     * Отказ оплаты идентификатором (Билет/Абонемент)
     *
     * @generated from protobuf enum value: T_ABT_TICKET_PAY_FAIL = 25;
     */
    T_ABT_TICKET_PAY_FAIL = 25,
    /**
     * Оплата кошельком QR (виртуальной картой)
     *
     * @generated from protobuf enum value: T_QR_WALLET_PAY_SUCCESS = 26;
     */
    T_QR_WALLET_PAY_SUCCESS = 26,
    /**
     * Отказ оплаты кошельком QR (виртуальной картой)
     *
     * @generated from protobuf enum value: T_QR_WALLET_PAY_FAIL = 27;
     */
    T_QR_WALLET_PAY_FAIL = 27,
    /**
     * Оплата билетом/абонементом QR (виртуальной картой)
     *
     * @generated from protobuf enum value: T_QR_TICKET_PAY_SUCCESS = 28;
     */
    T_QR_TICKET_PAY_SUCCESS = 28,
    /**
     * Отказ билетом/абонементом кошельком QR (виртуальной картой)
     *
     * @generated from protobuf enum value: T_QR_TICKET_PAY_FAIL = 29;
     */
    T_QR_TICKET_PAY_FAIL = 29,
    /**
     * Не найден тариф оплаты
     *
     * @generated from protobuf enum value: T_TARIFF_NOT_FOUND = 30;
     */
    T_TARIFF_NOT_FOUND = 30,
    /**
     * КРС: оплата была совершена
     *
     * @generated from protobuf enum value: T_KRS_CONTROL_SUCCESS = 31;
     */
    T_KRS_CONTROL_SUCCESS = 31,
    /**
     * КРС: оплаты не было
     *
     * @generated from protobuf enum value: T_KRS_CONTROL_FAIL = 32;
     */
    T_KRS_CONTROL_FAIL = 32,
    /**
     * КРС: ошибка конфигурации
     *
     * @generated from protobuf enum value: T_KRS_CONFIG_ERROR = 33;
     */
    T_KRS_CONFIG_ERROR = 33,
    /**
     * Повторное прикладывание карты
     *
     * @generated from protobuf enum value: T_CARD_ATTACH_AGAIN = 34;
     */
    T_CARD_ATTACH_AGAIN = 34
}
/**
 * @generated from protobuf enum ru.sbertroika.common.tms.EventAttribute
 */
export enum EventAttribute {
    /**
     * Единый регистрационный номер
     *
     * @generated from protobuf enum value: EA_ERN = 0;
     */
    EA_ERN = 0,
    /**
     * Код операции
     *
     * @generated from protobuf enum value: EA_OPERATION_NUMBER = 1;
     */
    EA_OPERATION_NUMBER = 1,
    /**
     * Идентификатор пользователя
     *
     * @generated from protobuf enum value: EA_USER_ID = 2;
     */
    EA_USER_ID = 2,
    /**
     * Номер смены
     *
     * @generated from protobuf enum value: EA_SHIFT_NUM = 3;
     */
    EA_SHIFT_NUM = 3,
    /**
     * Сообщение в формате ISO8583
     *
     * @generated from protobuf enum value: EA_ISO = 4;
     */
    EA_ISO = 4,
    /**
     * Код ответа (от переферии, банковского ядра или библиотеки)
     *
     * @generated from protobuf enum value: EA_CODE = 5;
     */
    EA_CODE = 5,
    /**
     * Проивольное сообщение в UTF_8
     *
     * @generated from protobuf enum value: EA_MESSAGE = 6;
     */
    EA_MESSAGE = 6,
    /**
     * Серия билета
     *
     * @generated from protobuf enum value: EA_TICKET_SERIES = 7;
     */
    EA_TICKET_SERIES = 7,
    /**
     * Номер билета
     *
     * @generated from protobuf enum value: EA_TICKET_NUMBER = 8;
     */
    EA_TICKET_NUMBER = 8,
    /**
     * Идентификатор манифеста
     *
     * @generated from protobuf enum value: EA_MANIFEST_NUMBER = 9;
     */
    EA_MANIFEST_NUMBER = 9,
    /**
     * PAN Hash in sha256 (если отличный от SHA256 то должен быть проставлен EA_PAN_HASH_TYPE)
     *
     * @generated from protobuf enum value: EA_PAN_HASH = 10;
     */
    EA_PAN_HASH = 10,
    /**
     * Список билетов в формате serial:number;serial:number;.... Разделитель билетов ';'
     *
     * @generated from protobuf enum value: EA_TICKETS = 11;
     */
    EA_TICKETS = 11,
    /**
     * Версия стоп-листа
     *
     * @generated from protobuf enum value: EA_STOP_LIST_VERSION = 12;
     */
    EA_STOP_LIST_VERSION = 12,
    /**
     * Дата последнего обновления стоп-листа
     *
     * @generated from protobuf enum value: EA_STOP_LIST_UPDATE_DATE = 13;
     */
    EA_STOP_LIST_UPDATE_DATE = 13,
    /**
     * Битмап после списания в HEX
     *
     * @generated from protobuf enum value: EA_RAW = 14;
     */
    EA_RAW = 14,
    /**
     * UID карты HEX
     *
     * @generated from protobuf enum value: EA_CARD_UID = 15;
     */
    EA_CARD_UID = 15,
    /**
     * SAK карты HEX
     *
     * @generated from protobuf enum value: EA_CARD_SAK = 16;
     */
    EA_CARD_SAK = 16,
    /**
     * ATQA карты HEX/null
     *
     * @generated from protobuf enum value: EA_CARD_ATQA = 17;
     */
    EA_CARD_ATQA = 17,
    /**
     * Флаг(boolean) карта типа B
     *
     * @generated from protobuf enum value: EA_CARD_B = 18;
     */
    EA_CARD_B = 18,
    /**
     * Флаг(boolean) банковская карта
     *
     * @generated from protobuf enum value: EA_CARD_EMV = 19;
     */
    EA_CARD_EMV = 19,
    /**
     * Формат сообщения
     *
     * @generated from protobuf enum value: EA_RAW_FORMAT = 20;
     */
    EA_RAW_FORMAT = 20,
    /**
     * PAR
     *
     * @generated from protobuf enum value: EA_PAR = 21;
     */
    EA_PAR = 21,
    /**
     * Версия манифеста
     *
     * @generated from protobuf enum value: EA_MANIFEST_VER = 22;
     */
    EA_MANIFEST_VER = 22,
    /**
     * Идентификатор маршрута
     *
     * @generated from protobuf enum value: EA_ROUTE_ID = 23;
     */
    EA_ROUTE_ID = 23,
    /**
     * Идентификатор Т/С
     *
     * @generated from protobuf enum value: EA_VEHICLE_ID = 24;
     */
    EA_VEHICLE_ID = 24,
    /**
     * Тип хэша функции (Возможные значения: SHA256/HMAC_SHA1/HMAC_SHA256/HMAC_SHA256_SHA256/STRIBOG512)
     *
     * @generated from protobuf enum value: EA_PAN_HASH_TYPE = 25;
     */
    EA_PAN_HASH_TYPE = 25,
    /**
     * Идентификатор шаблона (списания/продления)
     *
     * @generated from protobuf enum value: EA_TEMPLATE_ID = 26;
     */
    EA_TEMPLATE_ID = 26,
    /**
     * Счётчики успешных оплат
     *
     * @generated from protobuf enum value: EA_COUNTERS = 27;
     */
    EA_COUNTERS = 27,
    /**
     * Тип оплаты
     *
     * @generated from protobuf enum value: EA_PAY_TYPE = 28;
     */
    EA_PAY_TYPE = 28,
    /**
     * Данные в рамках события
     *
     * @generated from protobuf enum value: EA_VALUE = 100;
     */
    EA_VALUE = 100,
    /**
     * Check-in
     *
     * @generated from protobuf enum value: EA_CHECK_IN = 101;
     */
    EA_CHECK_IN = 101,
    /**
     * Check-out
     *
     * @generated from protobuf enum value: EA_CHECK_OUT = 102;
     */
    EA_CHECK_OUT = 102,
    /**
     * Transport card number
     *
     * @generated from protobuf enum value: EA_CARD_NUM = 103;
     */
    EA_CARD_NUM = 103
}
/**
 * @generated from protobuf enum ru.sbertroika.common.tms.SettingParam
 */
export enum SettingParam {
    /**
     * Период синхронизации
     *
     * @generated from protobuf enum value: P_SYNC_TIMEOUT = 0;
     */
    P_SYNC_TIMEOUT = 0,
    /**
     * Ключ A к сектору 1 для карт простор
     *
     * @generated from protobuf enum value: P_PROSTOR_KEY_1A = 100;
     */
    P_PROSTOR_KEY_1A = 100,
    /**
     * Ключ B к сектору 1 для карт простор
     *
     * @generated from protobuf enum value: P_PROSTOR_KEY_1B = 101;
     */
    P_PROSTOR_KEY_1B = 101,
    /**
     * Тип ключа на чтение к сектору 1 для карт простор
     *
     * @generated from protobuf enum value: P_PROSTOR_KEY_TYPE_1R = 104;
     */
    P_PROSTOR_KEY_TYPE_1R = 104,
    /**
     * Тип ключа на запись к сектору 1 для карт простор
     *
     * @generated from protobuf enum value: P_PROSTOR_KEY_TYPE_1W = 105;
     */
    P_PROSTOR_KEY_TYPE_1W = 105,
    /**
     * Ключ A к сектору 8 для карт простор
     *
     * @generated from protobuf enum value: P_PROSTOR_KEY_8A = 102;
     */
    P_PROSTOR_KEY_8A = 102,
    /**
     * Ключ B к сектору 8 для карт простор
     *
     * @generated from protobuf enum value: P_PROSTOR_KEY_8B = 103;
     */
    P_PROSTOR_KEY_8B = 103,
    /**
     * Тип ключа на чтение к сектору 8 для карт простор
     *
     * @generated from protobuf enum value: P_PROSTOR_KEY_TYPE_8R = 106;
     */
    P_PROSTOR_KEY_TYPE_8R = 106,
    /**
     * Тип ключа на запись к сектору 8 для карт простор
     *
     * @generated from protobuf enum value: P_PROSTOR_KEY_TYPE_8W = 107;
     */
    P_PROSTOR_KEY_TYPE_8W = 107,
    /**
     * Флаг(boolean) использования физического SAM
     *
     * @generated from protobuf enum value: P_TROIKA_IS_USE_SAM = 200;
     */
    P_TROIKA_IS_USE_SAM = 200,
    /**
     * Флаг(boolean) включение пересадок по кошельку "Тройки"
     *
     * @generated from protobuf enum value: P_TROIKA_WALLET_TRANSFER_ENABLE = 201;
     */
    P_TROIKA_WALLET_TRANSFER_ENABLE = 201,
    /**
     * Скидка при пересадке задаётся в процентах,
     * если не задан или false, то в копейках
     *
     * @generated from protobuf enum value: P_TROIKA_WALLET_TRANSFER_IS_PERCENT = 202;
     */
    P_TROIKA_WALLET_TRANSFER_IS_PERCENT = 202,
    /**
     * Время для пересадки в минутах
     *
     * @generated from protobuf enum value: P_TROIKA_WALLET_TRANSFER_DELAY = 203;
     */
    P_TROIKA_WALLET_TRANSFER_DELAY = 203,
    /**
     * Размер скидки при пересадке
     *
     * @generated from protobuf enum value: P_TROIKA_WALLET_TRANSFER_DISCOUNT = 204;
     */
    P_TROIKA_WALLET_TRANSFER_DISCOUNT = 204,
    /**
     * Режим работы КРС: BT - Bluetooth, WF - WiFi, CD - Card
     *
     * @generated from protobuf enum value: P_KRS_WORK_TYPE = 300;
     */
    P_KRS_WORK_TYPE = 300,
    /**
     * Имя Bluetooth устройства
     *
     * @generated from protobuf enum value: P_KRS_BT_DEVICE_NAME = 301;
     */
    P_KRS_BT_DEVICE_NAME = 301,
    /**
     * Список разрешенных имён (масок) устройств
     *
     * @generated from protobuf enum value: P_KRS_BT_TRUSTED_NAMES = 302;
     */
    P_KRS_BT_TRUSTED_NAMES = 302,
    /**
     * Время доступности устройства для сопряжения
     *
     * @generated from protobuf enum value: P_KRS_BT_SERVER_DISCOVERABLE_TIME = 303;
     */
    P_KRS_BT_SERVER_DISCOVERABLE_TIME = 303,
    /**
     * Очистка разрешенных имён (масок) устройств
     *
     * @generated from protobuf enum value: P_KRS_BT_CLEAR_TRUSTED_NAMES = 304;
     */
    P_KRS_BT_CLEAR_TRUSTED_NAMES = 304,
    /**
     * Адрес хоста FTP
     *
     * @generated from protobuf enum value: P_FTP_HOST_ADDRESS = 400;
     */
    P_FTP_HOST_ADDRESS = 400,
    /**
     * Порт хоста FTP
     *
     * @generated from protobuf enum value: P_FTP_HOST_PORT = 401;
     */
    P_FTP_HOST_PORT = 401,
    /**
     * Имя пользователя FTP
     *
     * @generated from protobuf enum value: P_FTP_USERNAME = 402;
     */
    P_FTP_USERNAME = 402,
    /**
     * Пароль пользователя FTP
     *
     * @generated from protobuf enum value: P_FTP_PASSWORD = 403;
     */
    P_FTP_PASSWORD = 403,
    /**
     * Управление загрузкой фотографий льготников: 0 - не загружать, != 0 загрузить
     *
     * @generated from protobuf enum value: P_LOAD_PHOTOS = 500;
     */
    P_LOAD_PHOTOS = 500,
    /**
     * Сколько может быть открыта смена времени (в ms)
     *
     * @generated from protobuf enum value: P_MAX_OPEN_SHIFT_TIME_MS = 600;
     */
    P_MAX_OPEN_SHIFT_TIME_MS = 600,
    /**
     * Максимальное отклонение времени терминала от времени сервера в меньшую сторону
     *
     * @generated from protobuf enum value: P_MAX_FROM_INACCURACY_SERVER_TIME_MS = 601;
     */
    P_MAX_FROM_INACCURACY_SERVER_TIME_MS = 601,
    /**
     * Максимальное отклонение времени терминала от времени сервера в большую сторону
     *
     * @generated from protobuf enum value: P_MAX_BEFORE_INACCURACY_SERVER_TIME_MS = 602;
     */
    P_MAX_BEFORE_INACCURACY_SERVER_TIME_MS = 602,
    /**
     * Включить чек-ин / чек-аут
     *
     * @generated from protobuf enum value: P_IS_CHECK_IN_CHECK_OUT_ENABLE = 700;
     */
    P_IS_CHECK_IN_CHECK_OUT_ENABLE = 700,
    /**
     * Списывать максимум при чек-ин / чек-ауте
     *
     * @generated from protobuf enum value: P_IS_CHECK_IN_CHECK_OUT_PAYMENT_MAX = 701;
     */
    P_IS_CHECK_IN_CHECK_OUT_PAYMENT_MAX = 701
}
/**
 * Тип данных операции
 *
 * @generated from protobuf enum ru.sbertroika.common.tms.OperationRawFormat
 */
export enum OperationRawFormat {
    /**
     * Сырые данные упакованные в Base64
     *
     * @generated from protobuf enum value: ORF_CB64 = 0;
     */
    ORF_CB64 = 0
}
