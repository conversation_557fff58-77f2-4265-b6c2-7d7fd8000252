// @generated by protobuf-ts 2.9.3
// @generated from protobuf file "common.proto" (package "ru.sbertroika.common.v1", syntax proto3)
// tslint:disable
import type { BinaryWriteOptions } from "@protobuf-ts/runtime";
import type { IBinaryWriter } from "@protobuf-ts/runtime";
import { WireType } from "@protobuf-ts/runtime";
import type { BinaryReadOptions } from "@protobuf-ts/runtime";
import type { IBinaryReader } from "@protobuf-ts/runtime";
import { UnknownFieldHandler } from "@protobuf-ts/runtime";
import type { PartialMessage } from "@protobuf-ts/runtime";
import { reflectionMergePartial } from "@protobuf-ts/runtime";
import { MessageType } from "@protobuf-ts/runtime";
import { Timestamp } from "./google/protobuf/timestamp";
import { Empty } from "./google/protobuf/empty";
/**
 * @generated from protobuf message ru.sbertroika.common.v1.OperationError
 */
export interface OperationError {
    /**
     * @generated from protobuf field: ru.sbertroika.common.v1.ErrorType type = 1;
     */
    type: ErrorType;
    /**
     * @generated from protobuf field: string message = 2;
     */
    message: string;
    /**
     * @generated from protobuf field: int32 code = 3;
     */
    code: number;
}
/**
 * @generated from protobuf message ru.sbertroika.common.v1.PaginationRequest
 */
export interface PaginationRequest {
    /**
     * @generated from protobuf field: int32 page = 1;
     */
    page: number;
    /**
     * @generated from protobuf field: int32 limit = 2;
     */
    limit: number;
}
/**
 * @generated from protobuf message ru.sbertroika.common.v1.PaginationResponse
 */
export interface PaginationResponse {
    /**
     * @generated from protobuf field: int32 page = 1;
     */
    page: number;
    /**
     * @generated from protobuf field: int32 limit = 2;
     */
    limit: number;
    /**
     * @generated from protobuf field: int32 totalPage = 3;
     */
    totalPage: number;
    /**
     * @generated from protobuf field: int32 totalCount = 4;
     */
    totalCount: number;
}
/**
 * @generated from protobuf message ru.sbertroika.common.v1.PaymentMethod
 */
export interface PaymentMethod {
    /**
     * @generated from protobuf field: string paymentId = 1;
     */
    paymentId: string; // Идентификатор способа оплаты
    /**
     * @generated from protobuf field: ru.sbertroika.common.v1.PaymentType type = 2;
     */
    type: PaymentType; // Способ оплаты
    /**
     * @generated from protobuf field: string name = 3;
     */
    name: string; // Наименовани способа оплаты
    /**
     * @generated from protobuf field: string label = 4;
     */
    label: string; // Лэйбл способа оплаты
}
/**
 * @generated from protobuf message ru.sbertroika.common.v1.Card
 */
export interface Card {
    /**
     * @generated from protobuf field: string cardNum = 1;
     */
    cardNum: string; // PAN (example: ****************)
    /**
     * @generated from protobuf field: string cardDate = 2;
     */
    cardDate: string; // Дата окончания срока действия карты month/year (example: 11/24)
    /**
     * @generated from protobuf field: string cardHolder = 3;
     */
    cardHolder: string; // Держатель карты (example: IVAN IVANOV)
    /**
     * @generated from protobuf field: int32 cvc = 4;
     */
    cvc: number; // CVV/CVC (example: 123)
}
/**
 * @generated from protobuf message ru.sbertroika.common.v1.Sorted
 */
export interface Sorted {
    /**
     * @generated from protobuf field: string column = 1;
     */
    column: string; // название колонки
    /**
     * @generated from protobuf field: ru.sbertroika.common.v1.SortedType type = 2;
     */
    type: SortedType; // тип сортировки
}
/**
 * @generated from protobuf message ru.sbertroika.common.v1.Filter
 */
export interface Filter {
    /**
     * @generated from protobuf field: string column = 1;
     */
    column: string; // название колонки
    /**
     * @generated from protobuf field: string value = 2;
     */
    value: string; // значение фильтранции
}
/**
 * @generated from protobuf message ru.sbertroika.common.v1.Position
 */
export interface Position {
    /**
     * @generated from protobuf field: double latitude = 1;
     */
    latitude: number;
    /**
     * @generated from protobuf field: double longitude = 2;
     */
    longitude: number;
}
/**
 * @generated from protobuf message ru.sbertroika.common.v1.EmptyResponse
 */
export interface EmptyResponse {
    /**
     * @generated from protobuf oneof: response
     */
    response: {
        oneofKind: "error";
        /**
         * @generated from protobuf field: ru.sbertroika.common.v1.OperationError error = 1;
         */
        error: OperationError;
    } | {
        oneofKind: "empty";
        /**
         * @generated from protobuf field: google.protobuf.Empty empty = 2;
         */
        empty: Empty;
    } | {
        oneofKind: undefined;
    };
}
/**
 * @generated from protobuf message ru.sbertroika.common.v1.CreateResponse
 */
export interface CreateResponse {
    /**
     * @generated from protobuf oneof: response
     */
    response: {
        oneofKind: "error";
        /**
         * @generated from protobuf field: ru.sbertroika.common.v1.OperationError error = 1;
         */
        error: OperationError;
    } | {
        oneofKind: "id";
        /**
         * @generated from protobuf field: string id = 2;
         */
        id: string;
    } | {
        oneofKind: undefined;
    };
}
/**
 * @generated from protobuf message ru.sbertroika.common.v1.HistoryChange
 */
export interface HistoryChange {
    /**
     * @generated from protobuf field: string field = 1;
     */
    field: string;
    /**
     * @generated from protobuf field: optional string oldValue = 2;
     */
    oldValue?: string;
    /**
     * @generated from protobuf field: optional string value = 3;
     */
    value?: string;
}
/**
 * @generated from protobuf message ru.sbertroika.common.v1.History
 */
export interface History {
    /**
     * @generated from protobuf field: int64 version = 1;
     */
    version: bigint;
    /**
     * @generated from protobuf field: string versionCreateBy = 2;
     */
    versionCreateBy: string;
    /**
     * @generated from protobuf field: google.protobuf.Timestamp versionCreateAt = 3;
     */
    versionCreateAt?: Timestamp;
    /**
     * @generated from protobuf field: repeated ru.sbertroika.common.v1.HistoryChange change = 4;
     */
    change: HistoryChange[];
    /**
     * @generated from protobuf field: optional ru.sbertroika.common.v1.ModelStatus status = 5;
     */
    status?: ModelStatus;
}
/**
 * @generated from protobuf message ru.sbertroika.common.v1.HistoryResult
 */
export interface HistoryResult {
    /**
     * @generated from protobuf field: optional ru.sbertroika.common.v1.PaginationResponse pagination = 1;
     */
    pagination?: PaginationResponse;
    /**
     * @generated from protobuf field: repeated ru.sbertroika.common.v1.History history = 2;
     */
    history: History[];
}
/**
 * @generated from protobuf message ru.sbertroika.common.v1.HistoryResponse
 */
export interface HistoryResponse {
    /**
     * @generated from protobuf oneof: response
     */
    response: {
        oneofKind: "error";
        /**
         * @generated from protobuf field: ru.sbertroika.common.v1.OperationError error = 1;
         */
        error: OperationError;
    } | {
        oneofKind: "result";
        /**
         * @generated from protobuf field: ru.sbertroika.common.v1.HistoryResult result = 2;
         */
        result: HistoryResult;
    } | {
        oneofKind: undefined;
    };
}
/**
 * @generated from protobuf message ru.sbertroika.common.v1.ByIdRequest
 */
export interface ByIdRequest {
    /**
     * @generated from protobuf field: string id = 1;
     */
    id: string;
}
/**
 * @generated from protobuf enum ru.sbertroika.common.v1.ErrorType
 */
export enum ErrorType {
    /**
     * @generated from protobuf enum value: UNKNOWN_ERROR = 0;
     */
    UNKNOWN_ERROR = 0,
    /**
     * Сервис временно не доступен
     *
     * @generated from protobuf enum value: SERVICE_ERROR = 1;
     */
    SERVICE_ERROR = 1,
    /**
     * Операция не поддерживается
     *
     * @generated from protobuf enum value: UNSUPPORTED_OPERATION = 2;
     */
    UNSUPPORTED_OPERATION = 2,
    /**
     * Неправильно сформирован запрос
     *
     * @generated from protobuf enum value: BAD_REQUEST = 3;
     */
    BAD_REQUEST = 3,
    /**
     * Ошибка авторизации
     *
     * @generated from protobuf enum value: AUTHENTICATION_ERROR = 4;
     */
    AUTHENTICATION_ERROR = 4,
    /**
     * Запрашиваемый объект отсутсвует
     *
     * @generated from protobuf enum value: NOT_FOUND = 5;
     */
    NOT_FOUND = 5
}
/**
 * @generated from protobuf enum ru.sbertroika.common.v1.TransportType
 */
export enum TransportType {
    /**
     * Автобус
     *
     * @generated from protobuf enum value: BUS = 0;
     */
    BUS = 0,
    /**
     * Троллейбус
     *
     * @generated from protobuf enum value: TROLLEYBUS = 1;
     */
    TROLLEYBUS = 1,
    /**
     * Трамвай
     *
     * @generated from protobuf enum value: TRAM = 2;
     */
    TRAM = 2,
    /**
     * Метро
     *
     * @generated from protobuf enum value: METRO = 3;
     */
    METRO = 3
}
/**
 * @generated from protobuf enum ru.sbertroika.common.v1.OperationStatus
 */
export enum OperationStatus {
    /**
     * Проезд зафиксирован но транзакция еще не авторизовалась
     *
     * @generated from protobuf enum value: NEW = 0;
     */
    NEW = 0,
    /**
     * Успешная авторизация
     *
     * @generated from protobuf enum value: SUCCESS = 1;
     */
    SUCCESS = 1,
    /**
     * Отказ в аторизации
     *
     * @generated from protobuf enum value: FAILED = 2;
     */
    FAILED = 2
}
/**
 * @generated from protobuf enum ru.sbertroika.common.v1.AbonementType
 */
export enum AbonementType {
    /**
     * Кошелек
     *
     * @generated from protobuf enum value: WALLET = 0;
     */
    WALLET = 0,
    /**
     * Поездочный
     *
     * @generated from protobuf enum value: TRAVEL = 1;
     */
    TRAVEL = 1,
    /**
     * Безлимитный
     *
     * @generated from protobuf enum value: UNLIMITED = 2;
     */
    UNLIMITED = 2
}
/**
 * @generated from protobuf enum ru.sbertroika.common.v1.SubscriptionCounterType
 */
export enum SubscriptionCounterType {
    /**
     * Один счетчик на все виды транспорта
     *
     * @generated from protobuf enum value: SCT_ALL = 0;
     */
    SCT_ALL = 0,
    /**
     * Один счетчик на все разрешенные виды транспорта
     *
     * @generated from protobuf enum value: SCT_ALLOW_LIST = 1;
     */
    SCT_ALLOW_LIST = 1,
    /**
     * Для каждого перечисленного вида транспорта свой счетчик
     *
     * @generated from protobuf enum value: SCT_SINGLE = 2;
     */
    SCT_SINGLE = 2,
    /**
     * Неограниченное число поездок
     *
     * @generated from protobuf enum value: SCT_ALL_UNLIMITED = 3;
     */
    SCT_ALL_UNLIMITED = 3,
    /**
     * Неограниченное число поездок на все разрешенные виды транспорта
     *
     * @generated from protobuf enum value: SCT_ALLOW_LIST_UNLIMITED = 4;
     */
    SCT_ALLOW_LIST_UNLIMITED = 4
}
/**
 * @generated from protobuf enum ru.sbertroika.common.v1.PaymentType
 */
export enum PaymentType {
    /**
     * Банковская карта
     *
     * @generated from protobuf enum value: EMV = 0;
     */
    EMV = 0,
    /**
     * Привязанная к аккаунту карта
     *
     * @generated from protobuf enum value: CARRIER = 1;
     */
    CARRIER = 1,
    /**
     * Сбер Пэй
     *
     * @generated from protobuf enum value: SBER_PAY = 2;
     */
    SBER_PAY = 2,
    /**
     * СБП
     *
     * @generated from protobuf enum value: SBP = 3;
     */
    SBP = 3,
    /**
     * Привязанный способ оплаты
     *
     * @generated from protobuf enum value: BINDING = 4;
     */
    BINDING = 4
}
/**
 * @generated from protobuf enum ru.sbertroika.common.v1.ProlongType
 */
export enum ProlongType {
    /**
     * Заданный период
     *
     * @generated from protobuf enum value: PT_PERIOD = 0;
     */
    PT_PERIOD = 0,
    /**
     * С даты окончания билета
     *
     * @generated from protobuf enum value: PT_END_DATE = 1;
     */
    PT_END_DATE = 1,
    /**
     * C даты активации
     *
     * @generated from protobuf enum value: PT_ACTIVE_DATE = 2;
     */
    PT_ACTIVE_DATE = 2
}
/**
 * @generated from protobuf enum ru.sbertroika.common.v1.PaymentAttribute
 */
export enum PaymentAttribute {
    /**
     * Тип платежной системы
     *
     * @generated from protobuf enum value: PAYMENT_TYPE = 0;
     */
    PAYMENT_TYPE = 0,
    /**
     * Идентификатор клиента (сервис, который принял платеж)
     *
     * @generated from protobuf enum value: CLIENT_ID = 1;
     */
    CLIENT_ID = 1,
    /**
     * Идентификатор пользователя
     *
     * @generated from protobuf enum value: USER_ID = 3;
     */
    USER_ID = 3,
    /**
     * Идентификатор платежа
     *
     * @generated from protobuf enum value: ORDER_ID = 4;
     */
    ORDER_ID = 4,
    /**
     * Дата/время совершения платежа
     *
     * @generated from protobuf enum value: PAYMENT_DATE = 5;
     */
    PAYMENT_DATE = 5,
    /**
     * Ссылка на которую требуется перенаправить пользователя в случае успешной оплаты
     *
     * @generated from protobuf enum value: RETURN_URL = 6;
     */
    RETURN_URL = 6,
    /**
     * Ссылка на которую требуется перенаправить пользователя в случае неуспешной оплаты
     *
     * @generated from protobuf enum value: FAIL_URL = 7;
     */
    FAIL_URL = 7,
    /**
     * Список позиций в корзине
     *
     * @generated from protobuf enum value: ITEMS = 8;
     */
    ITEMS = 8,
    /**
     * Конфигурация
     *
     * @generated from protobuf enum value: CONFIG = 9;
     */
    CONFIG = 9,
    /**
     * JSON Параметры
     *
     * @generated from protobuf enum value: JSON_PARAMS = 10;
     */
    JSON_PARAMS = 10,
    /**
     * Email пользователя
     *
     * @generated from protobuf enum value: USER_EMAIL = 11;
     */
    USER_EMAIL = 11,
    /**
     * Телефон пользователя
     *
     * @generated from protobuf enum value: USER_PHONE = 12;
     */
    USER_PHONE = 12
}
/**
 * @generated from protobuf enum ru.sbertroika.common.v1.AuthType
 */
export enum AuthType {
    /**
     * @generated from protobuf enum value: OPEN_ID = 0;
     */
    OPEN_ID = 0,
    /**
     * @generated from protobuf enum value: MTLS = 1;
     */
    MTLS = 1
}
/**
 * @generated from protobuf enum ru.sbertroika.common.v1.SortedType
 */
export enum SortedType {
    /**
     * @generated from protobuf enum value: ASC = 0;
     */
    ASC = 0,
    /**
     * @generated from protobuf enum value: DESC = 1;
     */
    DESC = 1
}
/**
 * @generated from protobuf enum ru.sbertroika.common.v1.ConstraintType
 */
export enum ConstraintType {
    /**
     * @generated from protobuf enum value: TARIFF = 0;
     */
    TARIFF = 0,
    /**
     * @generated from protobuf enum value: ROUTE = 1;
     */
    ROUTE = 1,
    /**
     * @generated from protobuf enum value: TRANSPORT = 2;
     */
    TRANSPORT = 2,
    /**
     * @generated from protobuf enum value: SERVICE = 3;
     */
    SERVICE = 3,
    /**
     * @generated from protobuf enum value: ORGANIZATION = 4;
     */
    ORGANIZATION = 4
}
/**
 * @generated from protobuf enum ru.sbertroika.common.v1.ConstraintBaseRule
 */
export enum ConstraintBaseRule {
    /**
     * @generated from protobuf enum value: ALLOW = 0;
     */
    ALLOW = 0,
    /**
     * @generated from protobuf enum value: DENY = 1;
     */
    DENY = 1
}
/**
 * @generated from protobuf enum ru.sbertroika.common.v1.RouteScheme
 */
export enum RouteScheme {
    /**
     * @generated from protobuf enum value: DIRECTIONAL = 0;
     */
    DIRECTIONAL = 0,
    /**
     * @generated from protobuf enum value: CIRCLE = 1;
     */
    CIRCLE = 1
}
/**
 * @generated from protobuf enum ru.sbertroika.common.v1.ModelStatus
 */
export enum ModelStatus {
    /**
     * @generated from protobuf enum value: ACTIVE = 0;
     */
    ACTIVE = 0,
    /**
     * @generated from protobuf enum value: DISABLED = 1;
     */
    DISABLED = 1,
    /**
     * @generated from protobuf enum value: BLOCKED = 2;
     */
    BLOCKED = 2,
    /**
     * @generated from protobuf enum value: IS_DELETED = 3;
     */
    IS_DELETED = 3
}
// @generated message type with reflection information, may provide speed optimized methods
class OperationError$Type extends MessageType<OperationError> {
    constructor() {
        super("ru.sbertroika.common.v1.OperationError", [
            { no: 1, name: "type", kind: "enum", T: () => ["ru.sbertroika.common.v1.ErrorType", ErrorType] },
            { no: 2, name: "message", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 3, name: "code", kind: "scalar", T: 5 /*ScalarType.INT32*/ }
        ]);
    }
    create(value?: PartialMessage<OperationError>): OperationError {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.type = 0;
        message.message = "";
        message.code = 0;
        if (value !== undefined)
            reflectionMergePartial<OperationError>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: OperationError): OperationError {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* ru.sbertroika.common.v1.ErrorType type */ 1:
                    message.type = reader.int32();
                    break;
                case /* string message */ 2:
                    message.message = reader.string();
                    break;
                case /* int32 code */ 3:
                    message.code = reader.int32();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: OperationError, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* ru.sbertroika.common.v1.ErrorType type = 1; */
        if (message.type !== 0)
            writer.tag(1, WireType.Varint).int32(message.type);
        /* string message = 2; */
        if (message.message !== "")
            writer.tag(2, WireType.LengthDelimited).string(message.message);
        /* int32 code = 3; */
        if (message.code !== 0)
            writer.tag(3, WireType.Varint).int32(message.code);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.common.v1.OperationError
 */
export const OperationError = new OperationError$Type();
// @generated message type with reflection information, may provide speed optimized methods
class PaginationRequest$Type extends MessageType<PaginationRequest> {
    constructor() {
        super("ru.sbertroika.common.v1.PaginationRequest", [
            { no: 1, name: "page", kind: "scalar", T: 5 /*ScalarType.INT32*/ },
            { no: 2, name: "limit", kind: "scalar", T: 5 /*ScalarType.INT32*/ }
        ]);
    }
    create(value?: PartialMessage<PaginationRequest>): PaginationRequest {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.page = 0;
        message.limit = 0;
        if (value !== undefined)
            reflectionMergePartial<PaginationRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: PaginationRequest): PaginationRequest {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* int32 page */ 1:
                    message.page = reader.int32();
                    break;
                case /* int32 limit */ 2:
                    message.limit = reader.int32();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: PaginationRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* int32 page = 1; */
        if (message.page !== 0)
            writer.tag(1, WireType.Varint).int32(message.page);
        /* int32 limit = 2; */
        if (message.limit !== 0)
            writer.tag(2, WireType.Varint).int32(message.limit);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.common.v1.PaginationRequest
 */
export const PaginationRequest = new PaginationRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class PaginationResponse$Type extends MessageType<PaginationResponse> {
    constructor() {
        super("ru.sbertroika.common.v1.PaginationResponse", [
            { no: 1, name: "page", kind: "scalar", T: 5 /*ScalarType.INT32*/ },
            { no: 2, name: "limit", kind: "scalar", T: 5 /*ScalarType.INT32*/ },
            { no: 3, name: "totalPage", kind: "scalar", T: 5 /*ScalarType.INT32*/ },
            { no: 4, name: "totalCount", kind: "scalar", T: 5 /*ScalarType.INT32*/ }
        ]);
    }
    create(value?: PartialMessage<PaginationResponse>): PaginationResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.page = 0;
        message.limit = 0;
        message.totalPage = 0;
        message.totalCount = 0;
        if (value !== undefined)
            reflectionMergePartial<PaginationResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: PaginationResponse): PaginationResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* int32 page */ 1:
                    message.page = reader.int32();
                    break;
                case /* int32 limit */ 2:
                    message.limit = reader.int32();
                    break;
                case /* int32 totalPage */ 3:
                    message.totalPage = reader.int32();
                    break;
                case /* int32 totalCount */ 4:
                    message.totalCount = reader.int32();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: PaginationResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* int32 page = 1; */
        if (message.page !== 0)
            writer.tag(1, WireType.Varint).int32(message.page);
        /* int32 limit = 2; */
        if (message.limit !== 0)
            writer.tag(2, WireType.Varint).int32(message.limit);
        /* int32 totalPage = 3; */
        if (message.totalPage !== 0)
            writer.tag(3, WireType.Varint).int32(message.totalPage);
        /* int32 totalCount = 4; */
        if (message.totalCount !== 0)
            writer.tag(4, WireType.Varint).int32(message.totalCount);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.common.v1.PaginationResponse
 */
export const PaginationResponse = new PaginationResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class PaymentMethod$Type extends MessageType<PaymentMethod> {
    constructor() {
        super("ru.sbertroika.common.v1.PaymentMethod", [
            { no: 1, name: "paymentId", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "type", kind: "enum", T: () => ["ru.sbertroika.common.v1.PaymentType", PaymentType] },
            { no: 3, name: "name", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 4, name: "label", kind: "scalar", T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<PaymentMethod>): PaymentMethod {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.paymentId = "";
        message.type = 0;
        message.name = "";
        message.label = "";
        if (value !== undefined)
            reflectionMergePartial<PaymentMethod>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: PaymentMethod): PaymentMethod {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* string paymentId */ 1:
                    message.paymentId = reader.string();
                    break;
                case /* ru.sbertroika.common.v1.PaymentType type */ 2:
                    message.type = reader.int32();
                    break;
                case /* string name */ 3:
                    message.name = reader.string();
                    break;
                case /* string label */ 4:
                    message.label = reader.string();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: PaymentMethod, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* string paymentId = 1; */
        if (message.paymentId !== "")
            writer.tag(1, WireType.LengthDelimited).string(message.paymentId);
        /* ru.sbertroika.common.v1.PaymentType type = 2; */
        if (message.type !== 0)
            writer.tag(2, WireType.Varint).int32(message.type);
        /* string name = 3; */
        if (message.name !== "")
            writer.tag(3, WireType.LengthDelimited).string(message.name);
        /* string label = 4; */
        if (message.label !== "")
            writer.tag(4, WireType.LengthDelimited).string(message.label);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.common.v1.PaymentMethod
 */
export const PaymentMethod = new PaymentMethod$Type();
// @generated message type with reflection information, may provide speed optimized methods
class Card$Type extends MessageType<Card> {
    constructor() {
        super("ru.sbertroika.common.v1.Card", [
            { no: 1, name: "cardNum", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "cardDate", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 3, name: "cardHolder", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 4, name: "cvc", kind: "scalar", T: 5 /*ScalarType.INT32*/ }
        ]);
    }
    create(value?: PartialMessage<Card>): Card {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.cardNum = "";
        message.cardDate = "";
        message.cardHolder = "";
        message.cvc = 0;
        if (value !== undefined)
            reflectionMergePartial<Card>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: Card): Card {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* string cardNum */ 1:
                    message.cardNum = reader.string();
                    break;
                case /* string cardDate */ 2:
                    message.cardDate = reader.string();
                    break;
                case /* string cardHolder */ 3:
                    message.cardHolder = reader.string();
                    break;
                case /* int32 cvc */ 4:
                    message.cvc = reader.int32();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: Card, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* string cardNum = 1; */
        if (message.cardNum !== "")
            writer.tag(1, WireType.LengthDelimited).string(message.cardNum);
        /* string cardDate = 2; */
        if (message.cardDate !== "")
            writer.tag(2, WireType.LengthDelimited).string(message.cardDate);
        /* string cardHolder = 3; */
        if (message.cardHolder !== "")
            writer.tag(3, WireType.LengthDelimited).string(message.cardHolder);
        /* int32 cvc = 4; */
        if (message.cvc !== 0)
            writer.tag(4, WireType.Varint).int32(message.cvc);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.common.v1.Card
 */
export const Card = new Card$Type();
// @generated message type with reflection information, may provide speed optimized methods
class Sorted$Type extends MessageType<Sorted> {
    constructor() {
        super("ru.sbertroika.common.v1.Sorted", [
            { no: 1, name: "column", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "type", kind: "enum", T: () => ["ru.sbertroika.common.v1.SortedType", SortedType] }
        ]);
    }
    create(value?: PartialMessage<Sorted>): Sorted {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.column = "";
        message.type = 0;
        if (value !== undefined)
            reflectionMergePartial<Sorted>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: Sorted): Sorted {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* string column */ 1:
                    message.column = reader.string();
                    break;
                case /* ru.sbertroika.common.v1.SortedType type */ 2:
                    message.type = reader.int32();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: Sorted, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* string column = 1; */
        if (message.column !== "")
            writer.tag(1, WireType.LengthDelimited).string(message.column);
        /* ru.sbertroika.common.v1.SortedType type = 2; */
        if (message.type !== 0)
            writer.tag(2, WireType.Varint).int32(message.type);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.common.v1.Sorted
 */
export const Sorted = new Sorted$Type();
// @generated message type with reflection information, may provide speed optimized methods
class Filter$Type extends MessageType<Filter> {
    constructor() {
        super("ru.sbertroika.common.v1.Filter", [
            { no: 1, name: "column", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "value", kind: "scalar", T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<Filter>): Filter {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.column = "";
        message.value = "";
        if (value !== undefined)
            reflectionMergePartial<Filter>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: Filter): Filter {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* string column */ 1:
                    message.column = reader.string();
                    break;
                case /* string value */ 2:
                    message.value = reader.string();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: Filter, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* string column = 1; */
        if (message.column !== "")
            writer.tag(1, WireType.LengthDelimited).string(message.column);
        /* string value = 2; */
        if (message.value !== "")
            writer.tag(2, WireType.LengthDelimited).string(message.value);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.common.v1.Filter
 */
export const Filter = new Filter$Type();
// @generated message type with reflection information, may provide speed optimized methods
class Position$Type extends MessageType<Position> {
    constructor() {
        super("ru.sbertroika.common.v1.Position", [
            { no: 1, name: "latitude", kind: "scalar", T: 1 /*ScalarType.DOUBLE*/ },
            { no: 2, name: "longitude", kind: "scalar", T: 1 /*ScalarType.DOUBLE*/ }
        ]);
    }
    create(value?: PartialMessage<Position>): Position {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.latitude = 0;
        message.longitude = 0;
        if (value !== undefined)
            reflectionMergePartial<Position>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: Position): Position {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* double latitude */ 1:
                    message.latitude = reader.double();
                    break;
                case /* double longitude */ 2:
                    message.longitude = reader.double();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: Position, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* double latitude = 1; */
        if (message.latitude !== 0)
            writer.tag(1, WireType.Bit64).double(message.latitude);
        /* double longitude = 2; */
        if (message.longitude !== 0)
            writer.tag(2, WireType.Bit64).double(message.longitude);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.common.v1.Position
 */
export const Position = new Position$Type();
// @generated message type with reflection information, may provide speed optimized methods
class EmptyResponse$Type extends MessageType<EmptyResponse> {
    constructor() {
        super("ru.sbertroika.common.v1.EmptyResponse", [
            { no: 1, name: "error", kind: "message", oneof: "response", T: () => OperationError },
            { no: 2, name: "empty", kind: "message", oneof: "response", T: () => Empty }
        ]);
    }
    create(value?: PartialMessage<EmptyResponse>): EmptyResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.response = { oneofKind: undefined };
        if (value !== undefined)
            reflectionMergePartial<EmptyResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: EmptyResponse): EmptyResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* ru.sbertroika.common.v1.OperationError error */ 1:
                    message.response = {
                        oneofKind: "error",
                        error: OperationError.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).error)
                    };
                    break;
                case /* google.protobuf.Empty empty */ 2:
                    message.response = {
                        oneofKind: "empty",
                        empty: Empty.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).empty)
                    };
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: EmptyResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* ru.sbertroika.common.v1.OperationError error = 1; */
        if (message.response.oneofKind === "error")
            OperationError.internalBinaryWrite(message.response.error, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* google.protobuf.Empty empty = 2; */
        if (message.response.oneofKind === "empty")
            Empty.internalBinaryWrite(message.response.empty, writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.common.v1.EmptyResponse
 */
export const EmptyResponse = new EmptyResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class CreateResponse$Type extends MessageType<CreateResponse> {
    constructor() {
        super("ru.sbertroika.common.v1.CreateResponse", [
            { no: 1, name: "error", kind: "message", oneof: "response", T: () => OperationError },
            { no: 2, name: "id", kind: "scalar", oneof: "response", T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<CreateResponse>): CreateResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.response = { oneofKind: undefined };
        if (value !== undefined)
            reflectionMergePartial<CreateResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: CreateResponse): CreateResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* ru.sbertroika.common.v1.OperationError error */ 1:
                    message.response = {
                        oneofKind: "error",
                        error: OperationError.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).error)
                    };
                    break;
                case /* string id */ 2:
                    message.response = {
                        oneofKind: "id",
                        id: reader.string()
                    };
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: CreateResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* ru.sbertroika.common.v1.OperationError error = 1; */
        if (message.response.oneofKind === "error")
            OperationError.internalBinaryWrite(message.response.error, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* string id = 2; */
        if (message.response.oneofKind === "id")
            writer.tag(2, WireType.LengthDelimited).string(message.response.id);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.common.v1.CreateResponse
 */
export const CreateResponse = new CreateResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class HistoryChange$Type extends MessageType<HistoryChange> {
    constructor() {
        super("ru.sbertroika.common.v1.HistoryChange", [
            { no: 1, name: "field", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "oldValue", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 3, name: "value", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<HistoryChange>): HistoryChange {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.field = "";
        if (value !== undefined)
            reflectionMergePartial<HistoryChange>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: HistoryChange): HistoryChange {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* string field */ 1:
                    message.field = reader.string();
                    break;
                case /* optional string oldValue */ 2:
                    message.oldValue = reader.string();
                    break;
                case /* optional string value */ 3:
                    message.value = reader.string();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: HistoryChange, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* string field = 1; */
        if (message.field !== "")
            writer.tag(1, WireType.LengthDelimited).string(message.field);
        /* optional string oldValue = 2; */
        if (message.oldValue !== undefined)
            writer.tag(2, WireType.LengthDelimited).string(message.oldValue);
        /* optional string value = 3; */
        if (message.value !== undefined)
            writer.tag(3, WireType.LengthDelimited).string(message.value);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.common.v1.HistoryChange
 */
export const HistoryChange = new HistoryChange$Type();
// @generated message type with reflection information, may provide speed optimized methods
class History$Type extends MessageType<History> {
    constructor() {
        super("ru.sbertroika.common.v1.History", [
            { no: 1, name: "version", kind: "scalar", T: 3 /*ScalarType.INT64*/, L: 0 /*LongType.BIGINT*/ },
            { no: 2, name: "versionCreateBy", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 3, name: "versionCreateAt", kind: "message", T: () => Timestamp },
            { no: 4, name: "change", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => HistoryChange },
            { no: 5, name: "status", kind: "enum", opt: true, T: () => ["ru.sbertroika.common.v1.ModelStatus", ModelStatus] }
        ]);
    }
    create(value?: PartialMessage<History>): History {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.version = 0n;
        message.versionCreateBy = "";
        message.change = [];
        if (value !== undefined)
            reflectionMergePartial<History>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: History): History {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* int64 version */ 1:
                    message.version = reader.int64().toBigInt();
                    break;
                case /* string versionCreateBy */ 2:
                    message.versionCreateBy = reader.string();
                    break;
                case /* google.protobuf.Timestamp versionCreateAt */ 3:
                    message.versionCreateAt = Timestamp.internalBinaryRead(reader, reader.uint32(), options, message.versionCreateAt);
                    break;
                case /* repeated ru.sbertroika.common.v1.HistoryChange change */ 4:
                    message.change.push(HistoryChange.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                case /* optional ru.sbertroika.common.v1.ModelStatus status */ 5:
                    message.status = reader.int32();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: History, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* int64 version = 1; */
        if (message.version !== 0n)
            writer.tag(1, WireType.Varint).int64(message.version);
        /* string versionCreateBy = 2; */
        if (message.versionCreateBy !== "")
            writer.tag(2, WireType.LengthDelimited).string(message.versionCreateBy);
        /* google.protobuf.Timestamp versionCreateAt = 3; */
        if (message.versionCreateAt)
            Timestamp.internalBinaryWrite(message.versionCreateAt, writer.tag(3, WireType.LengthDelimited).fork(), options).join();
        /* repeated ru.sbertroika.common.v1.HistoryChange change = 4; */
        for (let i = 0; i < message.change.length; i++)
            HistoryChange.internalBinaryWrite(message.change[i], writer.tag(4, WireType.LengthDelimited).fork(), options).join();
        /* optional ru.sbertroika.common.v1.ModelStatus status = 5; */
        if (message.status !== undefined)
            writer.tag(5, WireType.Varint).int32(message.status);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.common.v1.History
 */
export const History = new History$Type();
// @generated message type with reflection information, may provide speed optimized methods
class HistoryResult$Type extends MessageType<HistoryResult> {
    constructor() {
        super("ru.sbertroika.common.v1.HistoryResult", [
            { no: 1, name: "pagination", kind: "message", T: () => PaginationResponse },
            { no: 2, name: "history", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => History }
        ]);
    }
    create(value?: PartialMessage<HistoryResult>): HistoryResult {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.history = [];
        if (value !== undefined)
            reflectionMergePartial<HistoryResult>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: HistoryResult): HistoryResult {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* optional ru.sbertroika.common.v1.PaginationResponse pagination */ 1:
                    message.pagination = PaginationResponse.internalBinaryRead(reader, reader.uint32(), options, message.pagination);
                    break;
                case /* repeated ru.sbertroika.common.v1.History history */ 2:
                    message.history.push(History.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: HistoryResult, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* optional ru.sbertroika.common.v1.PaginationResponse pagination = 1; */
        if (message.pagination)
            PaginationResponse.internalBinaryWrite(message.pagination, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* repeated ru.sbertroika.common.v1.History history = 2; */
        for (let i = 0; i < message.history.length; i++)
            History.internalBinaryWrite(message.history[i], writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.common.v1.HistoryResult
 */
export const HistoryResult = new HistoryResult$Type();
// @generated message type with reflection information, may provide speed optimized methods
class HistoryResponse$Type extends MessageType<HistoryResponse> {
    constructor() {
        super("ru.sbertroika.common.v1.HistoryResponse", [
            { no: 1, name: "error", kind: "message", oneof: "response", T: () => OperationError },
            { no: 2, name: "result", kind: "message", oneof: "response", T: () => HistoryResult }
        ]);
    }
    create(value?: PartialMessage<HistoryResponse>): HistoryResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.response = { oneofKind: undefined };
        if (value !== undefined)
            reflectionMergePartial<HistoryResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: HistoryResponse): HistoryResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* ru.sbertroika.common.v1.OperationError error */ 1:
                    message.response = {
                        oneofKind: "error",
                        error: OperationError.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).error)
                    };
                    break;
                case /* ru.sbertroika.common.v1.HistoryResult result */ 2:
                    message.response = {
                        oneofKind: "result",
                        result: HistoryResult.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).result)
                    };
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: HistoryResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* ru.sbertroika.common.v1.OperationError error = 1; */
        if (message.response.oneofKind === "error")
            OperationError.internalBinaryWrite(message.response.error, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* ru.sbertroika.common.v1.HistoryResult result = 2; */
        if (message.response.oneofKind === "result")
            HistoryResult.internalBinaryWrite(message.response.result, writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.common.v1.HistoryResponse
 */
export const HistoryResponse = new HistoryResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ByIdRequest$Type extends MessageType<ByIdRequest> {
    constructor() {
        super("ru.sbertroika.common.v1.ByIdRequest", [
            { no: 1, name: "id", kind: "scalar", T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<ByIdRequest>): ByIdRequest {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.id = "";
        if (value !== undefined)
            reflectionMergePartial<ByIdRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: ByIdRequest): ByIdRequest {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* string id */ 1:
                    message.id = reader.string();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: ByIdRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* string id = 1; */
        if (message.id !== "")
            writer.tag(1, WireType.LengthDelimited).string(message.id);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.common.v1.ByIdRequest
 */
export const ByIdRequest = new ByIdRequest$Type();
