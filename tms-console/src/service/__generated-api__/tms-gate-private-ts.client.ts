// @generated by protobuf-ts 2.9.3
// @generated from protobuf file "tms-gate-private-ts.proto" (package "ru.sbertroika.tms.gate.v1", syntax proto3)
// tslint:disable
import type { RpcTransport } from "@protobuf-ts/runtime-rpc";
import type { ServiceInfo } from "@protobuf-ts/runtime-rpc";
import { TMSGatePrivateTSService } from "./tms-gate-private-ts";
import type { AssignSettingGroupToTerminalProfileRequest } from "./tms-gate-private-ts";
import type { AssignSettingsToTerminalProfileRequest } from "./tms-gate-private-ts";
import type { AssignTemplateSettingsToSettingGroupRequest } from "./tms-gate-private-ts";
import type { TemplateSettingsResponse } from "./tms-gate-private-ts";
import type { TerminalProfileResponse } from "./tms-gate-private-ts";
import type { SettingsGroupResponse } from "./tms-gate-private-ts";
import type { SettingsResponse } from "./tms-gate-private-ts";
import type { ByIdRequest } from "./common";
import type { TemplateSettings } from "./tms-gate-private-ts";
import type { TerminalProfile } from "./tms-gate-private-ts";
import type { SettingsGroup } from "./tms-gate-private-ts";
import type { EmptyResponse } from "./common";
import type { Settings } from "./tms-gate-private-ts";
import type { TemplateSettingsListResponse } from "./tms-gate-private-ts";
import type { TemplateSettingsListRequest } from "./tms-gate-private-ts";
import type { TerminalProfileListResponse } from "./tms-gate-private-ts";
import type { TerminalProfileListRequest } from "./tms-gate-private-ts";
import type { SettingsGroupListResponse } from "./tms-gate-private-ts";
import type { SettingsGroupListRequest } from "./tms-gate-private-ts";
import { stackIntercept } from "@protobuf-ts/runtime-rpc";
import type { SettingsListResponse } from "./tms-gate-private-ts";
import type { SettingsListRequest } from "./tms-gate-private-ts";
import type { UnaryCall } from "@protobuf-ts/runtime-rpc";
import type { RpcOptions } from "@protobuf-ts/runtime-rpc";
/**
 * @generated from protobuf service ru.sbertroika.tms.gate.v1.TMSGatePrivateTSService
 */
export interface ITMSGatePrivateTSServiceClient {
    /**
     * @generated from protobuf rpc: settingsList(ru.sbertroika.tms.gate.v1.SettingsListRequest) returns (ru.sbertroika.tms.gate.v1.SettingsListResponse);
     */
    settingsList(input: SettingsListRequest, options?: RpcOptions): UnaryCall<SettingsListRequest, SettingsListResponse>;
    /**
     * @generated from protobuf rpc: settingsGroupList(ru.sbertroika.tms.gate.v1.SettingsGroupListRequest) returns (ru.sbertroika.tms.gate.v1.SettingsGroupListResponse);
     */
    settingsGroupList(input: SettingsGroupListRequest, options?: RpcOptions): UnaryCall<SettingsGroupListRequest, SettingsGroupListResponse>;
    /**
     * @generated from protobuf rpc: terminalProfileList(ru.sbertroika.tms.gate.v1.TerminalProfileListRequest) returns (ru.sbertroika.tms.gate.v1.TerminalProfileListResponse);
     */
    terminalProfileList(input: TerminalProfileListRequest, options?: RpcOptions): UnaryCall<TerminalProfileListRequest, TerminalProfileListResponse>;
    /**
     * @generated from protobuf rpc: templateSettingsList(ru.sbertroika.tms.gate.v1.TemplateSettingsListRequest) returns (ru.sbertroika.tms.gate.v1.TemplateSettingsListResponse);
     */
    templateSettingsList(input: TemplateSettingsListRequest, options?: RpcOptions): UnaryCall<TemplateSettingsListRequest, TemplateSettingsListResponse>;
    /**
     * @generated from protobuf rpc: createSettings(ru.sbertroika.tms.gate.v1.Settings) returns (ru.sbertroika.common.v1.EmptyResponse);
     */
    createSettings(input: Settings, options?: RpcOptions): UnaryCall<Settings, EmptyResponse>;
    /**
     * @generated from protobuf rpc: createSettingsGroup(ru.sbertroika.tms.gate.v1.SettingsGroup) returns (ru.sbertroika.common.v1.EmptyResponse);
     */
    createSettingsGroup(input: SettingsGroup, options?: RpcOptions): UnaryCall<SettingsGroup, EmptyResponse>;
    /**
     * @generated from protobuf rpc: createTerminalProfile(ru.sbertroika.tms.gate.v1.TerminalProfile) returns (ru.sbertroika.common.v1.EmptyResponse);
     */
    createTerminalProfile(input: TerminalProfile, options?: RpcOptions): UnaryCall<TerminalProfile, EmptyResponse>;
    /**
     * @generated from protobuf rpc: createTemplateSettings(ru.sbertroika.tms.gate.v1.TemplateSettings) returns (ru.sbertroika.common.v1.EmptyResponse);
     */
    createTemplateSettings(input: TemplateSettings, options?: RpcOptions): UnaryCall<TemplateSettings, EmptyResponse>;
    /**
     * @generated from protobuf rpc: updateSettings(ru.sbertroika.tms.gate.v1.Settings) returns (ru.sbertroika.common.v1.EmptyResponse);
     */
    updateSettings(input: Settings, options?: RpcOptions): UnaryCall<Settings, EmptyResponse>;
    /**
     * @generated from protobuf rpc: updateSettingsGroup(ru.sbertroika.tms.gate.v1.SettingsGroup) returns (ru.sbertroika.common.v1.EmptyResponse);
     */
    updateSettingsGroup(input: SettingsGroup, options?: RpcOptions): UnaryCall<SettingsGroup, EmptyResponse>;
    /**
     * @generated from protobuf rpc: updateTerminalProfile(ru.sbertroika.tms.gate.v1.TerminalProfile) returns (ru.sbertroika.common.v1.EmptyResponse);
     */
    updateTerminalProfile(input: TerminalProfile, options?: RpcOptions): UnaryCall<TerminalProfile, EmptyResponse>;
    /**
     * @generated from protobuf rpc: updateTemplateSettings(ru.sbertroika.tms.gate.v1.TemplateSettings) returns (ru.sbertroika.common.v1.EmptyResponse);
     */
    updateTemplateSettings(input: TemplateSettings, options?: RpcOptions): UnaryCall<TemplateSettings, EmptyResponse>;
    /**
     * @generated from protobuf rpc: deleteSettings(ru.sbertroika.common.v1.ByIdRequest) returns (ru.sbertroika.common.v1.EmptyResponse);
     */
    deleteSettings(input: ByIdRequest, options?: RpcOptions): UnaryCall<ByIdRequest, EmptyResponse>;
    /**
     * @generated from protobuf rpc: deleteSettingsGroup(ru.sbertroika.common.v1.ByIdRequest) returns (ru.sbertroika.common.v1.EmptyResponse);
     */
    deleteSettingsGroup(input: ByIdRequest, options?: RpcOptions): UnaryCall<ByIdRequest, EmptyResponse>;
    /**
     * @generated from protobuf rpc: deleteTerminalProfile(ru.sbertroika.common.v1.ByIdRequest) returns (ru.sbertroika.common.v1.EmptyResponse);
     */
    deleteTerminalProfile(input: ByIdRequest, options?: RpcOptions): UnaryCall<ByIdRequest, EmptyResponse>;
    /**
     * @generated from protobuf rpc: deleteTemplateSettings(ru.sbertroika.common.v1.ByIdRequest) returns (ru.sbertroika.common.v1.EmptyResponse);
     */
    deleteTemplateSettings(input: ByIdRequest, options?: RpcOptions): UnaryCall<ByIdRequest, EmptyResponse>;
    /**
     * @generated from protobuf rpc: getSettingsById(ru.sbertroika.common.v1.ByIdRequest) returns (ru.sbertroika.tms.gate.v1.SettingsResponse);
     */
    getSettingsById(input: ByIdRequest, options?: RpcOptions): UnaryCall<ByIdRequest, SettingsResponse>;
    /**
     * @generated from protobuf rpc: getSettingsGroupById(ru.sbertroika.common.v1.ByIdRequest) returns (ru.sbertroika.tms.gate.v1.SettingsGroupResponse);
     */
    getSettingsGroupById(input: ByIdRequest, options?: RpcOptions): UnaryCall<ByIdRequest, SettingsGroupResponse>;
    /**
     * @generated from protobuf rpc: getTerminalProfileById(ru.sbertroika.common.v1.ByIdRequest) returns (ru.sbertroika.tms.gate.v1.TerminalProfileResponse);
     */
    getTerminalProfileById(input: ByIdRequest, options?: RpcOptions): UnaryCall<ByIdRequest, TerminalProfileResponse>;
    /**
     * @generated from protobuf rpc: getTemplateSettingsById(ru.sbertroika.common.v1.ByIdRequest) returns (ru.sbertroika.tms.gate.v1.TemplateSettingsResponse);
     */
    getTemplateSettingsById(input: ByIdRequest, options?: RpcOptions): UnaryCall<ByIdRequest, TemplateSettingsResponse>;
    /**
     * @generated from protobuf rpc: getTemplateSettingsForSettingGroup(ru.sbertroika.common.v1.ByIdRequest) returns (ru.sbertroika.tms.gate.v1.TemplateSettingsListResponse);
     */
    getTemplateSettingsForSettingGroup(input: ByIdRequest, options?: RpcOptions): UnaryCall<ByIdRequest, TemplateSettingsListResponse>;
    /**
     * @generated from protobuf rpc: getSettingsForTerminalProfile(ru.sbertroika.common.v1.ByIdRequest) returns (ru.sbertroika.tms.gate.v1.SettingsListResponse);
     */
    getSettingsForTerminalProfile(input: ByIdRequest, options?: RpcOptions): UnaryCall<ByIdRequest, SettingsListResponse>;
    /**
     * @generated from protobuf rpc: getSettingGroupForTerminalProfile(ru.sbertroika.common.v1.ByIdRequest) returns (ru.sbertroika.tms.gate.v1.SettingsGroupListResponse);
     */
    getSettingGroupForTerminalProfile(input: ByIdRequest, options?: RpcOptions): UnaryCall<ByIdRequest, SettingsGroupListResponse>;
    /**
     * @generated from protobuf rpc: assignTemplateSettingsToSettingGroup(ru.sbertroika.tms.gate.v1.AssignTemplateSettingsToSettingGroupRequest) returns (ru.sbertroika.common.v1.EmptyResponse);
     */
    assignTemplateSettingsToSettingGroup(input: AssignTemplateSettingsToSettingGroupRequest, options?: RpcOptions): UnaryCall<AssignTemplateSettingsToSettingGroupRequest, EmptyResponse>;
    /**
     * @generated from protobuf rpc: assignSettingsToTerminalProfile(ru.sbertroika.tms.gate.v1.AssignSettingsToTerminalProfileRequest) returns (ru.sbertroika.common.v1.EmptyResponse);
     */
    assignSettingsToTerminalProfile(input: AssignSettingsToTerminalProfileRequest, options?: RpcOptions): UnaryCall<AssignSettingsToTerminalProfileRequest, EmptyResponse>;
    /**
     * @generated from protobuf rpc: assignSettingGroupToTerminalProfile(ru.sbertroika.tms.gate.v1.AssignSettingGroupToTerminalProfileRequest) returns (ru.sbertroika.common.v1.EmptyResponse);
     */
    assignSettingGroupToTerminalProfile(input: AssignSettingGroupToTerminalProfileRequest, options?: RpcOptions): UnaryCall<AssignSettingGroupToTerminalProfileRequest, EmptyResponse>;
}
/**
 * @generated from protobuf service ru.sbertroika.tms.gate.v1.TMSGatePrivateTSService
 */
export class TMSGatePrivateTSServiceClient implements ITMSGatePrivateTSServiceClient, ServiceInfo {
    typeName = TMSGatePrivateTSService.typeName;
    methods = TMSGatePrivateTSService.methods;
    options = TMSGatePrivateTSService.options;
    constructor(private readonly _transport: RpcTransport) {
    }
    /**
     * @generated from protobuf rpc: settingsList(ru.sbertroika.tms.gate.v1.SettingsListRequest) returns (ru.sbertroika.tms.gate.v1.SettingsListResponse);
     */
    settingsList(input: SettingsListRequest, options?: RpcOptions): UnaryCall<SettingsListRequest, SettingsListResponse> {
        const method = this.methods[0], opt = this._transport.mergeOptions(options);
        return stackIntercept<SettingsListRequest, SettingsListResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: settingsGroupList(ru.sbertroika.tms.gate.v1.SettingsGroupListRequest) returns (ru.sbertroika.tms.gate.v1.SettingsGroupListResponse);
     */
    settingsGroupList(input: SettingsGroupListRequest, options?: RpcOptions): UnaryCall<SettingsGroupListRequest, SettingsGroupListResponse> {
        const method = this.methods[1], opt = this._transport.mergeOptions(options);
        return stackIntercept<SettingsGroupListRequest, SettingsGroupListResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: terminalProfileList(ru.sbertroika.tms.gate.v1.TerminalProfileListRequest) returns (ru.sbertroika.tms.gate.v1.TerminalProfileListResponse);
     */
    terminalProfileList(input: TerminalProfileListRequest, options?: RpcOptions): UnaryCall<TerminalProfileListRequest, TerminalProfileListResponse> {
        const method = this.methods[2], opt = this._transport.mergeOptions(options);
        return stackIntercept<TerminalProfileListRequest, TerminalProfileListResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: templateSettingsList(ru.sbertroika.tms.gate.v1.TemplateSettingsListRequest) returns (ru.sbertroika.tms.gate.v1.TemplateSettingsListResponse);
     */
    templateSettingsList(input: TemplateSettingsListRequest, options?: RpcOptions): UnaryCall<TemplateSettingsListRequest, TemplateSettingsListResponse> {
        const method = this.methods[3], opt = this._transport.mergeOptions(options);
        return stackIntercept<TemplateSettingsListRequest, TemplateSettingsListResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: createSettings(ru.sbertroika.tms.gate.v1.Settings) returns (ru.sbertroika.common.v1.EmptyResponse);
     */
    createSettings(input: Settings, options?: RpcOptions): UnaryCall<Settings, EmptyResponse> {
        const method = this.methods[4], opt = this._transport.mergeOptions(options);
        return stackIntercept<Settings, EmptyResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: createSettingsGroup(ru.sbertroika.tms.gate.v1.SettingsGroup) returns (ru.sbertroika.common.v1.EmptyResponse);
     */
    createSettingsGroup(input: SettingsGroup, options?: RpcOptions): UnaryCall<SettingsGroup, EmptyResponse> {
        const method = this.methods[5], opt = this._transport.mergeOptions(options);
        return stackIntercept<SettingsGroup, EmptyResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: createTerminalProfile(ru.sbertroika.tms.gate.v1.TerminalProfile) returns (ru.sbertroika.common.v1.EmptyResponse);
     */
    createTerminalProfile(input: TerminalProfile, options?: RpcOptions): UnaryCall<TerminalProfile, EmptyResponse> {
        const method = this.methods[6], opt = this._transport.mergeOptions(options);
        return stackIntercept<TerminalProfile, EmptyResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: createTemplateSettings(ru.sbertroika.tms.gate.v1.TemplateSettings) returns (ru.sbertroika.common.v1.EmptyResponse);
     */
    createTemplateSettings(input: TemplateSettings, options?: RpcOptions): UnaryCall<TemplateSettings, EmptyResponse> {
        const method = this.methods[7], opt = this._transport.mergeOptions(options);
        return stackIntercept<TemplateSettings, EmptyResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: updateSettings(ru.sbertroika.tms.gate.v1.Settings) returns (ru.sbertroika.common.v1.EmptyResponse);
     */
    updateSettings(input: Settings, options?: RpcOptions): UnaryCall<Settings, EmptyResponse> {
        const method = this.methods[8], opt = this._transport.mergeOptions(options);
        return stackIntercept<Settings, EmptyResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: updateSettingsGroup(ru.sbertroika.tms.gate.v1.SettingsGroup) returns (ru.sbertroika.common.v1.EmptyResponse);
     */
    updateSettingsGroup(input: SettingsGroup, options?: RpcOptions): UnaryCall<SettingsGroup, EmptyResponse> {
        const method = this.methods[9], opt = this._transport.mergeOptions(options);
        return stackIntercept<SettingsGroup, EmptyResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: updateTerminalProfile(ru.sbertroika.tms.gate.v1.TerminalProfile) returns (ru.sbertroika.common.v1.EmptyResponse);
     */
    updateTerminalProfile(input: TerminalProfile, options?: RpcOptions): UnaryCall<TerminalProfile, EmptyResponse> {
        const method = this.methods[10], opt = this._transport.mergeOptions(options);
        return stackIntercept<TerminalProfile, EmptyResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: updateTemplateSettings(ru.sbertroika.tms.gate.v1.TemplateSettings) returns (ru.sbertroika.common.v1.EmptyResponse);
     */
    updateTemplateSettings(input: TemplateSettings, options?: RpcOptions): UnaryCall<TemplateSettings, EmptyResponse> {
        const method = this.methods[11], opt = this._transport.mergeOptions(options);
        return stackIntercept<TemplateSettings, EmptyResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: deleteSettings(ru.sbertroika.common.v1.ByIdRequest) returns (ru.sbertroika.common.v1.EmptyResponse);
     */
    deleteSettings(input: ByIdRequest, options?: RpcOptions): UnaryCall<ByIdRequest, EmptyResponse> {
        const method = this.methods[12], opt = this._transport.mergeOptions(options);
        return stackIntercept<ByIdRequest, EmptyResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: deleteSettingsGroup(ru.sbertroika.common.v1.ByIdRequest) returns (ru.sbertroika.common.v1.EmptyResponse);
     */
    deleteSettingsGroup(input: ByIdRequest, options?: RpcOptions): UnaryCall<ByIdRequest, EmptyResponse> {
        const method = this.methods[13], opt = this._transport.mergeOptions(options);
        return stackIntercept<ByIdRequest, EmptyResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: deleteTerminalProfile(ru.sbertroika.common.v1.ByIdRequest) returns (ru.sbertroika.common.v1.EmptyResponse);
     */
    deleteTerminalProfile(input: ByIdRequest, options?: RpcOptions): UnaryCall<ByIdRequest, EmptyResponse> {
        const method = this.methods[14], opt = this._transport.mergeOptions(options);
        return stackIntercept<ByIdRequest, EmptyResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: deleteTemplateSettings(ru.sbertroika.common.v1.ByIdRequest) returns (ru.sbertroika.common.v1.EmptyResponse);
     */
    deleteTemplateSettings(input: ByIdRequest, options?: RpcOptions): UnaryCall<ByIdRequest, EmptyResponse> {
        const method = this.methods[15], opt = this._transport.mergeOptions(options);
        return stackIntercept<ByIdRequest, EmptyResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: getSettingsById(ru.sbertroika.common.v1.ByIdRequest) returns (ru.sbertroika.tms.gate.v1.SettingsResponse);
     */
    getSettingsById(input: ByIdRequest, options?: RpcOptions): UnaryCall<ByIdRequest, SettingsResponse> {
        const method = this.methods[16], opt = this._transport.mergeOptions(options);
        return stackIntercept<ByIdRequest, SettingsResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: getSettingsGroupById(ru.sbertroika.common.v1.ByIdRequest) returns (ru.sbertroika.tms.gate.v1.SettingsGroupResponse);
     */
    getSettingsGroupById(input: ByIdRequest, options?: RpcOptions): UnaryCall<ByIdRequest, SettingsGroupResponse> {
        const method = this.methods[17], opt = this._transport.mergeOptions(options);
        return stackIntercept<ByIdRequest, SettingsGroupResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: getTerminalProfileById(ru.sbertroika.common.v1.ByIdRequest) returns (ru.sbertroika.tms.gate.v1.TerminalProfileResponse);
     */
    getTerminalProfileById(input: ByIdRequest, options?: RpcOptions): UnaryCall<ByIdRequest, TerminalProfileResponse> {
        const method = this.methods[18], opt = this._transport.mergeOptions(options);
        return stackIntercept<ByIdRequest, TerminalProfileResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: getTemplateSettingsById(ru.sbertroika.common.v1.ByIdRequest) returns (ru.sbertroika.tms.gate.v1.TemplateSettingsResponse);
     */
    getTemplateSettingsById(input: ByIdRequest, options?: RpcOptions): UnaryCall<ByIdRequest, TemplateSettingsResponse> {
        const method = this.methods[19], opt = this._transport.mergeOptions(options);
        return stackIntercept<ByIdRequest, TemplateSettingsResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: getTemplateSettingsForSettingGroup(ru.sbertroika.common.v1.ByIdRequest) returns (ru.sbertroika.tms.gate.v1.TemplateSettingsListResponse);
     */
    getTemplateSettingsForSettingGroup(input: ByIdRequest, options?: RpcOptions): UnaryCall<ByIdRequest, TemplateSettingsListResponse> {
        const method = this.methods[20], opt = this._transport.mergeOptions(options);
        return stackIntercept<ByIdRequest, TemplateSettingsListResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: getSettingsForTerminalProfile(ru.sbertroika.common.v1.ByIdRequest) returns (ru.sbertroika.tms.gate.v1.SettingsListResponse);
     */
    getSettingsForTerminalProfile(input: ByIdRequest, options?: RpcOptions): UnaryCall<ByIdRequest, SettingsListResponse> {
        const method = this.methods[21], opt = this._transport.mergeOptions(options);
        return stackIntercept<ByIdRequest, SettingsListResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: getSettingGroupForTerminalProfile(ru.sbertroika.common.v1.ByIdRequest) returns (ru.sbertroika.tms.gate.v1.SettingsGroupListResponse);
     */
    getSettingGroupForTerminalProfile(input: ByIdRequest, options?: RpcOptions): UnaryCall<ByIdRequest, SettingsGroupListResponse> {
        const method = this.methods[22], opt = this._transport.mergeOptions(options);
        return stackIntercept<ByIdRequest, SettingsGroupListResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: assignTemplateSettingsToSettingGroup(ru.sbertroika.tms.gate.v1.AssignTemplateSettingsToSettingGroupRequest) returns (ru.sbertroika.common.v1.EmptyResponse);
     */
    assignTemplateSettingsToSettingGroup(input: AssignTemplateSettingsToSettingGroupRequest, options?: RpcOptions): UnaryCall<AssignTemplateSettingsToSettingGroupRequest, EmptyResponse> {
        const method = this.methods[23], opt = this._transport.mergeOptions(options);
        return stackIntercept<AssignTemplateSettingsToSettingGroupRequest, EmptyResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: assignSettingsToTerminalProfile(ru.sbertroika.tms.gate.v1.AssignSettingsToTerminalProfileRequest) returns (ru.sbertroika.common.v1.EmptyResponse);
     */
    assignSettingsToTerminalProfile(input: AssignSettingsToTerminalProfileRequest, options?: RpcOptions): UnaryCall<AssignSettingsToTerminalProfileRequest, EmptyResponse> {
        const method = this.methods[24], opt = this._transport.mergeOptions(options);
        return stackIntercept<AssignSettingsToTerminalProfileRequest, EmptyResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: assignSettingGroupToTerminalProfile(ru.sbertroika.tms.gate.v1.AssignSettingGroupToTerminalProfileRequest) returns (ru.sbertroika.common.v1.EmptyResponse);
     */
    assignSettingGroupToTerminalProfile(input: AssignSettingGroupToTerminalProfileRequest, options?: RpcOptions): UnaryCall<AssignSettingGroupToTerminalProfileRequest, EmptyResponse> {
        const method = this.methods[25], opt = this._transport.mergeOptions(options);
        return stackIntercept<AssignSettingGroupToTerminalProfileRequest, EmptyResponse>("unary", this._transport, method, opt, input);
    }
}
