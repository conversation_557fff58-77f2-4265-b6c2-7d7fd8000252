// @generated by protobuf-ts 2.9.3
// @generated from protobuf file "tms-gate-private.proto" (package "ru.sbertroika.tms.gate.v1", syntax proto3)
// tslint:disable
import { ByIdRequest } from "./common";
import { Empty } from "./google/protobuf/empty";
import { EmptyResponse } from "./common";
import { ServiceType } from "@protobuf-ts/runtime-rpc";
import type { BinaryWriteOptions } from "@protobuf-ts/runtime";
import type { IBinaryWriter } from "@protobuf-ts/runtime";
import { WireType } from "@protobuf-ts/runtime";
import type { BinaryReadOptions } from "@protobuf-ts/runtime";
import type { IBinaryReader } from "@protobuf-ts/runtime";
import { UnknownFieldHandler } from "@protobuf-ts/runtime";
import type { PartialMessage } from "@protobuf-ts/runtime";
import { reflectionMergePartial } from "@protobuf-ts/runtime";
import { MessageType } from "@protobuf-ts/runtime";
import { Timestamp } from "./google/protobuf/timestamp";
import { EventType } from "./common-tms";
import { PaginationResponse } from "./common";
import { OperationError } from "./common";
import { PaginationRequest } from "./common";
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.TerminalUserFilters
 */
export interface TerminalUserFilters {
    /**
     * @generated from protobuf field: repeated string userId = 1;
     */
    userId: string[];
    /**
     * @generated from protobuf field: optional string login = 2;
     */
    login?: string;
    /**
     * @generated from protobuf field: optional string surname = 3;
     */
    surname?: string;
    /**
     * @generated from protobuf field: optional string personalNumber = 4;
     */
    personalNumber?: string;
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.TerminalUsersListRequest
 */
export interface TerminalUsersListRequest {
    /**
     * @generated from protobuf field: ru.sbertroika.tms.gate.v1.TerminalUserFilters filters = 1;
     */
    filters?: TerminalUserFilters;
    /**
     * @generated from protobuf field: optional ru.sbertroika.common.v1.PaginationRequest pagination = 2;
     */
    pagination?: PaginationRequest;
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.TerminalUsersListResponse
 */
export interface TerminalUsersListResponse {
    /**
     * @generated from protobuf oneof: response
     */
    response: {
        oneofKind: "error";
        /**
         * @generated from protobuf field: ru.sbertroika.common.v1.OperationError error = 1;
         */
        error: OperationError;
    } | {
        oneofKind: "result";
        /**
         * @generated from protobuf field: ru.sbertroika.tms.gate.v1.TerminalUserList result = 2;
         */
        result: TerminalUserList;
    } | {
        oneofKind: undefined;
    };
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.TerminalUserList
 */
export interface TerminalUserList {
    /**
     * @generated from protobuf field: optional ru.sbertroika.common.v1.PaginationResponse pagination = 1;
     */
    pagination?: PaginationResponse;
    /**
     * @generated from protobuf field: repeated ru.sbertroika.tms.gate.v1.TerminalUser user = 2;
     */
    user: TerminalUser[];
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.TerminalUser
 */
export interface TerminalUser {
    /**
     * @generated from protobuf field: string userId = 1;
     */
    userId: string; // идентификатор пользователя
    /**
     * @generated from protobuf field: string login = 2;
     */
    login: string; // логин
    /**
     * @generated from protobuf field: string surname = 3;
     */
    surname: string; // Фамилия
    /**
     * @generated from protobuf field: string name = 4;
     */
    name: string; // Имя
    /**
     * @generated from protobuf field: string middleName = 5;
     */
    middleName: string; // Отчество
    /**
     * @generated from protobuf field: string personalNumber = 6;
     */
    personalNumber: string; // Табельный номер
    /**
     * @generated from protobuf field: string password = 7;
     */
    password: string; // Хэш пароля
    /**
     * @generated from protobuf field: repeated string roles = 8;
     */
    roles: string[]; // ролли
    /**
     * @generated from protobuf field: repeated string groups = 9;
     */
    groups: string[]; // группы
    /**
     * @generated from protobuf field: bool enabled = 10;
     */
    enabled: boolean; // активный
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.UpdateUserReq
 */
export interface UpdateUserReq {
    /**
     * @generated from protobuf field: string userId = 1;
     */
    userId: string; // идентификатор пользователя
    /**
     * @generated from protobuf field: string surname = 2;
     */
    surname: string; // Фамилия
    /**
     * @generated from protobuf field: string name = 3;
     */
    name: string; // Имя
    /**
     * @generated from protobuf field: string middleName = 4;
     */
    middleName: string; // Отчество
    /**
     * @generated from protobuf field: string personalNumber = 5;
     */
    personalNumber: string; // Табельный номер
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.BlockUserRequest
 */
export interface BlockUserRequest {
    /**
     * @generated from protobuf field: string userId = 1;
     */
    userId: string;
    /**
     * @generated from protobuf field: bool block = 2;
     */
    block: boolean;
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.RolesResponse
 */
export interface RolesResponse {
    /**
     * @generated from protobuf oneof: response
     */
    response: {
        oneofKind: "error";
        /**
         * @generated from protobuf field: ru.sbertroika.common.v1.OperationError error = 1;
         */
        error: OperationError;
    } | {
        oneofKind: "result";
        /**
         * @generated from protobuf field: ru.sbertroika.tms.gate.v1.RoleResult result = 2;
         */
        result: RoleResult;
    } | {
        oneofKind: undefined;
    };
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.GroupsResponse
 */
export interface GroupsResponse {
    /**
     * @generated from protobuf oneof: response
     */
    response: {
        oneofKind: "error";
        /**
         * @generated from protobuf field: ru.sbertroika.common.v1.OperationError error = 1;
         */
        error: OperationError;
    } | {
        oneofKind: "result";
        /**
         * @generated from protobuf field: ru.sbertroika.tms.gate.v1.GroupResult result = 2;
         */
        result: GroupResult;
    } | {
        oneofKind: undefined;
    };
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.RoleResult
 */
export interface RoleResult {
    /**
     * @generated from protobuf field: repeated ru.sbertroika.tms.gate.v1.Role roles = 1;
     */
    roles: Role[];
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.Role
 */
export interface Role {
    /**
     * @generated from protobuf field: string name = 1;
     */
    name: string;
    /**
     * @generated from protobuf field: string description = 2;
     */
    description: string;
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.GroupResult
 */
export interface GroupResult {
    /**
     * @generated from protobuf field: repeated string groups = 1;
     */
    groups: string[];
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.ChangePasswordRequest
 */
export interface ChangePasswordRequest {
    /**
     * @generated from protobuf field: string userId = 1;
     */
    userId: string;
    /**
     * @generated from protobuf field: string newPassword = 2;
     */
    newPassword: string;
    /**
     * @generated from protobuf field: string confirmPassword = 3;
     */
    confirmPassword: string;
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.UserReg
 */
export interface UserReg {
    /**
     * @generated from protobuf field: string login = 1;
     */
    login: string;
    /**
     * @generated from protobuf field: string surname = 2;
     */
    surname: string;
    /**
     * @generated from protobuf field: string name = 3;
     */
    name: string;
    /**
     * @generated from protobuf field: string middleName = 4;
     */
    middleName: string;
    /**
     * @generated from protobuf field: string password = 5;
     */
    password: string;
    /**
     * @generated from protobuf field: string group = 6;
     */
    group: string;
    /**
     * @generated from protobuf field: string personalNumber = 7;
     */
    personalNumber: string;
    /**
     * @generated from protobuf field: string role = 8;
     */
    role: string;
    /**
     * @generated from protobuf field: optional string userId = 9;
     */
    userId?: string;
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.TerminalRequest
 */
export interface TerminalRequest {
    /**
     * @generated from protobuf field: string serialNumber = 1;
     */
    serialNumber: string; // Заводской номер терминала
    /**
     * @generated from protobuf field: string title = 2;
     */
    title: string; // Наименование терминала
    /**
     * @generated from protobuf field: string typeId = 3;
     */
    typeId: string; // uuid типа терминала из справочника
    /**
     * @generated from protobuf field: string organizationId = 4;
     */
    organizationId: string; // Организация, которой принадлежит терминал
    /**
     * @generated from protobuf field: optional string wifiMac = 8;
     */
    wifiMac?: string; // MAC-адрес WiFi
    /**
     * @generated from protobuf field: optional string bluetoothMac = 9;
     */
    bluetoothMac?: string; // MAC-адрес bluetooth
    /**
     * @generated from protobuf field: optional string ethernetMac = 10;
     */
    ethernetMac?: string; // MAC-адрес ethernet
    /**
     * @generated from protobuf field: optional ru.sbertroika.tms.gate.v1.TerminalStatus status = 12;
     */
    status?: TerminalStatus; // Опционально и только для update, значения ACTIVE,DISABLED,BLOCKED,IS_DELETED
    /**
     * @generated from protobuf field: optional string tid = 13;
     */
    tid?: string; // Внешний идентификатор терминала (TID)
    /**
     * @generated from protobuf field: string projectId = 14;
     */
    projectId: string; // Проект к которому принадлежит терминал
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.TerminalResponse
 */
export interface TerminalResponse {
    /**
     * @generated from protobuf oneof: response
     */
    response: {
        oneofKind: "error";
        /**
         * @generated from protobuf field: ru.sbertroika.common.v1.OperationError error = 1;
         */
        error: OperationError;
    } | {
        oneofKind: "result";
        /**
         * @generated from protobuf field: ru.sbertroika.tms.gate.v1.Terminal result = 2;
         */
        result: Terminal;
    } | {
        oneofKind: undefined;
    };
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.Terminal
 */
export interface Terminal {
    /**
     * @generated from protobuf field: string id = 1;
     */
    id: string;
    /**
     * @generated from protobuf field: string serialNumber = 2;
     */
    serialNumber: string; // Заводской номер терминала
    /**
     * @generated from protobuf field: string title = 3;
     */
    title: string; // Наименование терминала
    /**
     * @generated from protobuf field: ru.sbertroika.tms.gate.v1.TerminalStatus status = 4;
     */
    status: TerminalStatus; // Статус терминала
    /**
     * @generated from protobuf field: string versionPO = 5;
     */
    versionPO: string; // Версия ПО
    /**
     * @generated from protobuf field: ru.sbertroika.tms.gate.v1.TerminalType type = 6;
     */
    type?: TerminalType; // тип терминала из справочника
    /**
     * @generated from protobuf field: string organizationId = 7;
     */
    organizationId: string; // Организация, которой принадлежит терминал
    /**
     * @generated from protobuf field: string imei = 10;
     */
    imei: string; // IMEI[]
    /**
     * @generated from protobuf field: string wifiMac = 11;
     */
    wifiMac: string; // MAC-адрес WiFi
    /**
     * @generated from protobuf field: string bluetoothMac = 12;
     */
    bluetoothMac: string; // MAC-адрес bluetooth
    /**
     * @generated from protobuf field: string ethernetMac = 13;
     */
    ethernetMac: string; // MAC-адрес ethernet
    /**
     * @generated from protobuf field: bool isActivated = 14;
     */
    isActivated: boolean; // Активация (блок / не блок)
    /**
     * @generated from protobuf field: string tid = 15;
     */
    tid: string; // Внешний идентификатор терминала (TID)
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.TerminalListRequest
 */
export interface TerminalListRequest {
    /**
     * @generated from protobuf field: optional string serialNumber = 1;
     */
    serialNumber?: string; // Искать по серийному номеру
    /**
     * @generated from protobuf field: optional string organizationId = 2;
     */
    organizationId?: string; // Искать по организации / группе
    /**
     * @generated from protobuf field: optional ru.sbertroika.tms.gate.v1.TerminalStatus status = 3;
     */
    status?: TerminalStatus; // Статус терминала
    /**
     * @generated from protobuf field: optional ru.sbertroika.common.v1.PaginationRequest pagination = 4;
     */
    pagination?: PaginationRequest;
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.TerminalListResponse
 */
export interface TerminalListResponse {
    /**
     * @generated from protobuf oneof: response
     */
    response: {
        oneofKind: "error";
        /**
         * @generated from protobuf field: ru.sbertroika.common.v1.OperationError error = 1;
         */
        error: OperationError;
    } | {
        oneofKind: "result";
        /**
         * @generated from protobuf field: ru.sbertroika.tms.gate.v1.TerminalList result = 2;
         */
        result: TerminalList;
    } | {
        oneofKind: undefined;
    };
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.TerminalList
 */
export interface TerminalList {
    /**
     * @generated from protobuf field: optional ru.sbertroika.common.v1.PaginationResponse pagination = 1;
     */
    pagination?: PaginationResponse;
    /**
     * @generated from protobuf field: repeated ru.sbertroika.tms.gate.v1.Terminal terminal = 2;
     */
    terminal: Terminal[];
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.DeleteTerminalRequest
 */
export interface DeleteTerminalRequest {
    /**
     * @generated from protobuf field: string serialNumber = 1;
     */
    serialNumber: string; // Серийный номер терминала
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.DeleteTerminalResponse
 */
export interface DeleteTerminalResponse {
    /**
     * @generated from protobuf oneof: response
     */
    response: {
        oneofKind: "error";
        /**
         * @generated from protobuf field: ru.sbertroika.common.v1.OperationError error = 1;
         */
        error: OperationError;
    } | {
        oneofKind: "result";
        /**
         * @generated from protobuf field: string result = 2;
         */
        result: string;
    } | {
        oneofKind: undefined;
    };
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.BlockTerminalRequest
 */
export interface BlockTerminalRequest {
    /**
     * @generated from protobuf field: string serialNumber = 1;
     */
    serialNumber: string; // Серийный номер терминала
    /**
     * @generated from protobuf field: bool isBlock = 2;
     */
    isBlock: boolean; // Флаг true - заблокировать, false - разблокировать
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.BlockTerminalResponse
 */
export interface BlockTerminalResponse {
    /**
     * @generated from protobuf oneof: response
     */
    response: {
        oneofKind: "error";
        /**
         * @generated from protobuf field: ru.sbertroika.common.v1.OperationError error = 1;
         */
        error: OperationError;
    } | {
        oneofKind: "result";
        /**
         * @generated from protobuf field: string result = 2;
         */
        result: string;
    } | {
        oneofKind: undefined;
    };
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.TerminalType
 */
export interface TerminalType {
    /**
     * @generated from protobuf field: optional string id = 1;
     */
    id?: string; // только для update, для create не указывать
    /**
     * @generated from protobuf field: string name = 2;
     */
    name: string;
    /**
     * @generated from protobuf field: string slug = 3;
     */
    slug: string;
    /**
     * @generated from protobuf field: string comment = 4;
     */
    comment: string;
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.TerminalTypeResponse
 */
export interface TerminalTypeResponse {
    /**
     * @generated from protobuf oneof: response
     */
    response: {
        oneofKind: "error";
        /**
         * @generated from protobuf field: ru.sbertroika.common.v1.OperationError error = 1;
         */
        error: OperationError;
    } | {
        oneofKind: "result";
        /**
         * @generated from protobuf field: ru.sbertroika.tms.gate.v1.TerminalType result = 2;
         */
        result: TerminalType;
    } | {
        oneofKind: undefined;
    };
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.TerminalTypeListResponse
 */
export interface TerminalTypeListResponse {
    /**
     * @generated from protobuf oneof: response
     */
    response: {
        oneofKind: "error";
        /**
         * @generated from protobuf field: ru.sbertroika.common.v1.OperationError error = 1;
         */
        error: OperationError;
    } | {
        oneofKind: "result";
        /**
         * @generated from protobuf field: ru.sbertroika.tms.gate.v1.TerminalTypeList result = 2;
         */
        result: TerminalTypeList;
    } | {
        oneofKind: undefined;
    };
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.TerminalTypeList
 */
export interface TerminalTypeList {
    /**
     * @generated from protobuf field: repeated ru.sbertroika.tms.gate.v1.TerminalType terminalType = 2;
     */
    terminalType: TerminalType[];
}
/**
 * Журнал терминальных событий
 *
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.JournalListFilter
 */
export interface JournalListFilter {
    /**
     * @generated from protobuf field: optional string terminalSerial = 1;
     */
    terminalSerial?: string; // Заводской номер терминала
    /**
     * @generated from protobuf field: repeated ru.sbertroika.common.tms.EventType eventType = 2;
     */
    eventType: EventType[]; // Тип события
    /**
     * @generated from protobuf field: optional google.protobuf.Timestamp createdAtFrom = 3;
     */
    createdAtFrom?: Timestamp; // Дата формирования события на терминале (в UTC+0) - с
    /**
     * @generated from protobuf field: optional google.protobuf.Timestamp createdAtTo = 4;
     */
    createdAtTo?: Timestamp; // Дата формирования события на терминале (в UTC+0) - по
    /**
     * @generated from protobuf field: optional string userId = 5;
     */
    userId?: string; // Идентификатор пользователя (если пользователь авторизован на терминале)
    /**
     * @generated from protobuf field: optional uint32 shiftNum = 6;
     */
    shiftNum?: number; // Номер смены на терминале
    /**
     * @generated from protobuf field: optional uint32 ern = 7;
     */
    ern?: number; // Единый регистрационный номер (уникальный в рамках смены)
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.JournalListRequest
 */
export interface JournalListRequest {
    /**
     * @generated from protobuf field: optional ru.sbertroika.common.v1.PaginationRequest pagination = 1;
     */
    pagination?: PaginationRequest;
    /**
     * @generated from protobuf field: optional ru.sbertroika.tms.gate.v1.JournalListFilter filter = 2;
     */
    filter?: JournalListFilter;
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.TerminalJournalEvent
 */
export interface TerminalJournalEvent {
    /**
     * @generated from protobuf field: ru.sbertroika.common.tms.EventType eventType = 1;
     */
    eventType: EventType; // Тип события
    /**
     * @generated from protobuf field: google.protobuf.Timestamp createdAt = 2;
     */
    createdAt?: Timestamp; // Дата формирования события на терминале (в UTC+0)
    /**
     * @generated from protobuf field: string terminalSerial = 3;
     */
    terminalSerial: string; // Заводской номер терминала
    /**
     * @generated from protobuf field: optional uint32 ern = 4;
     */
    ern?: number; // Единый регистрационный номер (уникальный в рамках смены)
    /**
     * @generated from protobuf field: optional string userId = 5;
     */
    userId?: string; // Идентификатор пользователя (если пользователь авторизован на терминале)
    /**
     * @generated from protobuf field: optional uint32 shiftNum = 6;
     */
    shiftNum?: number; // Номер смены на терминале
    /**
     * @generated from protobuf field: optional uint32 stopListVersion = 7;
     */
    stopListVersion?: number; // Версия стоп-листа
    /**
     * @generated from protobuf field: optional google.protobuf.Timestamp stopListUpdate = 8;
     */
    stopListUpdate?: Timestamp; // Дата обновления стоп-листа
    /**
     * @generated from protobuf field: optional int32 errorCode = 9;
     */
    errorCode?: number; // Код ошибки
    /**
     * @generated from protobuf field: optional string errorMessage = 10;
     */
    errorMessage?: string; // Сообщение об ошибке
    /**
     * @generated from protobuf field: optional string value = 11;
     */
    value?: string; // Значение в рамках события
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.JournalListResult
 */
export interface JournalListResult {
    /**
     * @generated from protobuf field: optional ru.sbertroika.common.v1.PaginationResponse pagination = 1;
     */
    pagination?: PaginationResponse;
    /**
     * @generated from protobuf field: optional ru.sbertroika.tms.gate.v1.JournalListFilter filter = 2;
     */
    filter?: JournalListFilter;
    /**
     * @generated from protobuf field: repeated ru.sbertroika.tms.gate.v1.TerminalJournalEvent event = 3;
     */
    event: TerminalJournalEvent[];
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.JournalListResponse
 */
export interface JournalListResponse {
    /**
     * @generated from protobuf oneof: response
     */
    response: {
        oneofKind: "error";
        /**
         * @generated from protobuf field: ru.sbertroika.common.v1.OperationError error = 1;
         */
        error: OperationError;
    } | {
        oneofKind: "result";
        /**
         * @generated from protobuf field: ru.sbertroika.tms.gate.v1.JournalListResult result = 2;
         */
        result: JournalListResult;
    } | {
        oneofKind: undefined;
    };
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.TerminalUserResponse
 */
export interface TerminalUserResponse {
    /**
     * @generated from protobuf oneof: response
     */
    response: {
        oneofKind: "error";
        /**
         * @generated from protobuf field: ru.sbertroika.common.v1.OperationError error = 1;
         */
        error: OperationError;
    } | {
        oneofKind: "result";
        /**
         * @generated from protobuf field: ru.sbertroika.tms.gate.v1.TerminalUser result = 2;
         */
        result: TerminalUser;
    } | {
        oneofKind: undefined;
    };
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.EventTypeListResponse
 */
export interface EventTypeListResponse {
    /**
     * @generated from protobuf oneof: response
     */
    response: {
        oneofKind: "error";
        /**
         * @generated from protobuf field: ru.sbertroika.common.v1.OperationError error = 1;
         */
        error: OperationError;
    } | {
        oneofKind: "result";
        /**
         * @generated from protobuf field: ru.sbertroika.tms.gate.v1.EventTypeDescriptionList result = 2;
         */
        result: EventTypeDescriptionList;
    } | {
        oneofKind: undefined;
    };
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.EventTypeDescriptionList
 */
export interface EventTypeDescriptionList {
    /**
     * @generated from protobuf field: repeated ru.sbertroika.tms.gate.v1.EventTypeDescription eventType = 1;
     */
    eventType: EventTypeDescription[];
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.EventTypeDescription
 */
export interface EventTypeDescription {
    /**
     * @generated from protobuf field: ru.sbertroika.common.tms.EventType type = 1;
     */
    type: EventType;
    /**
     * @generated from protobuf field: string description = 2;
     */
    description: string;
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.TerminalTypeListRequest
 */
export interface TerminalTypeListRequest {
    /**
     * @generated from protobuf field: optional ru.sbertroika.tms.gate.v1.TerminalTypeFilters filters = 1;
     */
    filters?: TerminalTypeFilters;
}
/**
 * @generated from protobuf message ru.sbertroika.tms.gate.v1.TerminalTypeFilters
 */
export interface TerminalTypeFilters {
    /**
     * @generated from protobuf field: optional string name = 1;
     */
    name?: string;
}
/**
 * @generated from protobuf enum ru.sbertroika.tms.gate.v1.TerminalStatus
 */
export enum TerminalStatus {
    /**
     * @generated from protobuf enum value: ACTIVE = 0;
     */
    ACTIVE = 0,
    /**
     * @generated from protobuf enum value: DISABLED = 1;
     */
    DISABLED = 1,
    /**
     * @generated from protobuf enum value: BLOCKED = 2;
     */
    BLOCKED = 2,
    /**
     * @generated from protobuf enum value: IS_DELETED = 3;
     */
    IS_DELETED = 3,
    /**
     * @generated from protobuf enum value: ALL = 4;
     */
    ALL = 4
}
// @generated message type with reflection information, may provide speed optimized methods
class TerminalUserFilters$Type extends MessageType<TerminalUserFilters> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.TerminalUserFilters", [
            { no: 1, name: "userId", kind: "scalar", repeat: 2 /*RepeatType.UNPACKED*/, T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "login", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 3, name: "surname", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 4, name: "personalNumber", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<TerminalUserFilters>): TerminalUserFilters {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.userId = [];
        if (value !== undefined)
            reflectionMergePartial<TerminalUserFilters>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: TerminalUserFilters): TerminalUserFilters {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated string userId */ 1:
                    message.userId.push(reader.string());
                    break;
                case /* optional string login */ 2:
                    message.login = reader.string();
                    break;
                case /* optional string surname */ 3:
                    message.surname = reader.string();
                    break;
                case /* optional string personalNumber */ 4:
                    message.personalNumber = reader.string();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: TerminalUserFilters, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated string userId = 1; */
        for (let i = 0; i < message.userId.length; i++)
            writer.tag(1, WireType.LengthDelimited).string(message.userId[i]);
        /* optional string login = 2; */
        if (message.login !== undefined)
            writer.tag(2, WireType.LengthDelimited).string(message.login);
        /* optional string surname = 3; */
        if (message.surname !== undefined)
            writer.tag(3, WireType.LengthDelimited).string(message.surname);
        /* optional string personalNumber = 4; */
        if (message.personalNumber !== undefined)
            writer.tag(4, WireType.LengthDelimited).string(message.personalNumber);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.TerminalUserFilters
 */
export const TerminalUserFilters = new TerminalUserFilters$Type();
// @generated message type with reflection information, may provide speed optimized methods
class TerminalUsersListRequest$Type extends MessageType<TerminalUsersListRequest> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.TerminalUsersListRequest", [
            { no: 1, name: "filters", kind: "message", T: () => TerminalUserFilters },
            { no: 2, name: "pagination", kind: "message", T: () => PaginationRequest }
        ]);
    }
    create(value?: PartialMessage<TerminalUsersListRequest>): TerminalUsersListRequest {
        const message = globalThis.Object.create((this.messagePrototype!));
        if (value !== undefined)
            reflectionMergePartial<TerminalUsersListRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: TerminalUsersListRequest): TerminalUsersListRequest {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* ru.sbertroika.tms.gate.v1.TerminalUserFilters filters */ 1:
                    message.filters = TerminalUserFilters.internalBinaryRead(reader, reader.uint32(), options, message.filters);
                    break;
                case /* optional ru.sbertroika.common.v1.PaginationRequest pagination */ 2:
                    message.pagination = PaginationRequest.internalBinaryRead(reader, reader.uint32(), options, message.pagination);
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: TerminalUsersListRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* ru.sbertroika.tms.gate.v1.TerminalUserFilters filters = 1; */
        if (message.filters)
            TerminalUserFilters.internalBinaryWrite(message.filters, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* optional ru.sbertroika.common.v1.PaginationRequest pagination = 2; */
        if (message.pagination)
            PaginationRequest.internalBinaryWrite(message.pagination, writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.TerminalUsersListRequest
 */
export const TerminalUsersListRequest = new TerminalUsersListRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class TerminalUsersListResponse$Type extends MessageType<TerminalUsersListResponse> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.TerminalUsersListResponse", [
            { no: 1, name: "error", kind: "message", oneof: "response", T: () => OperationError },
            { no: 2, name: "result", kind: "message", oneof: "response", T: () => TerminalUserList }
        ]);
    }
    create(value?: PartialMessage<TerminalUsersListResponse>): TerminalUsersListResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.response = { oneofKind: undefined };
        if (value !== undefined)
            reflectionMergePartial<TerminalUsersListResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: TerminalUsersListResponse): TerminalUsersListResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* ru.sbertroika.common.v1.OperationError error */ 1:
                    message.response = {
                        oneofKind: "error",
                        error: OperationError.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).error)
                    };
                    break;
                case /* ru.sbertroika.tms.gate.v1.TerminalUserList result */ 2:
                    message.response = {
                        oneofKind: "result",
                        result: TerminalUserList.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).result)
                    };
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: TerminalUsersListResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* ru.sbertroika.common.v1.OperationError error = 1; */
        if (message.response.oneofKind === "error")
            OperationError.internalBinaryWrite(message.response.error, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* ru.sbertroika.tms.gate.v1.TerminalUserList result = 2; */
        if (message.response.oneofKind === "result")
            TerminalUserList.internalBinaryWrite(message.response.result, writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.TerminalUsersListResponse
 */
export const TerminalUsersListResponse = new TerminalUsersListResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class TerminalUserList$Type extends MessageType<TerminalUserList> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.TerminalUserList", [
            { no: 1, name: "pagination", kind: "message", T: () => PaginationResponse },
            { no: 2, name: "user", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => TerminalUser }
        ]);
    }
    create(value?: PartialMessage<TerminalUserList>): TerminalUserList {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.user = [];
        if (value !== undefined)
            reflectionMergePartial<TerminalUserList>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: TerminalUserList): TerminalUserList {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* optional ru.sbertroika.common.v1.PaginationResponse pagination */ 1:
                    message.pagination = PaginationResponse.internalBinaryRead(reader, reader.uint32(), options, message.pagination);
                    break;
                case /* repeated ru.sbertroika.tms.gate.v1.TerminalUser user */ 2:
                    message.user.push(TerminalUser.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: TerminalUserList, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* optional ru.sbertroika.common.v1.PaginationResponse pagination = 1; */
        if (message.pagination)
            PaginationResponse.internalBinaryWrite(message.pagination, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* repeated ru.sbertroika.tms.gate.v1.TerminalUser user = 2; */
        for (let i = 0; i < message.user.length; i++)
            TerminalUser.internalBinaryWrite(message.user[i], writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.TerminalUserList
 */
export const TerminalUserList = new TerminalUserList$Type();
// @generated message type with reflection information, may provide speed optimized methods
class TerminalUser$Type extends MessageType<TerminalUser> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.TerminalUser", [
            { no: 1, name: "userId", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "login", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 3, name: "surname", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 4, name: "name", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 5, name: "middleName", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 6, name: "personalNumber", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 7, name: "password", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 8, name: "roles", kind: "scalar", repeat: 2 /*RepeatType.UNPACKED*/, T: 9 /*ScalarType.STRING*/ },
            { no: 9, name: "groups", kind: "scalar", repeat: 2 /*RepeatType.UNPACKED*/, T: 9 /*ScalarType.STRING*/ },
            { no: 10, name: "enabled", kind: "scalar", T: 8 /*ScalarType.BOOL*/ }
        ]);
    }
    create(value?: PartialMessage<TerminalUser>): TerminalUser {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.userId = "";
        message.login = "";
        message.surname = "";
        message.name = "";
        message.middleName = "";
        message.personalNumber = "";
        message.password = "";
        message.roles = [];
        message.groups = [];
        message.enabled = false;
        if (value !== undefined)
            reflectionMergePartial<TerminalUser>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: TerminalUser): TerminalUser {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* string userId */ 1:
                    message.userId = reader.string();
                    break;
                case /* string login */ 2:
                    message.login = reader.string();
                    break;
                case /* string surname */ 3:
                    message.surname = reader.string();
                    break;
                case /* string name */ 4:
                    message.name = reader.string();
                    break;
                case /* string middleName */ 5:
                    message.middleName = reader.string();
                    break;
                case /* string personalNumber */ 6:
                    message.personalNumber = reader.string();
                    break;
                case /* string password */ 7:
                    message.password = reader.string();
                    break;
                case /* repeated string roles */ 8:
                    message.roles.push(reader.string());
                    break;
                case /* repeated string groups */ 9:
                    message.groups.push(reader.string());
                    break;
                case /* bool enabled */ 10:
                    message.enabled = reader.bool();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: TerminalUser, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* string userId = 1; */
        if (message.userId !== "")
            writer.tag(1, WireType.LengthDelimited).string(message.userId);
        /* string login = 2; */
        if (message.login !== "")
            writer.tag(2, WireType.LengthDelimited).string(message.login);
        /* string surname = 3; */
        if (message.surname !== "")
            writer.tag(3, WireType.LengthDelimited).string(message.surname);
        /* string name = 4; */
        if (message.name !== "")
            writer.tag(4, WireType.LengthDelimited).string(message.name);
        /* string middleName = 5; */
        if (message.middleName !== "")
            writer.tag(5, WireType.LengthDelimited).string(message.middleName);
        /* string personalNumber = 6; */
        if (message.personalNumber !== "")
            writer.tag(6, WireType.LengthDelimited).string(message.personalNumber);
        /* string password = 7; */
        if (message.password !== "")
            writer.tag(7, WireType.LengthDelimited).string(message.password);
        /* repeated string roles = 8; */
        for (let i = 0; i < message.roles.length; i++)
            writer.tag(8, WireType.LengthDelimited).string(message.roles[i]);
        /* repeated string groups = 9; */
        for (let i = 0; i < message.groups.length; i++)
            writer.tag(9, WireType.LengthDelimited).string(message.groups[i]);
        /* bool enabled = 10; */
        if (message.enabled !== false)
            writer.tag(10, WireType.Varint).bool(message.enabled);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.TerminalUser
 */
export const TerminalUser = new TerminalUser$Type();
// @generated message type with reflection information, may provide speed optimized methods
class UpdateUserReq$Type extends MessageType<UpdateUserReq> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.UpdateUserReq", [
            { no: 1, name: "userId", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "surname", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 3, name: "name", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 4, name: "middleName", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 5, name: "personalNumber", kind: "scalar", T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<UpdateUserReq>): UpdateUserReq {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.userId = "";
        message.surname = "";
        message.name = "";
        message.middleName = "";
        message.personalNumber = "";
        if (value !== undefined)
            reflectionMergePartial<UpdateUserReq>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: UpdateUserReq): UpdateUserReq {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* string userId */ 1:
                    message.userId = reader.string();
                    break;
                case /* string surname */ 2:
                    message.surname = reader.string();
                    break;
                case /* string name */ 3:
                    message.name = reader.string();
                    break;
                case /* string middleName */ 4:
                    message.middleName = reader.string();
                    break;
                case /* string personalNumber */ 5:
                    message.personalNumber = reader.string();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: UpdateUserReq, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* string userId = 1; */
        if (message.userId !== "")
            writer.tag(1, WireType.LengthDelimited).string(message.userId);
        /* string surname = 2; */
        if (message.surname !== "")
            writer.tag(2, WireType.LengthDelimited).string(message.surname);
        /* string name = 3; */
        if (message.name !== "")
            writer.tag(3, WireType.LengthDelimited).string(message.name);
        /* string middleName = 4; */
        if (message.middleName !== "")
            writer.tag(4, WireType.LengthDelimited).string(message.middleName);
        /* string personalNumber = 5; */
        if (message.personalNumber !== "")
            writer.tag(5, WireType.LengthDelimited).string(message.personalNumber);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.UpdateUserReq
 */
export const UpdateUserReq = new UpdateUserReq$Type();
// @generated message type with reflection information, may provide speed optimized methods
class BlockUserRequest$Type extends MessageType<BlockUserRequest> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.BlockUserRequest", [
            { no: 1, name: "userId", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "block", kind: "scalar", T: 8 /*ScalarType.BOOL*/ }
        ]);
    }
    create(value?: PartialMessage<BlockUserRequest>): BlockUserRequest {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.userId = "";
        message.block = false;
        if (value !== undefined)
            reflectionMergePartial<BlockUserRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: BlockUserRequest): BlockUserRequest {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* string userId */ 1:
                    message.userId = reader.string();
                    break;
                case /* bool block */ 2:
                    message.block = reader.bool();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: BlockUserRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* string userId = 1; */
        if (message.userId !== "")
            writer.tag(1, WireType.LengthDelimited).string(message.userId);
        /* bool block = 2; */
        if (message.block !== false)
            writer.tag(2, WireType.Varint).bool(message.block);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.BlockUserRequest
 */
export const BlockUserRequest = new BlockUserRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class RolesResponse$Type extends MessageType<RolesResponse> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.RolesResponse", [
            { no: 1, name: "error", kind: "message", oneof: "response", T: () => OperationError },
            { no: 2, name: "result", kind: "message", oneof: "response", T: () => RoleResult }
        ]);
    }
    create(value?: PartialMessage<RolesResponse>): RolesResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.response = { oneofKind: undefined };
        if (value !== undefined)
            reflectionMergePartial<RolesResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: RolesResponse): RolesResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* ru.sbertroika.common.v1.OperationError error */ 1:
                    message.response = {
                        oneofKind: "error",
                        error: OperationError.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).error)
                    };
                    break;
                case /* ru.sbertroika.tms.gate.v1.RoleResult result */ 2:
                    message.response = {
                        oneofKind: "result",
                        result: RoleResult.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).result)
                    };
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: RolesResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* ru.sbertroika.common.v1.OperationError error = 1; */
        if (message.response.oneofKind === "error")
            OperationError.internalBinaryWrite(message.response.error, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* ru.sbertroika.tms.gate.v1.RoleResult result = 2; */
        if (message.response.oneofKind === "result")
            RoleResult.internalBinaryWrite(message.response.result, writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.RolesResponse
 */
export const RolesResponse = new RolesResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class GroupsResponse$Type extends MessageType<GroupsResponse> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.GroupsResponse", [
            { no: 1, name: "error", kind: "message", oneof: "response", T: () => OperationError },
            { no: 2, name: "result", kind: "message", oneof: "response", T: () => GroupResult }
        ]);
    }
    create(value?: PartialMessage<GroupsResponse>): GroupsResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.response = { oneofKind: undefined };
        if (value !== undefined)
            reflectionMergePartial<GroupsResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: GroupsResponse): GroupsResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* ru.sbertroika.common.v1.OperationError error */ 1:
                    message.response = {
                        oneofKind: "error",
                        error: OperationError.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).error)
                    };
                    break;
                case /* ru.sbertroika.tms.gate.v1.GroupResult result */ 2:
                    message.response = {
                        oneofKind: "result",
                        result: GroupResult.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).result)
                    };
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: GroupsResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* ru.sbertroika.common.v1.OperationError error = 1; */
        if (message.response.oneofKind === "error")
            OperationError.internalBinaryWrite(message.response.error, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* ru.sbertroika.tms.gate.v1.GroupResult result = 2; */
        if (message.response.oneofKind === "result")
            GroupResult.internalBinaryWrite(message.response.result, writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.GroupsResponse
 */
export const GroupsResponse = new GroupsResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class RoleResult$Type extends MessageType<RoleResult> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.RoleResult", [
            { no: 1, name: "roles", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => Role }
        ]);
    }
    create(value?: PartialMessage<RoleResult>): RoleResult {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.roles = [];
        if (value !== undefined)
            reflectionMergePartial<RoleResult>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: RoleResult): RoleResult {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated ru.sbertroika.tms.gate.v1.Role roles */ 1:
                    message.roles.push(Role.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: RoleResult, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated ru.sbertroika.tms.gate.v1.Role roles = 1; */
        for (let i = 0; i < message.roles.length; i++)
            Role.internalBinaryWrite(message.roles[i], writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.RoleResult
 */
export const RoleResult = new RoleResult$Type();
// @generated message type with reflection information, may provide speed optimized methods
class Role$Type extends MessageType<Role> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.Role", [
            { no: 1, name: "name", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "description", kind: "scalar", T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<Role>): Role {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.name = "";
        message.description = "";
        if (value !== undefined)
            reflectionMergePartial<Role>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: Role): Role {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* string name */ 1:
                    message.name = reader.string();
                    break;
                case /* string description */ 2:
                    message.description = reader.string();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: Role, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* string name = 1; */
        if (message.name !== "")
            writer.tag(1, WireType.LengthDelimited).string(message.name);
        /* string description = 2; */
        if (message.description !== "")
            writer.tag(2, WireType.LengthDelimited).string(message.description);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.Role
 */
export const Role = new Role$Type();
// @generated message type with reflection information, may provide speed optimized methods
class GroupResult$Type extends MessageType<GroupResult> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.GroupResult", [
            { no: 1, name: "groups", kind: "scalar", repeat: 2 /*RepeatType.UNPACKED*/, T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<GroupResult>): GroupResult {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.groups = [];
        if (value !== undefined)
            reflectionMergePartial<GroupResult>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: GroupResult): GroupResult {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated string groups */ 1:
                    message.groups.push(reader.string());
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: GroupResult, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated string groups = 1; */
        for (let i = 0; i < message.groups.length; i++)
            writer.tag(1, WireType.LengthDelimited).string(message.groups[i]);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.GroupResult
 */
export const GroupResult = new GroupResult$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ChangePasswordRequest$Type extends MessageType<ChangePasswordRequest> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.ChangePasswordRequest", [
            { no: 1, name: "userId", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "newPassword", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 3, name: "confirmPassword", kind: "scalar", T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<ChangePasswordRequest>): ChangePasswordRequest {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.userId = "";
        message.newPassword = "";
        message.confirmPassword = "";
        if (value !== undefined)
            reflectionMergePartial<ChangePasswordRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: ChangePasswordRequest): ChangePasswordRequest {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* string userId */ 1:
                    message.userId = reader.string();
                    break;
                case /* string newPassword */ 2:
                    message.newPassword = reader.string();
                    break;
                case /* string confirmPassword */ 3:
                    message.confirmPassword = reader.string();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: ChangePasswordRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* string userId = 1; */
        if (message.userId !== "")
            writer.tag(1, WireType.LengthDelimited).string(message.userId);
        /* string newPassword = 2; */
        if (message.newPassword !== "")
            writer.tag(2, WireType.LengthDelimited).string(message.newPassword);
        /* string confirmPassword = 3; */
        if (message.confirmPassword !== "")
            writer.tag(3, WireType.LengthDelimited).string(message.confirmPassword);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.ChangePasswordRequest
 */
export const ChangePasswordRequest = new ChangePasswordRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class UserReg$Type extends MessageType<UserReg> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.UserReg", [
            { no: 1, name: "login", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "surname", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 3, name: "name", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 4, name: "middleName", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 5, name: "password", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 6, name: "group", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 7, name: "personalNumber", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 8, name: "role", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 9, name: "userId", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<UserReg>): UserReg {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.login = "";
        message.surname = "";
        message.name = "";
        message.middleName = "";
        message.password = "";
        message.group = "";
        message.personalNumber = "";
        message.role = "";
        if (value !== undefined)
            reflectionMergePartial<UserReg>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: UserReg): UserReg {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* string login */ 1:
                    message.login = reader.string();
                    break;
                case /* string surname */ 2:
                    message.surname = reader.string();
                    break;
                case /* string name */ 3:
                    message.name = reader.string();
                    break;
                case /* string middleName */ 4:
                    message.middleName = reader.string();
                    break;
                case /* string password */ 5:
                    message.password = reader.string();
                    break;
                case /* string group */ 6:
                    message.group = reader.string();
                    break;
                case /* string personalNumber */ 7:
                    message.personalNumber = reader.string();
                    break;
                case /* string role */ 8:
                    message.role = reader.string();
                    break;
                case /* optional string userId */ 9:
                    message.userId = reader.string();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: UserReg, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* string login = 1; */
        if (message.login !== "")
            writer.tag(1, WireType.LengthDelimited).string(message.login);
        /* string surname = 2; */
        if (message.surname !== "")
            writer.tag(2, WireType.LengthDelimited).string(message.surname);
        /* string name = 3; */
        if (message.name !== "")
            writer.tag(3, WireType.LengthDelimited).string(message.name);
        /* string middleName = 4; */
        if (message.middleName !== "")
            writer.tag(4, WireType.LengthDelimited).string(message.middleName);
        /* string password = 5; */
        if (message.password !== "")
            writer.tag(5, WireType.LengthDelimited).string(message.password);
        /* string group = 6; */
        if (message.group !== "")
            writer.tag(6, WireType.LengthDelimited).string(message.group);
        /* string personalNumber = 7; */
        if (message.personalNumber !== "")
            writer.tag(7, WireType.LengthDelimited).string(message.personalNumber);
        /* string role = 8; */
        if (message.role !== "")
            writer.tag(8, WireType.LengthDelimited).string(message.role);
        /* optional string userId = 9; */
        if (message.userId !== undefined)
            writer.tag(9, WireType.LengthDelimited).string(message.userId);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.UserReg
 */
export const UserReg = new UserReg$Type();
// @generated message type with reflection information, may provide speed optimized methods
class TerminalRequest$Type extends MessageType<TerminalRequest> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.TerminalRequest", [
            { no: 1, name: "serialNumber", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "title", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 3, name: "typeId", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 4, name: "organizationId", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 8, name: "wifiMac", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 9, name: "bluetoothMac", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 10, name: "ethernetMac", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 12, name: "status", kind: "enum", opt: true, T: () => ["ru.sbertroika.tms.gate.v1.TerminalStatus", TerminalStatus] },
            { no: 13, name: "tid", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 14, name: "projectId", kind: "scalar", T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<TerminalRequest>): TerminalRequest {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.serialNumber = "";
        message.title = "";
        message.typeId = "";
        message.organizationId = "";
        message.projectId = "";
        if (value !== undefined)
            reflectionMergePartial<TerminalRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: TerminalRequest): TerminalRequest {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* string serialNumber */ 1:
                    message.serialNumber = reader.string();
                    break;
                case /* string title */ 2:
                    message.title = reader.string();
                    break;
                case /* string typeId */ 3:
                    message.typeId = reader.string();
                    break;
                case /* string organizationId */ 4:
                    message.organizationId = reader.string();
                    break;
                case /* optional string wifiMac */ 8:
                    message.wifiMac = reader.string();
                    break;
                case /* optional string bluetoothMac */ 9:
                    message.bluetoothMac = reader.string();
                    break;
                case /* optional string ethernetMac */ 10:
                    message.ethernetMac = reader.string();
                    break;
                case /* optional ru.sbertroika.tms.gate.v1.TerminalStatus status */ 12:
                    message.status = reader.int32();
                    break;
                case /* optional string tid */ 13:
                    message.tid = reader.string();
                    break;
                case /* string projectId */ 14:
                    message.projectId = reader.string();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: TerminalRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* string serialNumber = 1; */
        if (message.serialNumber !== "")
            writer.tag(1, WireType.LengthDelimited).string(message.serialNumber);
        /* string title = 2; */
        if (message.title !== "")
            writer.tag(2, WireType.LengthDelimited).string(message.title);
        /* string typeId = 3; */
        if (message.typeId !== "")
            writer.tag(3, WireType.LengthDelimited).string(message.typeId);
        /* string organizationId = 4; */
        if (message.organizationId !== "")
            writer.tag(4, WireType.LengthDelimited).string(message.organizationId);
        /* optional string wifiMac = 8; */
        if (message.wifiMac !== undefined)
            writer.tag(8, WireType.LengthDelimited).string(message.wifiMac);
        /* optional string bluetoothMac = 9; */
        if (message.bluetoothMac !== undefined)
            writer.tag(9, WireType.LengthDelimited).string(message.bluetoothMac);
        /* optional string ethernetMac = 10; */
        if (message.ethernetMac !== undefined)
            writer.tag(10, WireType.LengthDelimited).string(message.ethernetMac);
        /* optional ru.sbertroika.tms.gate.v1.TerminalStatus status = 12; */
        if (message.status !== undefined)
            writer.tag(12, WireType.Varint).int32(message.status);
        /* optional string tid = 13; */
        if (message.tid !== undefined)
            writer.tag(13, WireType.LengthDelimited).string(message.tid);
        /* string projectId = 14; */
        if (message.projectId !== "")
            writer.tag(14, WireType.LengthDelimited).string(message.projectId);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.TerminalRequest
 */
export const TerminalRequest = new TerminalRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class TerminalResponse$Type extends MessageType<TerminalResponse> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.TerminalResponse", [
            { no: 1, name: "error", kind: "message", oneof: "response", T: () => OperationError },
            { no: 2, name: "result", kind: "message", oneof: "response", T: () => Terminal }
        ]);
    }
    create(value?: PartialMessage<TerminalResponse>): TerminalResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.response = { oneofKind: undefined };
        if (value !== undefined)
            reflectionMergePartial<TerminalResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: TerminalResponse): TerminalResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* ru.sbertroika.common.v1.OperationError error */ 1:
                    message.response = {
                        oneofKind: "error",
                        error: OperationError.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).error)
                    };
                    break;
                case /* ru.sbertroika.tms.gate.v1.Terminal result */ 2:
                    message.response = {
                        oneofKind: "result",
                        result: Terminal.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).result)
                    };
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: TerminalResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* ru.sbertroika.common.v1.OperationError error = 1; */
        if (message.response.oneofKind === "error")
            OperationError.internalBinaryWrite(message.response.error, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* ru.sbertroika.tms.gate.v1.Terminal result = 2; */
        if (message.response.oneofKind === "result")
            Terminal.internalBinaryWrite(message.response.result, writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.TerminalResponse
 */
export const TerminalResponse = new TerminalResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class Terminal$Type extends MessageType<Terminal> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.Terminal", [
            { no: 1, name: "id", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "serialNumber", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 3, name: "title", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 4, name: "status", kind: "enum", T: () => ["ru.sbertroika.tms.gate.v1.TerminalStatus", TerminalStatus] },
            { no: 5, name: "versionPO", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 6, name: "type", kind: "message", T: () => TerminalType },
            { no: 7, name: "organizationId", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 10, name: "imei", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 11, name: "wifiMac", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 12, name: "bluetoothMac", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 13, name: "ethernetMac", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 14, name: "isActivated", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 15, name: "tid", kind: "scalar", T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<Terminal>): Terminal {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.id = "";
        message.serialNumber = "";
        message.title = "";
        message.status = 0;
        message.versionPO = "";
        message.organizationId = "";
        message.imei = "";
        message.wifiMac = "";
        message.bluetoothMac = "";
        message.ethernetMac = "";
        message.isActivated = false;
        message.tid = "";
        if (value !== undefined)
            reflectionMergePartial<Terminal>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: Terminal): Terminal {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* string id */ 1:
                    message.id = reader.string();
                    break;
                case /* string serialNumber */ 2:
                    message.serialNumber = reader.string();
                    break;
                case /* string title */ 3:
                    message.title = reader.string();
                    break;
                case /* ru.sbertroika.tms.gate.v1.TerminalStatus status */ 4:
                    message.status = reader.int32();
                    break;
                case /* string versionPO */ 5:
                    message.versionPO = reader.string();
                    break;
                case /* ru.sbertroika.tms.gate.v1.TerminalType type */ 6:
                    message.type = TerminalType.internalBinaryRead(reader, reader.uint32(), options, message.type);
                    break;
                case /* string organizationId */ 7:
                    message.organizationId = reader.string();
                    break;
                case /* string imei */ 10:
                    message.imei = reader.string();
                    break;
                case /* string wifiMac */ 11:
                    message.wifiMac = reader.string();
                    break;
                case /* string bluetoothMac */ 12:
                    message.bluetoothMac = reader.string();
                    break;
                case /* string ethernetMac */ 13:
                    message.ethernetMac = reader.string();
                    break;
                case /* bool isActivated */ 14:
                    message.isActivated = reader.bool();
                    break;
                case /* string tid */ 15:
                    message.tid = reader.string();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: Terminal, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* string id = 1; */
        if (message.id !== "")
            writer.tag(1, WireType.LengthDelimited).string(message.id);
        /* string serialNumber = 2; */
        if (message.serialNumber !== "")
            writer.tag(2, WireType.LengthDelimited).string(message.serialNumber);
        /* string title = 3; */
        if (message.title !== "")
            writer.tag(3, WireType.LengthDelimited).string(message.title);
        /* ru.sbertroika.tms.gate.v1.TerminalStatus status = 4; */
        if (message.status !== 0)
            writer.tag(4, WireType.Varint).int32(message.status);
        /* string versionPO = 5; */
        if (message.versionPO !== "")
            writer.tag(5, WireType.LengthDelimited).string(message.versionPO);
        /* ru.sbertroika.tms.gate.v1.TerminalType type = 6; */
        if (message.type)
            TerminalType.internalBinaryWrite(message.type, writer.tag(6, WireType.LengthDelimited).fork(), options).join();
        /* string organizationId = 7; */
        if (message.organizationId !== "")
            writer.tag(7, WireType.LengthDelimited).string(message.organizationId);
        /* string imei = 10; */
        if (message.imei !== "")
            writer.tag(10, WireType.LengthDelimited).string(message.imei);
        /* string wifiMac = 11; */
        if (message.wifiMac !== "")
            writer.tag(11, WireType.LengthDelimited).string(message.wifiMac);
        /* string bluetoothMac = 12; */
        if (message.bluetoothMac !== "")
            writer.tag(12, WireType.LengthDelimited).string(message.bluetoothMac);
        /* string ethernetMac = 13; */
        if (message.ethernetMac !== "")
            writer.tag(13, WireType.LengthDelimited).string(message.ethernetMac);
        /* bool isActivated = 14; */
        if (message.isActivated !== false)
            writer.tag(14, WireType.Varint).bool(message.isActivated);
        /* string tid = 15; */
        if (message.tid !== "")
            writer.tag(15, WireType.LengthDelimited).string(message.tid);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.Terminal
 */
export const Terminal = new Terminal$Type();
// @generated message type with reflection information, may provide speed optimized methods
class TerminalListRequest$Type extends MessageType<TerminalListRequest> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.TerminalListRequest", [
            { no: 1, name: "serialNumber", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "organizationId", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 3, name: "status", kind: "enum", opt: true, T: () => ["ru.sbertroika.tms.gate.v1.TerminalStatus", TerminalStatus] },
            { no: 4, name: "pagination", kind: "message", T: () => PaginationRequest }
        ]);
    }
    create(value?: PartialMessage<TerminalListRequest>): TerminalListRequest {
        const message = globalThis.Object.create((this.messagePrototype!));
        if (value !== undefined)
            reflectionMergePartial<TerminalListRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: TerminalListRequest): TerminalListRequest {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* optional string serialNumber */ 1:
                    message.serialNumber = reader.string();
                    break;
                case /* optional string organizationId */ 2:
                    message.organizationId = reader.string();
                    break;
                case /* optional ru.sbertroika.tms.gate.v1.TerminalStatus status */ 3:
                    message.status = reader.int32();
                    break;
                case /* optional ru.sbertroika.common.v1.PaginationRequest pagination */ 4:
                    message.pagination = PaginationRequest.internalBinaryRead(reader, reader.uint32(), options, message.pagination);
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: TerminalListRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* optional string serialNumber = 1; */
        if (message.serialNumber !== undefined)
            writer.tag(1, WireType.LengthDelimited).string(message.serialNumber);
        /* optional string organizationId = 2; */
        if (message.organizationId !== undefined)
            writer.tag(2, WireType.LengthDelimited).string(message.organizationId);
        /* optional ru.sbertroika.tms.gate.v1.TerminalStatus status = 3; */
        if (message.status !== undefined)
            writer.tag(3, WireType.Varint).int32(message.status);
        /* optional ru.sbertroika.common.v1.PaginationRequest pagination = 4; */
        if (message.pagination)
            PaginationRequest.internalBinaryWrite(message.pagination, writer.tag(4, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.TerminalListRequest
 */
export const TerminalListRequest = new TerminalListRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class TerminalListResponse$Type extends MessageType<TerminalListResponse> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.TerminalListResponse", [
            { no: 1, name: "error", kind: "message", oneof: "response", T: () => OperationError },
            { no: 2, name: "result", kind: "message", oneof: "response", T: () => TerminalList }
        ]);
    }
    create(value?: PartialMessage<TerminalListResponse>): TerminalListResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.response = { oneofKind: undefined };
        if (value !== undefined)
            reflectionMergePartial<TerminalListResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: TerminalListResponse): TerminalListResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* ru.sbertroika.common.v1.OperationError error */ 1:
                    message.response = {
                        oneofKind: "error",
                        error: OperationError.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).error)
                    };
                    break;
                case /* ru.sbertroika.tms.gate.v1.TerminalList result */ 2:
                    message.response = {
                        oneofKind: "result",
                        result: TerminalList.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).result)
                    };
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: TerminalListResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* ru.sbertroika.common.v1.OperationError error = 1; */
        if (message.response.oneofKind === "error")
            OperationError.internalBinaryWrite(message.response.error, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* ru.sbertroika.tms.gate.v1.TerminalList result = 2; */
        if (message.response.oneofKind === "result")
            TerminalList.internalBinaryWrite(message.response.result, writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.TerminalListResponse
 */
export const TerminalListResponse = new TerminalListResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class TerminalList$Type extends MessageType<TerminalList> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.TerminalList", [
            { no: 1, name: "pagination", kind: "message", T: () => PaginationResponse },
            { no: 2, name: "terminal", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => Terminal }
        ]);
    }
    create(value?: PartialMessage<TerminalList>): TerminalList {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.terminal = [];
        if (value !== undefined)
            reflectionMergePartial<TerminalList>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: TerminalList): TerminalList {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* optional ru.sbertroika.common.v1.PaginationResponse pagination */ 1:
                    message.pagination = PaginationResponse.internalBinaryRead(reader, reader.uint32(), options, message.pagination);
                    break;
                case /* repeated ru.sbertroika.tms.gate.v1.Terminal terminal */ 2:
                    message.terminal.push(Terminal.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: TerminalList, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* optional ru.sbertroika.common.v1.PaginationResponse pagination = 1; */
        if (message.pagination)
            PaginationResponse.internalBinaryWrite(message.pagination, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* repeated ru.sbertroika.tms.gate.v1.Terminal terminal = 2; */
        for (let i = 0; i < message.terminal.length; i++)
            Terminal.internalBinaryWrite(message.terminal[i], writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.TerminalList
 */
export const TerminalList = new TerminalList$Type();
// @generated message type with reflection information, may provide speed optimized methods
class DeleteTerminalRequest$Type extends MessageType<DeleteTerminalRequest> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.DeleteTerminalRequest", [
            { no: 1, name: "serialNumber", kind: "scalar", T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<DeleteTerminalRequest>): DeleteTerminalRequest {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.serialNumber = "";
        if (value !== undefined)
            reflectionMergePartial<DeleteTerminalRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: DeleteTerminalRequest): DeleteTerminalRequest {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* string serialNumber */ 1:
                    message.serialNumber = reader.string();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: DeleteTerminalRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* string serialNumber = 1; */
        if (message.serialNumber !== "")
            writer.tag(1, WireType.LengthDelimited).string(message.serialNumber);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.DeleteTerminalRequest
 */
export const DeleteTerminalRequest = new DeleteTerminalRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class DeleteTerminalResponse$Type extends MessageType<DeleteTerminalResponse> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.DeleteTerminalResponse", [
            { no: 1, name: "error", kind: "message", oneof: "response", T: () => OperationError },
            { no: 2, name: "result", kind: "scalar", oneof: "response", T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<DeleteTerminalResponse>): DeleteTerminalResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.response = { oneofKind: undefined };
        if (value !== undefined)
            reflectionMergePartial<DeleteTerminalResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: DeleteTerminalResponse): DeleteTerminalResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* ru.sbertroika.common.v1.OperationError error */ 1:
                    message.response = {
                        oneofKind: "error",
                        error: OperationError.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).error)
                    };
                    break;
                case /* string result */ 2:
                    message.response = {
                        oneofKind: "result",
                        result: reader.string()
                    };
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: DeleteTerminalResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* ru.sbertroika.common.v1.OperationError error = 1; */
        if (message.response.oneofKind === "error")
            OperationError.internalBinaryWrite(message.response.error, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* string result = 2; */
        if (message.response.oneofKind === "result")
            writer.tag(2, WireType.LengthDelimited).string(message.response.result);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.DeleteTerminalResponse
 */
export const DeleteTerminalResponse = new DeleteTerminalResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class BlockTerminalRequest$Type extends MessageType<BlockTerminalRequest> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.BlockTerminalRequest", [
            { no: 1, name: "serialNumber", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "isBlock", kind: "scalar", T: 8 /*ScalarType.BOOL*/ }
        ]);
    }
    create(value?: PartialMessage<BlockTerminalRequest>): BlockTerminalRequest {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.serialNumber = "";
        message.isBlock = false;
        if (value !== undefined)
            reflectionMergePartial<BlockTerminalRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: BlockTerminalRequest): BlockTerminalRequest {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* string serialNumber */ 1:
                    message.serialNumber = reader.string();
                    break;
                case /* bool isBlock */ 2:
                    message.isBlock = reader.bool();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: BlockTerminalRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* string serialNumber = 1; */
        if (message.serialNumber !== "")
            writer.tag(1, WireType.LengthDelimited).string(message.serialNumber);
        /* bool isBlock = 2; */
        if (message.isBlock !== false)
            writer.tag(2, WireType.Varint).bool(message.isBlock);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.BlockTerminalRequest
 */
export const BlockTerminalRequest = new BlockTerminalRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class BlockTerminalResponse$Type extends MessageType<BlockTerminalResponse> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.BlockTerminalResponse", [
            { no: 1, name: "error", kind: "message", oneof: "response", T: () => OperationError },
            { no: 2, name: "result", kind: "scalar", oneof: "response", T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<BlockTerminalResponse>): BlockTerminalResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.response = { oneofKind: undefined };
        if (value !== undefined)
            reflectionMergePartial<BlockTerminalResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: BlockTerminalResponse): BlockTerminalResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* ru.sbertroika.common.v1.OperationError error */ 1:
                    message.response = {
                        oneofKind: "error",
                        error: OperationError.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).error)
                    };
                    break;
                case /* string result */ 2:
                    message.response = {
                        oneofKind: "result",
                        result: reader.string()
                    };
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: BlockTerminalResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* ru.sbertroika.common.v1.OperationError error = 1; */
        if (message.response.oneofKind === "error")
            OperationError.internalBinaryWrite(message.response.error, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* string result = 2; */
        if (message.response.oneofKind === "result")
            writer.tag(2, WireType.LengthDelimited).string(message.response.result);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.BlockTerminalResponse
 */
export const BlockTerminalResponse = new BlockTerminalResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class TerminalType$Type extends MessageType<TerminalType> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.TerminalType", [
            { no: 1, name: "id", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "name", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 3, name: "slug", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 4, name: "comment", kind: "scalar", T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<TerminalType>): TerminalType {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.name = "";
        message.slug = "";
        message.comment = "";
        if (value !== undefined)
            reflectionMergePartial<TerminalType>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: TerminalType): TerminalType {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* optional string id */ 1:
                    message.id = reader.string();
                    break;
                case /* string name */ 2:
                    message.name = reader.string();
                    break;
                case /* string slug */ 3:
                    message.slug = reader.string();
                    break;
                case /* string comment */ 4:
                    message.comment = reader.string();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: TerminalType, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* optional string id = 1; */
        if (message.id !== undefined)
            writer.tag(1, WireType.LengthDelimited).string(message.id);
        /* string name = 2; */
        if (message.name !== "")
            writer.tag(2, WireType.LengthDelimited).string(message.name);
        /* string slug = 3; */
        if (message.slug !== "")
            writer.tag(3, WireType.LengthDelimited).string(message.slug);
        /* string comment = 4; */
        if (message.comment !== "")
            writer.tag(4, WireType.LengthDelimited).string(message.comment);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.TerminalType
 */
export const TerminalType = new TerminalType$Type();
// @generated message type with reflection information, may provide speed optimized methods
class TerminalTypeResponse$Type extends MessageType<TerminalTypeResponse> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.TerminalTypeResponse", [
            { no: 1, name: "error", kind: "message", oneof: "response", T: () => OperationError },
            { no: 2, name: "result", kind: "message", oneof: "response", T: () => TerminalType }
        ]);
    }
    create(value?: PartialMessage<TerminalTypeResponse>): TerminalTypeResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.response = { oneofKind: undefined };
        if (value !== undefined)
            reflectionMergePartial<TerminalTypeResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: TerminalTypeResponse): TerminalTypeResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* ru.sbertroika.common.v1.OperationError error */ 1:
                    message.response = {
                        oneofKind: "error",
                        error: OperationError.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).error)
                    };
                    break;
                case /* ru.sbertroika.tms.gate.v1.TerminalType result */ 2:
                    message.response = {
                        oneofKind: "result",
                        result: TerminalType.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).result)
                    };
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: TerminalTypeResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* ru.sbertroika.common.v1.OperationError error = 1; */
        if (message.response.oneofKind === "error")
            OperationError.internalBinaryWrite(message.response.error, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* ru.sbertroika.tms.gate.v1.TerminalType result = 2; */
        if (message.response.oneofKind === "result")
            TerminalType.internalBinaryWrite(message.response.result, writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.TerminalTypeResponse
 */
export const TerminalTypeResponse = new TerminalTypeResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class TerminalTypeListResponse$Type extends MessageType<TerminalTypeListResponse> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.TerminalTypeListResponse", [
            { no: 1, name: "error", kind: "message", oneof: "response", T: () => OperationError },
            { no: 2, name: "result", kind: "message", oneof: "response", T: () => TerminalTypeList }
        ]);
    }
    create(value?: PartialMessage<TerminalTypeListResponse>): TerminalTypeListResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.response = { oneofKind: undefined };
        if (value !== undefined)
            reflectionMergePartial<TerminalTypeListResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: TerminalTypeListResponse): TerminalTypeListResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* ru.sbertroika.common.v1.OperationError error */ 1:
                    message.response = {
                        oneofKind: "error",
                        error: OperationError.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).error)
                    };
                    break;
                case /* ru.sbertroika.tms.gate.v1.TerminalTypeList result */ 2:
                    message.response = {
                        oneofKind: "result",
                        result: TerminalTypeList.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).result)
                    };
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: TerminalTypeListResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* ru.sbertroika.common.v1.OperationError error = 1; */
        if (message.response.oneofKind === "error")
            OperationError.internalBinaryWrite(message.response.error, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* ru.sbertroika.tms.gate.v1.TerminalTypeList result = 2; */
        if (message.response.oneofKind === "result")
            TerminalTypeList.internalBinaryWrite(message.response.result, writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.TerminalTypeListResponse
 */
export const TerminalTypeListResponse = new TerminalTypeListResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class TerminalTypeList$Type extends MessageType<TerminalTypeList> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.TerminalTypeList", [
            { no: 2, name: "terminalType", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => TerminalType }
        ]);
    }
    create(value?: PartialMessage<TerminalTypeList>): TerminalTypeList {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.terminalType = [];
        if (value !== undefined)
            reflectionMergePartial<TerminalTypeList>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: TerminalTypeList): TerminalTypeList {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated ru.sbertroika.tms.gate.v1.TerminalType terminalType */ 2:
                    message.terminalType.push(TerminalType.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: TerminalTypeList, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated ru.sbertroika.tms.gate.v1.TerminalType terminalType = 2; */
        for (let i = 0; i < message.terminalType.length; i++)
            TerminalType.internalBinaryWrite(message.terminalType[i], writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.TerminalTypeList
 */
export const TerminalTypeList = new TerminalTypeList$Type();
// @generated message type with reflection information, may provide speed optimized methods
class JournalListFilter$Type extends MessageType<JournalListFilter> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.JournalListFilter", [
            { no: 1, name: "terminalSerial", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "eventType", kind: "enum", repeat: 1 /*RepeatType.PACKED*/, T: () => ["ru.sbertroika.common.tms.EventType", EventType] },
            { no: 3, name: "createdAtFrom", kind: "message", T: () => Timestamp },
            { no: 4, name: "createdAtTo", kind: "message", T: () => Timestamp },
            { no: 5, name: "userId", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 6, name: "shiftNum", kind: "scalar", opt: true, T: 13 /*ScalarType.UINT32*/ },
            { no: 7, name: "ern", kind: "scalar", opt: true, T: 13 /*ScalarType.UINT32*/ }
        ]);
    }
    create(value?: PartialMessage<JournalListFilter>): JournalListFilter {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.eventType = [];
        if (value !== undefined)
            reflectionMergePartial<JournalListFilter>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: JournalListFilter): JournalListFilter {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* optional string terminalSerial */ 1:
                    message.terminalSerial = reader.string();
                    break;
                case /* repeated ru.sbertroika.common.tms.EventType eventType */ 2:
                    if (wireType === WireType.LengthDelimited)
                        for (let e = reader.int32() + reader.pos; reader.pos < e;)
                            message.eventType.push(reader.int32());
                    else
                        message.eventType.push(reader.int32());
                    break;
                case /* optional google.protobuf.Timestamp createdAtFrom */ 3:
                    message.createdAtFrom = Timestamp.internalBinaryRead(reader, reader.uint32(), options, message.createdAtFrom);
                    break;
                case /* optional google.protobuf.Timestamp createdAtTo */ 4:
                    message.createdAtTo = Timestamp.internalBinaryRead(reader, reader.uint32(), options, message.createdAtTo);
                    break;
                case /* optional string userId */ 5:
                    message.userId = reader.string();
                    break;
                case /* optional uint32 shiftNum */ 6:
                    message.shiftNum = reader.uint32();
                    break;
                case /* optional uint32 ern */ 7:
                    message.ern = reader.uint32();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: JournalListFilter, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* optional string terminalSerial = 1; */
        if (message.terminalSerial !== undefined)
            writer.tag(1, WireType.LengthDelimited).string(message.terminalSerial);
        /* repeated ru.sbertroika.common.tms.EventType eventType = 2; */
        if (message.eventType.length) {
            writer.tag(2, WireType.LengthDelimited).fork();
            for (let i = 0; i < message.eventType.length; i++)
                writer.int32(message.eventType[i]);
            writer.join();
        }
        /* optional google.protobuf.Timestamp createdAtFrom = 3; */
        if (message.createdAtFrom)
            Timestamp.internalBinaryWrite(message.createdAtFrom, writer.tag(3, WireType.LengthDelimited).fork(), options).join();
        /* optional google.protobuf.Timestamp createdAtTo = 4; */
        if (message.createdAtTo)
            Timestamp.internalBinaryWrite(message.createdAtTo, writer.tag(4, WireType.LengthDelimited).fork(), options).join();
        /* optional string userId = 5; */
        if (message.userId !== undefined)
            writer.tag(5, WireType.LengthDelimited).string(message.userId);
        /* optional uint32 shiftNum = 6; */
        if (message.shiftNum !== undefined)
            writer.tag(6, WireType.Varint).uint32(message.shiftNum);
        /* optional uint32 ern = 7; */
        if (message.ern !== undefined)
            writer.tag(7, WireType.Varint).uint32(message.ern);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.JournalListFilter
 */
export const JournalListFilter = new JournalListFilter$Type();
// @generated message type with reflection information, may provide speed optimized methods
class JournalListRequest$Type extends MessageType<JournalListRequest> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.JournalListRequest", [
            { no: 1, name: "pagination", kind: "message", T: () => PaginationRequest },
            { no: 2, name: "filter", kind: "message", T: () => JournalListFilter }
        ]);
    }
    create(value?: PartialMessage<JournalListRequest>): JournalListRequest {
        const message = globalThis.Object.create((this.messagePrototype!));
        if (value !== undefined)
            reflectionMergePartial<JournalListRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: JournalListRequest): JournalListRequest {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* optional ru.sbertroika.common.v1.PaginationRequest pagination */ 1:
                    message.pagination = PaginationRequest.internalBinaryRead(reader, reader.uint32(), options, message.pagination);
                    break;
                case /* optional ru.sbertroika.tms.gate.v1.JournalListFilter filter */ 2:
                    message.filter = JournalListFilter.internalBinaryRead(reader, reader.uint32(), options, message.filter);
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: JournalListRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* optional ru.sbertroika.common.v1.PaginationRequest pagination = 1; */
        if (message.pagination)
            PaginationRequest.internalBinaryWrite(message.pagination, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* optional ru.sbertroika.tms.gate.v1.JournalListFilter filter = 2; */
        if (message.filter)
            JournalListFilter.internalBinaryWrite(message.filter, writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.JournalListRequest
 */
export const JournalListRequest = new JournalListRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class TerminalJournalEvent$Type extends MessageType<TerminalJournalEvent> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.TerminalJournalEvent", [
            { no: 1, name: "eventType", kind: "enum", T: () => ["ru.sbertroika.common.tms.EventType", EventType] },
            { no: 2, name: "createdAt", kind: "message", T: () => Timestamp },
            { no: 3, name: "terminalSerial", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 4, name: "ern", kind: "scalar", opt: true, T: 13 /*ScalarType.UINT32*/ },
            { no: 5, name: "userId", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 6, name: "shiftNum", kind: "scalar", opt: true, T: 13 /*ScalarType.UINT32*/ },
            { no: 7, name: "stopListVersion", kind: "scalar", opt: true, T: 13 /*ScalarType.UINT32*/ },
            { no: 8, name: "stopListUpdate", kind: "message", T: () => Timestamp },
            { no: 9, name: "errorCode", kind: "scalar", opt: true, T: 5 /*ScalarType.INT32*/ },
            { no: 10, name: "errorMessage", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ },
            { no: 11, name: "value", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<TerminalJournalEvent>): TerminalJournalEvent {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.eventType = 0;
        message.terminalSerial = "";
        if (value !== undefined)
            reflectionMergePartial<TerminalJournalEvent>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: TerminalJournalEvent): TerminalJournalEvent {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* ru.sbertroika.common.tms.EventType eventType */ 1:
                    message.eventType = reader.int32();
                    break;
                case /* google.protobuf.Timestamp createdAt */ 2:
                    message.createdAt = Timestamp.internalBinaryRead(reader, reader.uint32(), options, message.createdAt);
                    break;
                case /* string terminalSerial */ 3:
                    message.terminalSerial = reader.string();
                    break;
                case /* optional uint32 ern */ 4:
                    message.ern = reader.uint32();
                    break;
                case /* optional string userId */ 5:
                    message.userId = reader.string();
                    break;
                case /* optional uint32 shiftNum */ 6:
                    message.shiftNum = reader.uint32();
                    break;
                case /* optional uint32 stopListVersion */ 7:
                    message.stopListVersion = reader.uint32();
                    break;
                case /* optional google.protobuf.Timestamp stopListUpdate */ 8:
                    message.stopListUpdate = Timestamp.internalBinaryRead(reader, reader.uint32(), options, message.stopListUpdate);
                    break;
                case /* optional int32 errorCode */ 9:
                    message.errorCode = reader.int32();
                    break;
                case /* optional string errorMessage */ 10:
                    message.errorMessage = reader.string();
                    break;
                case /* optional string value */ 11:
                    message.value = reader.string();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: TerminalJournalEvent, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* ru.sbertroika.common.tms.EventType eventType = 1; */
        if (message.eventType !== 0)
            writer.tag(1, WireType.Varint).int32(message.eventType);
        /* google.protobuf.Timestamp createdAt = 2; */
        if (message.createdAt)
            Timestamp.internalBinaryWrite(message.createdAt, writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        /* string terminalSerial = 3; */
        if (message.terminalSerial !== "")
            writer.tag(3, WireType.LengthDelimited).string(message.terminalSerial);
        /* optional uint32 ern = 4; */
        if (message.ern !== undefined)
            writer.tag(4, WireType.Varint).uint32(message.ern);
        /* optional string userId = 5; */
        if (message.userId !== undefined)
            writer.tag(5, WireType.LengthDelimited).string(message.userId);
        /* optional uint32 shiftNum = 6; */
        if (message.shiftNum !== undefined)
            writer.tag(6, WireType.Varint).uint32(message.shiftNum);
        /* optional uint32 stopListVersion = 7; */
        if (message.stopListVersion !== undefined)
            writer.tag(7, WireType.Varint).uint32(message.stopListVersion);
        /* optional google.protobuf.Timestamp stopListUpdate = 8; */
        if (message.stopListUpdate)
            Timestamp.internalBinaryWrite(message.stopListUpdate, writer.tag(8, WireType.LengthDelimited).fork(), options).join();
        /* optional int32 errorCode = 9; */
        if (message.errorCode !== undefined)
            writer.tag(9, WireType.Varint).int32(message.errorCode);
        /* optional string errorMessage = 10; */
        if (message.errorMessage !== undefined)
            writer.tag(10, WireType.LengthDelimited).string(message.errorMessage);
        /* optional string value = 11; */
        if (message.value !== undefined)
            writer.tag(11, WireType.LengthDelimited).string(message.value);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.TerminalJournalEvent
 */
export const TerminalJournalEvent = new TerminalJournalEvent$Type();
// @generated message type with reflection information, may provide speed optimized methods
class JournalListResult$Type extends MessageType<JournalListResult> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.JournalListResult", [
            { no: 1, name: "pagination", kind: "message", T: () => PaginationResponse },
            { no: 2, name: "filter", kind: "message", T: () => JournalListFilter },
            { no: 3, name: "event", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => TerminalJournalEvent }
        ]);
    }
    create(value?: PartialMessage<JournalListResult>): JournalListResult {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.event = [];
        if (value !== undefined)
            reflectionMergePartial<JournalListResult>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: JournalListResult): JournalListResult {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* optional ru.sbertroika.common.v1.PaginationResponse pagination */ 1:
                    message.pagination = PaginationResponse.internalBinaryRead(reader, reader.uint32(), options, message.pagination);
                    break;
                case /* optional ru.sbertroika.tms.gate.v1.JournalListFilter filter */ 2:
                    message.filter = JournalListFilter.internalBinaryRead(reader, reader.uint32(), options, message.filter);
                    break;
                case /* repeated ru.sbertroika.tms.gate.v1.TerminalJournalEvent event */ 3:
                    message.event.push(TerminalJournalEvent.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: JournalListResult, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* optional ru.sbertroika.common.v1.PaginationResponse pagination = 1; */
        if (message.pagination)
            PaginationResponse.internalBinaryWrite(message.pagination, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* optional ru.sbertroika.tms.gate.v1.JournalListFilter filter = 2; */
        if (message.filter)
            JournalListFilter.internalBinaryWrite(message.filter, writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        /* repeated ru.sbertroika.tms.gate.v1.TerminalJournalEvent event = 3; */
        for (let i = 0; i < message.event.length; i++)
            TerminalJournalEvent.internalBinaryWrite(message.event[i], writer.tag(3, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.JournalListResult
 */
export const JournalListResult = new JournalListResult$Type();
// @generated message type with reflection information, may provide speed optimized methods
class JournalListResponse$Type extends MessageType<JournalListResponse> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.JournalListResponse", [
            { no: 1, name: "error", kind: "message", oneof: "response", T: () => OperationError },
            { no: 2, name: "result", kind: "message", oneof: "response", T: () => JournalListResult }
        ]);
    }
    create(value?: PartialMessage<JournalListResponse>): JournalListResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.response = { oneofKind: undefined };
        if (value !== undefined)
            reflectionMergePartial<JournalListResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: JournalListResponse): JournalListResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* ru.sbertroika.common.v1.OperationError error */ 1:
                    message.response = {
                        oneofKind: "error",
                        error: OperationError.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).error)
                    };
                    break;
                case /* ru.sbertroika.tms.gate.v1.JournalListResult result */ 2:
                    message.response = {
                        oneofKind: "result",
                        result: JournalListResult.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).result)
                    };
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: JournalListResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* ru.sbertroika.common.v1.OperationError error = 1; */
        if (message.response.oneofKind === "error")
            OperationError.internalBinaryWrite(message.response.error, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* ru.sbertroika.tms.gate.v1.JournalListResult result = 2; */
        if (message.response.oneofKind === "result")
            JournalListResult.internalBinaryWrite(message.response.result, writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.JournalListResponse
 */
export const JournalListResponse = new JournalListResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class TerminalUserResponse$Type extends MessageType<TerminalUserResponse> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.TerminalUserResponse", [
            { no: 1, name: "error", kind: "message", oneof: "response", T: () => OperationError },
            { no: 2, name: "result", kind: "message", oneof: "response", T: () => TerminalUser }
        ]);
    }
    create(value?: PartialMessage<TerminalUserResponse>): TerminalUserResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.response = { oneofKind: undefined };
        if (value !== undefined)
            reflectionMergePartial<TerminalUserResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: TerminalUserResponse): TerminalUserResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* ru.sbertroika.common.v1.OperationError error */ 1:
                    message.response = {
                        oneofKind: "error",
                        error: OperationError.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).error)
                    };
                    break;
                case /* ru.sbertroika.tms.gate.v1.TerminalUser result */ 2:
                    message.response = {
                        oneofKind: "result",
                        result: TerminalUser.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).result)
                    };
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: TerminalUserResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* ru.sbertroika.common.v1.OperationError error = 1; */
        if (message.response.oneofKind === "error")
            OperationError.internalBinaryWrite(message.response.error, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* ru.sbertroika.tms.gate.v1.TerminalUser result = 2; */
        if (message.response.oneofKind === "result")
            TerminalUser.internalBinaryWrite(message.response.result, writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.TerminalUserResponse
 */
export const TerminalUserResponse = new TerminalUserResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class EventTypeListResponse$Type extends MessageType<EventTypeListResponse> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.EventTypeListResponse", [
            { no: 1, name: "error", kind: "message", oneof: "response", T: () => OperationError },
            { no: 2, name: "result", kind: "message", oneof: "response", T: () => EventTypeDescriptionList }
        ]);
    }
    create(value?: PartialMessage<EventTypeListResponse>): EventTypeListResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.response = { oneofKind: undefined };
        if (value !== undefined)
            reflectionMergePartial<EventTypeListResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: EventTypeListResponse): EventTypeListResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* ru.sbertroika.common.v1.OperationError error */ 1:
                    message.response = {
                        oneofKind: "error",
                        error: OperationError.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).error)
                    };
                    break;
                case /* ru.sbertroika.tms.gate.v1.EventTypeDescriptionList result */ 2:
                    message.response = {
                        oneofKind: "result",
                        result: EventTypeDescriptionList.internalBinaryRead(reader, reader.uint32(), options, (message.response as any).result)
                    };
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: EventTypeListResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* ru.sbertroika.common.v1.OperationError error = 1; */
        if (message.response.oneofKind === "error")
            OperationError.internalBinaryWrite(message.response.error, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        /* ru.sbertroika.tms.gate.v1.EventTypeDescriptionList result = 2; */
        if (message.response.oneofKind === "result")
            EventTypeDescriptionList.internalBinaryWrite(message.response.result, writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.EventTypeListResponse
 */
export const EventTypeListResponse = new EventTypeListResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class EventTypeDescriptionList$Type extends MessageType<EventTypeDescriptionList> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.EventTypeDescriptionList", [
            { no: 1, name: "eventType", kind: "message", repeat: 1 /*RepeatType.PACKED*/, T: () => EventTypeDescription }
        ]);
    }
    create(value?: PartialMessage<EventTypeDescriptionList>): EventTypeDescriptionList {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.eventType = [];
        if (value !== undefined)
            reflectionMergePartial<EventTypeDescriptionList>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: EventTypeDescriptionList): EventTypeDescriptionList {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated ru.sbertroika.tms.gate.v1.EventTypeDescription eventType */ 1:
                    message.eventType.push(EventTypeDescription.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: EventTypeDescriptionList, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated ru.sbertroika.tms.gate.v1.EventTypeDescription eventType = 1; */
        for (let i = 0; i < message.eventType.length; i++)
            EventTypeDescription.internalBinaryWrite(message.eventType[i], writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.EventTypeDescriptionList
 */
export const EventTypeDescriptionList = new EventTypeDescriptionList$Type();
// @generated message type with reflection information, may provide speed optimized methods
class EventTypeDescription$Type extends MessageType<EventTypeDescription> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.EventTypeDescription", [
            { no: 1, name: "type", kind: "enum", T: () => ["ru.sbertroika.common.tms.EventType", EventType] },
            { no: 2, name: "description", kind: "scalar", T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<EventTypeDescription>): EventTypeDescription {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.type = 0;
        message.description = "";
        if (value !== undefined)
            reflectionMergePartial<EventTypeDescription>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: EventTypeDescription): EventTypeDescription {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* ru.sbertroika.common.tms.EventType type */ 1:
                    message.type = reader.int32();
                    break;
                case /* string description */ 2:
                    message.description = reader.string();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: EventTypeDescription, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* ru.sbertroika.common.tms.EventType type = 1; */
        if (message.type !== 0)
            writer.tag(1, WireType.Varint).int32(message.type);
        /* string description = 2; */
        if (message.description !== "")
            writer.tag(2, WireType.LengthDelimited).string(message.description);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.EventTypeDescription
 */
export const EventTypeDescription = new EventTypeDescription$Type();
// @generated message type with reflection information, may provide speed optimized methods
class TerminalTypeListRequest$Type extends MessageType<TerminalTypeListRequest> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.TerminalTypeListRequest", [
            { no: 1, name: "filters", kind: "message", T: () => TerminalTypeFilters }
        ]);
    }
    create(value?: PartialMessage<TerminalTypeListRequest>): TerminalTypeListRequest {
        const message = globalThis.Object.create((this.messagePrototype!));
        if (value !== undefined)
            reflectionMergePartial<TerminalTypeListRequest>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: TerminalTypeListRequest): TerminalTypeListRequest {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* optional ru.sbertroika.tms.gate.v1.TerminalTypeFilters filters */ 1:
                    message.filters = TerminalTypeFilters.internalBinaryRead(reader, reader.uint32(), options, message.filters);
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: TerminalTypeListRequest, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* optional ru.sbertroika.tms.gate.v1.TerminalTypeFilters filters = 1; */
        if (message.filters)
            TerminalTypeFilters.internalBinaryWrite(message.filters, writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.TerminalTypeListRequest
 */
export const TerminalTypeListRequest = new TerminalTypeListRequest$Type();
// @generated message type with reflection information, may provide speed optimized methods
class TerminalTypeFilters$Type extends MessageType<TerminalTypeFilters> {
    constructor() {
        super("ru.sbertroika.tms.gate.v1.TerminalTypeFilters", [
            { no: 1, name: "name", kind: "scalar", opt: true, T: 9 /*ScalarType.STRING*/ }
        ]);
    }
    create(value?: PartialMessage<TerminalTypeFilters>): TerminalTypeFilters {
        const message = globalThis.Object.create((this.messagePrototype!));
        if (value !== undefined)
            reflectionMergePartial<TerminalTypeFilters>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: TerminalTypeFilters): TerminalTypeFilters {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* optional string name */ 1:
                    message.name = reader.string();
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: TerminalTypeFilters, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* optional string name = 1; */
        if (message.name !== undefined)
            writer.tag(1, WireType.LengthDelimited).string(message.name);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message ru.sbertroika.tms.gate.v1.TerminalTypeFilters
 */
export const TerminalTypeFilters = new TerminalTypeFilters$Type();
/**
 * @generated ServiceType for protobuf service ru.sbertroika.tms.gate.v1.TMSGatePrivateService
 */
export const TMSGatePrivateService = new ServiceType("ru.sbertroika.tms.gate.v1.TMSGatePrivateService", [
    { name: "terminalUserList", options: {}, I: TerminalUsersListRequest, O: TerminalUsersListResponse },
    { name: "changePasswordUser", options: {}, I: ChangePasswordRequest, O: EmptyResponse },
    { name: "registrationUser", options: {}, I: UserReg, O: EmptyResponse },
    { name: "updateUser", options: {}, I: UpdateUserReq, O: EmptyResponse },
    { name: "blockUser", options: {}, I: BlockUserRequest, O: EmptyResponse },
    { name: "groups", options: {}, I: Empty, O: GroupsResponse },
    { name: "roles", options: {}, I: Empty, O: RolesResponse },
    { name: "createTerminal", options: {}, I: TerminalRequest, O: TerminalResponse },
    { name: "terminalList", options: {}, I: TerminalListRequest, O: TerminalListResponse },
    { name: "deleteTerminal", options: {}, I: DeleteTerminalRequest, O: DeleteTerminalResponse },
    { name: "blockTerminal", options: {}, I: BlockTerminalRequest, O: BlockTerminalResponse },
    { name: "updateTerminal", options: {}, I: TerminalRequest, O: TerminalResponse },
    { name: "createTerminalType", options: {}, I: TerminalType, O: TerminalTypeResponse },
    { name: "updateTerminalType", options: {}, I: TerminalType, O: TerminalTypeResponse },
    { name: "terminalTypeList", options: {}, I: TerminalTypeListRequest, O: TerminalTypeListResponse },
    { name: "journalList", options: {}, I: JournalListRequest, O: JournalListResponse },
    { name: "getEventTypeList", options: {}, I: Empty, O: EventTypeListResponse },
    { name: "getTerminalUserById", options: {}, I: ByIdRequest, O: TerminalUserResponse },
    { name: "getTerminalTypeById", options: {}, I: ByIdRequest, O: TerminalTypeResponse },
    { name: "getTerminalById", options: {}, I: ByIdRequest, O: TerminalResponse }
]);
