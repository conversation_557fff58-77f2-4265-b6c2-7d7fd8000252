// @generated by protobuf-ts 2.9.3
// @generated from protobuf file "tms-gate-private.proto" (package "ru.sbertroika.tms.gate.v1", syntax proto3)
// tslint:disable
import type { RpcTransport } from "@protobuf-ts/runtime-rpc";
import type { ServiceInfo } from "@protobuf-ts/runtime-rpc";
import { TMSGatePrivateService } from "./tms-gate-private";
import type { TerminalUserResponse } from "./tms-gate-private";
import type { ByIdRequest } from "./common";
import type { EventTypeListResponse } from "./tms-gate-private";
import type { JournalListResponse } from "./tms-gate-private";
import type { JournalListRequest } from "./tms-gate-private";
import type { TerminalTypeListResponse } from "./tms-gate-private";
import type { TerminalTypeListRequest } from "./tms-gate-private";
import type { TerminalTypeResponse } from "./tms-gate-private";
import type { TerminalType } from "./tms-gate-private";
import type { BlockTerminalResponse } from "./tms-gate-private";
import type { BlockTerminalRequest } from "./tms-gate-private";
import type { DeleteTerminalResponse } from "./tms-gate-private";
import type { DeleteTerminalRequest } from "./tms-gate-private";
import type { TerminalListResponse } from "./tms-gate-private";
import type { TerminalListRequest } from "./tms-gate-private";
import type { TerminalResponse } from "./tms-gate-private";
import type { TerminalRequest } from "./tms-gate-private";
import type { RolesResponse } from "./tms-gate-private";
import type { GroupsResponse } from "./tms-gate-private";
import type { Empty } from "./google/protobuf/empty";
import type { BlockUserRequest } from "./tms-gate-private";
import type { UpdateUserReq } from "./tms-gate-private";
import type { UserReg } from "./tms-gate-private";
import type { EmptyResponse } from "./common";
import type { ChangePasswordRequest } from "./tms-gate-private";
import { stackIntercept } from "@protobuf-ts/runtime-rpc";
import type { TerminalUsersListResponse } from "./tms-gate-private";
import type { TerminalUsersListRequest } from "./tms-gate-private";
import type { UnaryCall } from "@protobuf-ts/runtime-rpc";
import type { RpcOptions } from "@protobuf-ts/runtime-rpc";
/**
 * @generated from protobuf service ru.sbertroika.tms.gate.v1.TMSGatePrivateService
 */
export interface ITMSGatePrivateServiceClient {
    /**
     * Список пользователей с фильтрацией
     *
     * @generated from protobuf rpc: terminalUserList(ru.sbertroika.tms.gate.v1.TerminalUsersListRequest) returns (ru.sbertroika.tms.gate.v1.TerminalUsersListResponse);
     */
    terminalUserList(input: TerminalUsersListRequest, options?: RpcOptions): UnaryCall<TerminalUsersListRequest, TerminalUsersListResponse>;
    /**
     * Смена пароля с пользователем
     *
     * @generated from protobuf rpc: changePasswordUser(ru.sbertroika.tms.gate.v1.ChangePasswordRequest) returns (ru.sbertroika.common.v1.EmptyResponse);
     */
    changePasswordUser(input: ChangePasswordRequest, options?: RpcOptions): UnaryCall<ChangePasswordRequest, EmptyResponse>;
    /**
     * Регистрация пользователя
     *
     * @generated from protobuf rpc: registrationUser(ru.sbertroika.tms.gate.v1.UserReg) returns (ru.sbertroika.common.v1.EmptyResponse);
     */
    registrationUser(input: UserReg, options?: RpcOptions): UnaryCall<UserReg, EmptyResponse>;
    /**
     * Обновление данных пользователя
     *
     * @generated from protobuf rpc: updateUser(ru.sbertroika.tms.gate.v1.UpdateUserReq) returns (ru.sbertroika.common.v1.EmptyResponse);
     */
    updateUser(input: UpdateUserReq, options?: RpcOptions): UnaryCall<UpdateUserReq, EmptyResponse>;
    /**
     * Блокировка юзера
     *
     * @generated from protobuf rpc: blockUser(ru.sbertroika.tms.gate.v1.BlockUserRequest) returns (ru.sbertroika.common.v1.EmptyResponse);
     */
    blockUser(input: BlockUserRequest, options?: RpcOptions): UnaryCall<BlockUserRequest, EmptyResponse>;
    /**
     * Список групп
     *
     * @generated from protobuf rpc: groups(google.protobuf.Empty) returns (ru.sbertroika.tms.gate.v1.GroupsResponse);
     */
    groups(input: Empty, options?: RpcOptions): UnaryCall<Empty, GroupsResponse>;
    /**
     * Список ролей
     *
     * @generated from protobuf rpc: roles(google.protobuf.Empty) returns (ru.sbertroika.tms.gate.v1.RolesResponse);
     */
    roles(input: Empty, options?: RpcOptions): UnaryCall<Empty, RolesResponse>;
    /**
     * Создать терминал
     *
     * @generated from protobuf rpc: createTerminal(ru.sbertroika.tms.gate.v1.TerminalRequest) returns (ru.sbertroika.tms.gate.v1.TerminalResponse);
     */
    createTerminal(input: TerminalRequest, options?: RpcOptions): UnaryCall<TerminalRequest, TerminalResponse>;
    /**
     * Cписок терминалов
     *
     * @generated from protobuf rpc: terminalList(ru.sbertroika.tms.gate.v1.TerminalListRequest) returns (ru.sbertroika.tms.gate.v1.TerminalListResponse);
     */
    terminalList(input: TerminalListRequest, options?: RpcOptions): UnaryCall<TerminalListRequest, TerminalListResponse>;
    /**
     * Удалить терминал
     *
     * @generated from protobuf rpc: deleteTerminal(ru.sbertroika.tms.gate.v1.DeleteTerminalRequest) returns (ru.sbertroika.tms.gate.v1.DeleteTerminalResponse);
     */
    deleteTerminal(input: DeleteTerminalRequest, options?: RpcOptions): UnaryCall<DeleteTerminalRequest, DeleteTerminalResponse>;
    /**
     * Заблокировать/разблокировать терминал
     *
     * @generated from protobuf rpc: blockTerminal(ru.sbertroika.tms.gate.v1.BlockTerminalRequest) returns (ru.sbertroika.tms.gate.v1.BlockTerminalResponse);
     */
    blockTerminal(input: BlockTerminalRequest, options?: RpcOptions): UnaryCall<BlockTerminalRequest, BlockTerminalResponse>;
    /**
     * Обновить данные терминала
     *
     * @generated from protobuf rpc: updateTerminal(ru.sbertroika.tms.gate.v1.TerminalRequest) returns (ru.sbertroika.tms.gate.v1.TerminalResponse);
     */
    updateTerminal(input: TerminalRequest, options?: RpcOptions): UnaryCall<TerminalRequest, TerminalResponse>;
    /**
     * Создать тип терминала
     *
     * @generated from protobuf rpc: createTerminalType(ru.sbertroika.tms.gate.v1.TerminalType) returns (ru.sbertroika.tms.gate.v1.TerminalTypeResponse);
     */
    createTerminalType(input: TerminalType, options?: RpcOptions): UnaryCall<TerminalType, TerminalTypeResponse>;
    /**
     * Обновить тип терминала
     *
     * @generated from protobuf rpc: updateTerminalType(ru.sbertroika.tms.gate.v1.TerminalType) returns (ru.sbertroika.tms.gate.v1.TerminalTypeResponse);
     */
    updateTerminalType(input: TerminalType, options?: RpcOptions): UnaryCall<TerminalType, TerminalTypeResponse>;
    /**
     * Список типов терминалов
     *
     * @generated from protobuf rpc: terminalTypeList(ru.sbertroika.tms.gate.v1.TerminalTypeListRequest) returns (ru.sbertroika.tms.gate.v1.TerminalTypeListResponse);
     */
    terminalTypeList(input: TerminalTypeListRequest, options?: RpcOptions): UnaryCall<TerminalTypeListRequest, TerminalTypeListResponse>;
    /**
     * Журнал терминальных событий
     *
     * @generated from protobuf rpc: journalList(ru.sbertroika.tms.gate.v1.JournalListRequest) returns (ru.sbertroika.tms.gate.v1.JournalListResponse);
     */
    journalList(input: JournalListRequest, options?: RpcOptions): UnaryCall<JournalListRequest, JournalListResponse>;
    /**
     * @generated from protobuf rpc: getEventTypeList(google.protobuf.Empty) returns (ru.sbertroika.tms.gate.v1.EventTypeListResponse);
     */
    getEventTypeList(input: Empty, options?: RpcOptions): UnaryCall<Empty, EventTypeListResponse>;
    /**
     * @generated from protobuf rpc: getTerminalUserById(ru.sbertroika.common.v1.ByIdRequest) returns (ru.sbertroika.tms.gate.v1.TerminalUserResponse);
     */
    getTerminalUserById(input: ByIdRequest, options?: RpcOptions): UnaryCall<ByIdRequest, TerminalUserResponse>;
    /**
     * @generated from protobuf rpc: getTerminalTypeById(ru.sbertroika.common.v1.ByIdRequest) returns (ru.sbertroika.tms.gate.v1.TerminalTypeResponse);
     */
    getTerminalTypeById(input: ByIdRequest, options?: RpcOptions): UnaryCall<ByIdRequest, TerminalTypeResponse>;
    /**
     * @generated from protobuf rpc: getTerminalById(ru.sbertroika.common.v1.ByIdRequest) returns (ru.sbertroika.tms.gate.v1.TerminalResponse);
     */
    getTerminalById(input: ByIdRequest, options?: RpcOptions): UnaryCall<ByIdRequest, TerminalResponse>;
}
/**
 * @generated from protobuf service ru.sbertroika.tms.gate.v1.TMSGatePrivateService
 */
export class TMSGatePrivateServiceClient implements ITMSGatePrivateServiceClient, ServiceInfo {
    typeName = TMSGatePrivateService.typeName;
    methods = TMSGatePrivateService.methods;
    options = TMSGatePrivateService.options;
    constructor(private readonly _transport: RpcTransport) {
    }
    /**
     * Список пользователей с фильтрацией
     *
     * @generated from protobuf rpc: terminalUserList(ru.sbertroika.tms.gate.v1.TerminalUsersListRequest) returns (ru.sbertroika.tms.gate.v1.TerminalUsersListResponse);
     */
    terminalUserList(input: TerminalUsersListRequest, options?: RpcOptions): UnaryCall<TerminalUsersListRequest, TerminalUsersListResponse> {
        const method = this.methods[0], opt = this._transport.mergeOptions(options);
        return stackIntercept<TerminalUsersListRequest, TerminalUsersListResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * Смена пароля с пользователем
     *
     * @generated from protobuf rpc: changePasswordUser(ru.sbertroika.tms.gate.v1.ChangePasswordRequest) returns (ru.sbertroika.common.v1.EmptyResponse);
     */
    changePasswordUser(input: ChangePasswordRequest, options?: RpcOptions): UnaryCall<ChangePasswordRequest, EmptyResponse> {
        const method = this.methods[1], opt = this._transport.mergeOptions(options);
        return stackIntercept<ChangePasswordRequest, EmptyResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * Регистрация пользователя
     *
     * @generated from protobuf rpc: registrationUser(ru.sbertroika.tms.gate.v1.UserReg) returns (ru.sbertroika.common.v1.EmptyResponse);
     */
    registrationUser(input: UserReg, options?: RpcOptions): UnaryCall<UserReg, EmptyResponse> {
        const method = this.methods[2], opt = this._transport.mergeOptions(options);
        return stackIntercept<UserReg, EmptyResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * Обновление данных пользователя
     *
     * @generated from protobuf rpc: updateUser(ru.sbertroika.tms.gate.v1.UpdateUserReq) returns (ru.sbertroika.common.v1.EmptyResponse);
     */
    updateUser(input: UpdateUserReq, options?: RpcOptions): UnaryCall<UpdateUserReq, EmptyResponse> {
        const method = this.methods[3], opt = this._transport.mergeOptions(options);
        return stackIntercept<UpdateUserReq, EmptyResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * Блокировка юзера
     *
     * @generated from protobuf rpc: blockUser(ru.sbertroika.tms.gate.v1.BlockUserRequest) returns (ru.sbertroika.common.v1.EmptyResponse);
     */
    blockUser(input: BlockUserRequest, options?: RpcOptions): UnaryCall<BlockUserRequest, EmptyResponse> {
        const method = this.methods[4], opt = this._transport.mergeOptions(options);
        return stackIntercept<BlockUserRequest, EmptyResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * Список групп
     *
     * @generated from protobuf rpc: groups(google.protobuf.Empty) returns (ru.sbertroika.tms.gate.v1.GroupsResponse);
     */
    groups(input: Empty, options?: RpcOptions): UnaryCall<Empty, GroupsResponse> {
        const method = this.methods[5], opt = this._transport.mergeOptions(options);
        return stackIntercept<Empty, GroupsResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * Список ролей
     *
     * @generated from protobuf rpc: roles(google.protobuf.Empty) returns (ru.sbertroika.tms.gate.v1.RolesResponse);
     */
    roles(input: Empty, options?: RpcOptions): UnaryCall<Empty, RolesResponse> {
        const method = this.methods[6], opt = this._transport.mergeOptions(options);
        return stackIntercept<Empty, RolesResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * Создать терминал
     *
     * @generated from protobuf rpc: createTerminal(ru.sbertroika.tms.gate.v1.TerminalRequest) returns (ru.sbertroika.tms.gate.v1.TerminalResponse);
     */
    createTerminal(input: TerminalRequest, options?: RpcOptions): UnaryCall<TerminalRequest, TerminalResponse> {
        const method = this.methods[7], opt = this._transport.mergeOptions(options);
        return stackIntercept<TerminalRequest, TerminalResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * Cписок терминалов
     *
     * @generated from protobuf rpc: terminalList(ru.sbertroika.tms.gate.v1.TerminalListRequest) returns (ru.sbertroika.tms.gate.v1.TerminalListResponse);
     */
    terminalList(input: TerminalListRequest, options?: RpcOptions): UnaryCall<TerminalListRequest, TerminalListResponse> {
        const method = this.methods[8], opt = this._transport.mergeOptions(options);
        return stackIntercept<TerminalListRequest, TerminalListResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * Удалить терминал
     *
     * @generated from protobuf rpc: deleteTerminal(ru.sbertroika.tms.gate.v1.DeleteTerminalRequest) returns (ru.sbertroika.tms.gate.v1.DeleteTerminalResponse);
     */
    deleteTerminal(input: DeleteTerminalRequest, options?: RpcOptions): UnaryCall<DeleteTerminalRequest, DeleteTerminalResponse> {
        const method = this.methods[9], opt = this._transport.mergeOptions(options);
        return stackIntercept<DeleteTerminalRequest, DeleteTerminalResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * Заблокировать/разблокировать терминал
     *
     * @generated from protobuf rpc: blockTerminal(ru.sbertroika.tms.gate.v1.BlockTerminalRequest) returns (ru.sbertroika.tms.gate.v1.BlockTerminalResponse);
     */
    blockTerminal(input: BlockTerminalRequest, options?: RpcOptions): UnaryCall<BlockTerminalRequest, BlockTerminalResponse> {
        const method = this.methods[10], opt = this._transport.mergeOptions(options);
        return stackIntercept<BlockTerminalRequest, BlockTerminalResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * Обновить данные терминала
     *
     * @generated from protobuf rpc: updateTerminal(ru.sbertroika.tms.gate.v1.TerminalRequest) returns (ru.sbertroika.tms.gate.v1.TerminalResponse);
     */
    updateTerminal(input: TerminalRequest, options?: RpcOptions): UnaryCall<TerminalRequest, TerminalResponse> {
        const method = this.methods[11], opt = this._transport.mergeOptions(options);
        return stackIntercept<TerminalRequest, TerminalResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * Создать тип терминала
     *
     * @generated from protobuf rpc: createTerminalType(ru.sbertroika.tms.gate.v1.TerminalType) returns (ru.sbertroika.tms.gate.v1.TerminalTypeResponse);
     */
    createTerminalType(input: TerminalType, options?: RpcOptions): UnaryCall<TerminalType, TerminalTypeResponse> {
        const method = this.methods[12], opt = this._transport.mergeOptions(options);
        return stackIntercept<TerminalType, TerminalTypeResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * Обновить тип терминала
     *
     * @generated from protobuf rpc: updateTerminalType(ru.sbertroika.tms.gate.v1.TerminalType) returns (ru.sbertroika.tms.gate.v1.TerminalTypeResponse);
     */
    updateTerminalType(input: TerminalType, options?: RpcOptions): UnaryCall<TerminalType, TerminalTypeResponse> {
        const method = this.methods[13], opt = this._transport.mergeOptions(options);
        return stackIntercept<TerminalType, TerminalTypeResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * Список типов терминалов
     *
     * @generated from protobuf rpc: terminalTypeList(ru.sbertroika.tms.gate.v1.TerminalTypeListRequest) returns (ru.sbertroika.tms.gate.v1.TerminalTypeListResponse);
     */
    terminalTypeList(input: TerminalTypeListRequest, options?: RpcOptions): UnaryCall<TerminalTypeListRequest, TerminalTypeListResponse> {
        const method = this.methods[14], opt = this._transport.mergeOptions(options);
        return stackIntercept<TerminalTypeListRequest, TerminalTypeListResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * Журнал терминальных событий
     *
     * @generated from protobuf rpc: journalList(ru.sbertroika.tms.gate.v1.JournalListRequest) returns (ru.sbertroika.tms.gate.v1.JournalListResponse);
     */
    journalList(input: JournalListRequest, options?: RpcOptions): UnaryCall<JournalListRequest, JournalListResponse> {
        const method = this.methods[15], opt = this._transport.mergeOptions(options);
        return stackIntercept<JournalListRequest, JournalListResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: getEventTypeList(google.protobuf.Empty) returns (ru.sbertroika.tms.gate.v1.EventTypeListResponse);
     */
    getEventTypeList(input: Empty, options?: RpcOptions): UnaryCall<Empty, EventTypeListResponse> {
        const method = this.methods[16], opt = this._transport.mergeOptions(options);
        return stackIntercept<Empty, EventTypeListResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: getTerminalUserById(ru.sbertroika.common.v1.ByIdRequest) returns (ru.sbertroika.tms.gate.v1.TerminalUserResponse);
     */
    getTerminalUserById(input: ByIdRequest, options?: RpcOptions): UnaryCall<ByIdRequest, TerminalUserResponse> {
        const method = this.methods[17], opt = this._transport.mergeOptions(options);
        return stackIntercept<ByIdRequest, TerminalUserResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: getTerminalTypeById(ru.sbertroika.common.v1.ByIdRequest) returns (ru.sbertroika.tms.gate.v1.TerminalTypeResponse);
     */
    getTerminalTypeById(input: ByIdRequest, options?: RpcOptions): UnaryCall<ByIdRequest, TerminalTypeResponse> {
        const method = this.methods[18], opt = this._transport.mergeOptions(options);
        return stackIntercept<ByIdRequest, TerminalTypeResponse>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: getTerminalById(ru.sbertroika.common.v1.ByIdRequest) returns (ru.sbertroika.tms.gate.v1.TerminalResponse);
     */
    getTerminalById(input: ByIdRequest, options?: RpcOptions): UnaryCall<ByIdRequest, TerminalResponse> {
        const method = this.methods[19], opt = this._transport.mergeOptions(options);
        return stackIntercept<ByIdRequest, TerminalResponse>("unary", this._transport, method, opt, input);
    }
}
