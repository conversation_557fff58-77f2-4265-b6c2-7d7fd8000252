/* eslint-env node */
require("@rushstack/eslint-patch/modern-module-resolution");

module.exports = {
  root: true,
  extends: [
    "plugin:vue/vue3-recommended",
    "eslint:recommended",
    "@vue/eslint-config-typescript",
    "@vue/eslint-config-prettier/skip-formatting",
    "plugin:storybook/recommended"
  ],
  ignorePatterns: ["**/__generated-api__/**/*"],
  parserOptions: {
    parserOptions: {
      parser: "@typescript-eslint/parser",
      sourceType: "module",
      project: ["./tsconfig.json"],
    },
  },
};
