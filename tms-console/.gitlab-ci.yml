stages:
  - aie
  - build
  - deploy

variables:
  DOCKER_REPOSITORY_ADDR: $DOCKER_REPOSITORY_ADDR
  GIT_SUBMODULE_STRATEGY: recursive
  KUBEVALURL: "https://github.com/instrumenta/kubeval/releases/download/v0.16.1/kubeval-linux-amd64.tar.gz"

aie:
  stage: aie
  rules:
    - if: '$AIE_ENABLE == "true"'
      when: always
    - when: never
  tags:
    - docker
  script:
    - docker run --rm -v $(pwd):/app:rw -u $(id -u):$(id -g) -v /var/aisa:/aisa:rw --workdir /app aisa-linux:latest aisa --project-settings-file $CI_PROJECT_NAME.aiproj --scan-target ./ --reports "HTML,JSON" --reports-folder ".report"
  artifacts:
    expire_in: 14 day
    paths:
      - .report/
      
#Сборка контейнера приложения
build:
  stage: build
  when: on_success
  dependencies:
    - aie
  tags:
    - docker
  script:
    #Если ветка DEV то использует CI_COMMIT_REF_NAME в качестве тэга, для веток release/* и hotfix/* используем версии
    - if [ "$CI_COMMIT_REF_NAME" = "dev" ]; then
      TAG="$CI_COMMIT_SHORT_SHA"
      MODE=dev;
      else
      TAG="$(echo "$CI_COMMIT_BRANCH" | cut -d'/' -f2)"
      MODE=production;
      fi
 
    # Сборка и пуш образа в репозиторий Docker
    - docker build 
      -t $DOCKER_REPOSITORY_ADDR/tkp3/$CI_PROJECT_NAME:$TAG
      --build-arg MODE=$MODE
      --pull -f Dockerfile .
    - docker push $DOCKER_REPOSITORY_ADDR/tkp3/$CI_PROJECT_NAME:$TAG
    #- docker image rm $DOCKER_REPOSITORY_ADDR/tkp3/$CI_PROJECT_NAME:$CI_COMMIT_SHORT_SHA
  rules:
    #Запускаем сборку при коммите в dev, release/*  hotfix/* ветки
    - if: '$CI_COMMIT_REF_NAME == "dev" || $CI_COMMIT_REF_NAME =~ /^release\/.*$/ || $CI_COMMIT_REF_NAME =~ /^hotfix\/.*$/'

#Установка HELM манифестов
deploy_chart:
  stage: deploy
  image: 
    name: alpine/helm
    entrypoint: [""]
  tags:
    #Используем docker-exec тэг для раннера типа "docker execution" 
    - docker
  script:
    
    #Если ветка DEV то использует CI_COMMIT_REF_NAME в качестве тэга, для веток release/* и hotfix/* используем версии
    - if [ "$CI_COMMIT_REF_NAME" = "dev" ]; then
      TAG="$CI_COMMIT_SHORT_SHA";
      else
      TAG="$(echo "$CI_COMMIT_BRANCH" | cut -d'/' -f2)";
      fi
    # Деплой Helm-чарта с установкой образа и его параметрами
    - helm upgrade $CI_PROJECT_NAME ./charts/$CI_PROJECT_NAME 
      --install 
      --set image.repository=$DOCKER_REPOSITORY_ADDR/tkp3/$CI_PROJECT_NAME 
      --set image.tag=$TAG 
      --set image.pullPolicy=Always
      --wait
      --kubeconfig $KUBECONFIG
  rules:
    - if: '$CI_COMMIT_REF_NAME == "dev" || $CI_COMMIT_REF_NAME =~ /^release\/.*$/ || $CI_COMMIT_REF_NAME =~ /^hotfix\/.*$/'
