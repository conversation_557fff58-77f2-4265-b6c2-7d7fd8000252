server {
    listen 80 default_server;
    root /usr/share/nginx/html;

    location / {
        try_files $uri  /index.html =404;
    }

    location ~* .*remoteEntry.js$ {
        expires -1;
        add_header 'Cache-Control' 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0';
    }

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   html;
    }
}
