apiVersion: v1
kind: Service
metadata:
  name: {{ include "tms-console.fullname" . }}
  namespace: crm-ui
  labels:
    {{- include "tms-console.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.http.type }}
  ports:
    - port: {{ .Values.service.http.port }}
      targetPort: {{ .Values.service.http.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- include "tms-console.selectorLabels" . | nindent 4 }}

