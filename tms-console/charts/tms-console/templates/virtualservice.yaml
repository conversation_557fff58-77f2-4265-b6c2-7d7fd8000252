kind: VirtualService
apiVersion: networking.istio.io/v1beta1
metadata:
  name: tms-gate
  namespace: crm-ui
spec:
  hosts:
    - dev-tms-crm.sbertroika.tech
  gateways:
    - istio-system/lkp-gateway
  http:
    - match:
        - uri:
            prefix: /api/tms-gateway-api/
      route:
        - destination:
            host: tms-gate-private.tms-gate.svc.cluster.local
            port:
              number: 5005
      rewrite:
        uri: /
    - match:
        - uri:
            prefix: /
      route:
        - destination:
            host: tms-console.crm-ui.svc.cluster.local
            port:
              number: 80
