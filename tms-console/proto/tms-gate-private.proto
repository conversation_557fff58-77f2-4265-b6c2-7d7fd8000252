syntax = "proto3";

package ru.sbertroika.tms.gate.v1;

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "common.proto";
import "common-tms.proto";

option java_multiple_files = true;
option java_package = "ru.sbertroika.tms.gate.v1";

service TMSGatePrivateService {
  // Список пользователей с фильтрацией
  rpc terminalUserList(TerminalUsersListRequest) returns (TerminalUsersListResponse);
  // Смена пароля с пользователем
  rpc changePasswordUser(ChangePasswordRequest) returns (common.v1.EmptyResponse);
  // Регистрация пользователя
  rpc registrationUser(UserReg) returns (common.v1.EmptyResponse);
  // Обновление данных пользователя
  rpc updateUser(UpdateUserReq) returns (common.v1.EmptyResponse);
  // Блокировка юзера
  rpc blockUser(BlockUserRequest) returns (common.v1.EmptyResponse);
  // Список групп
  rpc groups(google.protobuf.Empty) returns (GroupsResponse);
  // Список ролей
  rpc roles(google.protobuf.Empty) returns (RolesResponse);

  // Создать терминал
  rpc createTerminal(TerminalRequest) returns (TerminalResponse);
  // Cписок терминалов
  rpc terminalList(TerminalListRequest) returns (TerminalListResponse);
  // Удалить терминал
  rpc deleteTerminal(DeleteTerminalRequest) returns (DeleteTerminalResponse);
  // Заблокировать/разблокировать терминал
  rpc blockTerminal(BlockTerminalRequest) returns (BlockTerminalResponse);
  // Обновить данные терминала
  rpc updateTerminal(TerminalRequest) returns (TerminalResponse);

  // Создать тип терминала
  rpc createTerminalType(TerminalType) returns (TerminalTypeResponse);
  // Обновить тип терминала
  rpc updateTerminalType(TerminalType) returns (TerminalTypeResponse);
  // Список типов терминалов
  rpc terminalTypeList(TerminalTypeListRequest) returns (TerminalTypeListResponse);

  // Журнал терминальных событий
  rpc journalList(JournalListRequest) returns (JournalListResponse);
  rpc getEventTypeList(google.protobuf.Empty) returns (EventTypeListResponse);

  rpc getTerminalUserById(common.v1.ByIdRequest) returns (TerminalUserResponse);
  rpc getTerminalTypeById(common.v1.ByIdRequest) returns (TerminalTypeResponse);
  rpc getTerminalById(common.v1.ByIdRequest) returns (TerminalResponse);
}

message TerminalUserFilters {
  repeated string userId = 1;
  optional string login = 2;
  optional string surname = 3;
  optional string personalNumber = 4;
}

message TerminalUsersListRequest {
  TerminalUserFilters filters = 1;
  optional common.v1.PaginationRequest pagination = 2;
}

message TerminalUsersListResponse {
  oneof response {
    common.v1.OperationError error = 1;
    TerminalUserList result = 2;
  }
}

message TerminalUserList {
  optional common.v1.PaginationResponse pagination = 1;
  repeated TerminalUser user = 2;
}

message TerminalUser {
  string userId = 1;                  // идентификатор пользователя
  string login = 2;                   // логин
  string surname = 3;                 // Фамилия
  string name = 4;                    // Имя
  string middleName = 5;              // Отчество
  string personalNumber = 6;          // Табельный номер
  string password = 7;                // Хэш пароля
  repeated string roles = 8;          // ролли
  repeated string groups = 9;         // группы
  bool enabled = 10;                  // активный
}

message UpdateUserReq {
  string userId = 1;                  // идентификатор пользователя
  string surname = 2;                 // Фамилия
  string name = 3;                    // Имя
  string middleName = 4;              // Отчество
  string personalNumber = 5;          // Табельный номер
}

message BlockUserRequest {
  string userId = 1;
  bool block = 2;
}

message RolesResponse {
  oneof response {
    common.v1.OperationError error = 1;
    RoleResult result = 2;
  }
}

message GroupsResponse {
  oneof response {
    common.v1.OperationError error = 1;
    GroupResult result = 2;
  }
}

message RoleResult {
  repeated Role roles = 1;
}

message Role{
  string name = 1;
  string description = 2;
}

message GroupResult {
  repeated string groups = 1;
}

message ChangePasswordRequest {
  string userId = 1;
  string newPassword = 2;
  string confirmPassword = 3;
}

message UserReg {
  string login = 1;
  string surname = 2;
  string name = 3;
  string middleName = 4;
  string password = 5;
  string group = 6;
  string personalNumber = 7;
  string role = 8;
  optional string userId = 9;
}

message TerminalRequest {
  string serialNumber = 1;                   // Заводской номер терминала
  string title = 2;                          // Наименование терминала
  string typeId = 3;                         // uuid типа терминала из справочника
  string organizationId = 4;        // Организация, которой принадлежит терминал
  optional string wifiMac = 8;               // MAC-адрес WiFi
  optional string bluetoothMac = 9;          // MAC-адрес bluetooth
  optional string ethernetMac = 10;          // MAC-адрес ethernet
  optional TerminalStatus status = 12;       // Опционально и только для update, значения ACTIVE,DISABLED,BLOCKED,IS_DELETED
  optional string tid = 13;                  // Внешний идентификатор терминала (TID)
  string projectId = 14;            // Проект к которому принадлежит терминал
}

message TerminalResponse {
  oneof response {
    common.v1.OperationError error = 1;
    Terminal result = 2;
  }
}

message Terminal {
  string id = 1;
  string serialNumber = 2;          // Заводской номер терминала
  string title = 3;                 // Наименование терминала
  TerminalStatus status = 4;        // Статус терминала
  string versionPO = 5;             // Версия ПО
  TerminalType type = 6;            // тип терминала из справочника
  string organizationId = 7;        // Организация, которой принадлежит терминал
  string imei = 10;                 // IMEI[]
  string wifiMac = 11;              // MAC-адрес WiFi
  string bluetoothMac = 12;         // MAC-адрес bluetooth
  string ethernetMac = 13;          // MAC-адрес ethernet
  bool isActivated = 14;            // Активация (блок / не блок)
  string tid = 15;                  // Внешний идентификатор терминала (TID)
}

enum TerminalStatus {
  ACTIVE = 0;
  DISABLED = 1;
  BLOCKED = 2;
  IS_DELETED = 3;
  ALL = 4;
}

message TerminalListRequest {
  optional string serialNumber = 1;          // Искать по серийному номеру
  optional string organizationId = 2;        // Искать по организации / группе
  optional TerminalStatus status = 3;        // Статус терминала
  optional common.v1.PaginationRequest pagination = 4;
}

message TerminalListResponse {
  oneof response {
    common.v1.OperationError error = 1;
    TerminalList result = 2;
  }
}

message TerminalList {
  optional common.v1.PaginationResponse pagination = 1;
  repeated Terminal terminal = 2;
}

message DeleteTerminalRequest {
  string serialNumber = 1;          // Серийный номер терминала
}

message DeleteTerminalResponse {
  oneof response {
    common.v1.OperationError error = 1;
    string result = 2;
  }
}

message BlockTerminalRequest {
  string serialNumber = 1;          // Серийный номер терминала
  bool isBlock = 2;                 // Флаг true - заблокировать, false - разблокировать
}

message BlockTerminalResponse {
  oneof response {
    common.v1.OperationError error = 1;
    string result = 2;
  }
}

message TerminalType {
  optional string id = 1; // только для update, для create не указывать
  string name = 2;
  string slug = 3;
  string comment = 4;
}

message TerminalTypeResponse {
  oneof response {
    common.v1.OperationError error = 1;
    TerminalType result = 2;
  }
}

message TerminalTypeListResponse {
  oneof response {
    common.v1.OperationError error = 1;
    TerminalTypeList result = 2;
  }
}

message TerminalTypeList {
  repeated TerminalType terminalType = 2;
}

/* Журнал терминальных событий */
message JournalListFilter {
  optional string terminalSerial = 1;                          // Заводской номер терминала
  repeated common.tms.EventType eventType = 2;                 // Тип события
  optional google.protobuf.Timestamp createdAtFrom = 3;        // Дата формирования события на терминале (в UTC+0) - с
  optional google.protobuf.Timestamp createdAtTo = 4;          // Дата формирования события на терминале (в UTC+0) - по
  optional string userId = 5;                                  // Идентификатор пользователя (если пользователь авторизован на терминале)
  optional uint32 shiftNum = 6;                                // Номер смены на терминале
  optional uint32 ern = 7;                                     // Единый регистрационный номер (уникальный в рамках смены)
}

message JournalListRequest {
  optional common.v1.PaginationRequest pagination = 1;
  optional JournalListFilter filter = 2;
}

message TerminalJournalEvent {
  common.tms.EventType eventType = 1;                           // Тип события
  google.protobuf.Timestamp createdAt = 2;                      // Дата формирования события на терминале (в UTC+0)
  string terminalSerial = 3;                                    // Заводской номер терминала
  optional uint32 ern = 4;                                      // Единый регистрационный номер (уникальный в рамках смены)
  optional string userId = 5;                                   // Идентификатор пользователя (если пользователь авторизован на терминале)
  optional uint32 shiftNum = 6;                                 // Номер смены на терминале
  optional uint32 stopListVersion = 7;                          // Версия стоп-листа
  optional google.protobuf.Timestamp stopListUpdate = 8;        // Дата обновления стоп-листа
  optional int32 errorCode = 9;                                 // Код ошибки
  optional string errorMessage = 10;                            // Сообщение об ошибке
  optional string value = 11;                                   // Значение в рамках события
}

message JournalListResult {
  optional common.v1.PaginationResponse pagination = 1;
  optional JournalListFilter filter = 2;
  repeated TerminalJournalEvent event = 3;
}

message JournalListResponse {
  oneof response {
    common.v1.OperationError error = 1;
    JournalListResult result = 2;
  }
}

message TerminalUserResponse {
  oneof response {
    common.v1.OperationError error = 1;
    TerminalUser result = 2;
  }
}

message EventTypeListResponse {
  oneof response {
    common.v1.OperationError error = 1;
    EventTypeDescriptionList result = 2;
  }
}

message EventTypeDescriptionList {
  repeated EventTypeDescription eventType = 1;
}

message EventTypeDescription {
  common.tms.EventType type = 1;
  string description = 2;
}

message TerminalTypeListRequest {
    optional TerminalTypeFilters filters = 1;
}

message TerminalTypeFilters {
  optional string name = 1;
}