# build stage
FROM node:20-alpine as build-stage

WORKDIR /app
COPY package*.json ./
RUN npm install

COPY . .
ARG MODE=production

RUN npm run build-crm-core
RUN npm run build -- --mode ${MODE}


#------------------------------------
# production stage
FROM nginx:stable-alpine as production-stage

COPY --from=build-stage /app/dist /usr/share/nginx/html
COPY nginx/default.conf /etc/nginx/conf.d/default.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]