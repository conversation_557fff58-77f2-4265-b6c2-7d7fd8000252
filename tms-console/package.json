{"name": "tms-console", "version": "0.0.1", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "tsc": "vue-tsc --noEmit --watch", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "api": "rimraf  --glob src/service/__generated-api__/* && npx protoc --ts_out src/service/__generated-api__ --proto_path proto proto/*.proto", "preview": "vite preview --port 5173", "build-crm-core": "cd crm-core && npm i", "proto": "node --env-file=../.env ./crm-core/config-utils/download-proto.js"}, "dependencies": {"@protobuf-ts/runtime": "^2.9.3", "crm-core": "file:crm-core", "vue": "^3.4.19"}, "devDependencies": {"@originjs/vite-plugin-federation": "^1.3.3", "@protobuf-ts/grpcweb-transport": "^2.9.3", "@protobuf-ts/plugin": "^2.9.3", "@rushstack/eslint-patch": "^1.6.1", "@vitejs/plugin-vue": "^4.6.2", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^12.0.0", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.21.1", "rimraf": "^5.0.0", "sass": "^1.62.1", "sass-loader": "^13.3.0", "typescript": "^5.3.3", "vite": "^5.1.3", "vue-tsc": "^1.8.27"}}