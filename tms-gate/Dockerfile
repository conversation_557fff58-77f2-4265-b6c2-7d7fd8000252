#FROM arm64v8/gradle:8.14-jdk17 as builder
FROM gradle:8.13-jdk17-alpine as builder

ARG GRADLE_USER_HOME=/tmp/.gradle
ENV GRADLE_USER_HOME=$GRADLE_USER_HOME
RUN cat /etc/os-release
RUN apk add gcompat
#RUN apt-get update
#RUN apt-get install libc6-compat


# Install libc6-compat which provides similar functionality to gcompat
#RUN apt-get update
#RUN apt-get gcompat
#RUN apt-get install -y --no-install-recommends \
#    libc6-compat \
#    && rm -rf /var/lib/apt/lists/*

WORKDIR /build
ADD . /build

COPY .ci-gradle/gradle.properties /tmp/.gradle/gradle.properties
COPY .ci-gradle/gradle.properties /home/<USER>/.gradle/gradle.properties
COPY .ci-gradle/gradle.properties /root/.gradle/gradle.properties

RUN env
RUN gradle --no-daemon :tms-gate:bootJar -i

#-------------------------
FROM swr.ru-moscow-1.hc.sbercloud.ru/tkp3/kotlin-ms:latest

COPY --from=builder /build/tms-gate/build/libs/tms-gate-*.jar ./tms-gate.jar

EXPOSE 5000 8080 6000

ENTRYPOINT ["java", "-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:6000", "-Djava.security.egd=file:/dev/./urandom", "-jar", "tms-gate.jar"]

