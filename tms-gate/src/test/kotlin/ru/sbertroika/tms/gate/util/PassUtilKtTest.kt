package ru.sbertroika.tms.gate.util

import org.junit.jupiter.api.Test

class PassUtilKtTest {

    @Test
    fun passVerify() {
        val passHash = passHashing("1111")
        val result = passVerify("1111", passHash)
        println(passHash)
        assert(result)
    }

    @Test
    fun passVerifyTest() {
        val passHash = "61ce917919bfa299e6874baed7f0f0c747cf8c7d"
        val pass = "987654321"
        val result = passVerify(pass, passHash)
        assert(result)
    }

    @Test
    fun passVerifySecondTest() {
        val passHash = "b83a349105e748b558dd2e5ff9ea5063be3b3338"
        val pass = "1111"
        val result = passVerify(pass, passHash)
        assert(result)
    }
}