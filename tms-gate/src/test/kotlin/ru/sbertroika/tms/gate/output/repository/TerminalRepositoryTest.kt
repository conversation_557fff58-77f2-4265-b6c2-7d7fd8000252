package ru.sbertroika.tms.gate.output.repository

import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.r2dbc.core.DatabaseClient
import org.springframework.test.context.ActiveProfiles
import org.junit.jupiter.api.Assertions.assertNotNull

@SpringBootTest
@ActiveProfiles("test")
class TerminalRepositoryTest {

    @Autowired
    @Qualifier("mainDatabaseClient")
    private lateinit var databaseClient: DatabaseClient

    @Autowired
    private lateinit var terminalCrudRepo: TerminalCrudRepo

    @Test
    fun testTerminalRepositoryInjection() {
        assertNotNull(databaseClient, "DatabaseClient должен быть инжектирован")
        assertNotNull(terminalCrudRepo, "TerminalCrudRepo должен быть инжектирован")
    }
} 