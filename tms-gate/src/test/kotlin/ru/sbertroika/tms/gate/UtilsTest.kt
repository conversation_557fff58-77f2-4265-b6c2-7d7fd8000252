package ru.sbertroika.tms.gate

import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test

class UtilsTest {

    @Test
    fun calcTotalPageTest() {
        assert(calcTotalPage(8, 10) == 1)
        assert(calcTotalPage(10, 10) == 1)
        assert(calcTotalPage(11, 10) == 2)
        assert(calcTotalPage(20, 10) == 2)
        assert(calcTotalPage(21, 10) == 3)
    }

    @Test
    fun testExtractTerminalSerialNum() {
        val header =
            "Hash=a4b58d7561252387b38dc6f22caca7a594c0fe0b5d2c27f9d47dc438b51bb642;Cert=\"-----BEGIN%20CERTIFICATE-----%0AMIIErzCCApegAwIBAgIUe21S8zL0qeDRNcydSAnHH6E8MpUwDQYJKoZIhvcNAQEL%0ABQAwczELMAkGA1UEBhMCUlUxDzANBgNVBAgMBk1vc2NvdzEPMA0GA1UEBwwGTW9z%0AY293MRcwFQYDVQQKDA5TYmVyVHJvaWthIExURDELMAkGA1UECwwCSVQxHDAaBgNV%0ABAMME3Rtcy5zYmVydHJvaWthLnRlY2gwHhcNMjMxMTA5MDM0MzQzWhcNMjMxMTE2%0AMTU0MzQzWjAbMRkwFwYDVQQDExAxMDAzMzUyMDcwMDQwMjMwMIIBIjANBgkqhkiG%0A9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4hk88MAoATXZQSkZyBKhV%2FnFEDuIiPL4wUYj%0AH1j04%2FV38H8EMpsxYMUaS%2B1DsXHPp1gW5dp%2FWdN99z38f9wRDz6aENqnGUhBqxB%2F%0AKpOrg%2FrEG8yetLP2rEEE39Z0v7jKOkhH3K4ezeWO4Zu1d1ZEL7PP64%2BjCVUil3tM%0AheaX2DNy9qs4Cu50EVjs3js%2F1Ew2SZCQH3gY5EObqBRPxeSHNvuBhTg0xr3FkKjh%0Azybx2TZchs%2BHwb6KIvDCnRX5W5wUn0aUvJlXumEYOmpR4N4OBSfB5fWnHLofOl%2FI%0ADWRi0WCGdYvgsZA4BEih%2B4kpSYSG6DbR1kQP1kxI0YKt3aafpQIDAQABo4GSMIGP%0AMA4GA1UdDwEB%2FwQEAwIBrjAdBgNVHSUEFjAUBggrBgEFBQcDAQYIKwYBBQUHAwIw%0AHQYDVR0OBBYEFE%2BhgSckU%2BJ9h%2BQsQCyL%2Bwo%2FucbpMB8GA1UdIwQYMBaAFHwQQiEi%0A3opIZMFvZaQfkf%2BczoMUMB4GA1UdEQQXMBWCE3Rtcy5zYmVydHJvaWthLnRlY2gw%0ADQYJKoZIhvcNAQELBQADggIBABblD6UR1kZ20ZqV6vQaDKzOEcR4RxlRpfNEBtjr%0A5qjJAARXxGj65M2UuYPnv%2BcIvC9QyFP8tqJhAQ5PDReEHC%2FkmGUqau%2Bw3cldd6uE%0ABwYIajilrB2yaXWY6cBo%2BrPYgZ0tx%2FXn6hAf16aAOND6L0Xlc4xDx9%2BxK8VJmjxi%0AAlCvYSHBO9sQN5YXb8JJkRfqrZO96KmNpSQ%2BKiE%2BMyydRtgdxseOEfjh6cQHe3Fl%0AwKEQ3emj4rkj8vVuG2M0PFb8hrQ6ukbGs8kPFB0lhJjmO7%2BwfOgz3L0XF%2FHHmgBg%0A7xI51hciePEIXAHsF%2FaIIYg8u9Pk99sM3KDAazyg%2BAJcaFVhAhRdeqI%2B4AZMpkxt%0AnYmNckCuQnFLjRLorQO%2F8MO86MrSnD0WyRqdGxLDv8qmJD5A2vc6Xsooj%2F8tMFYe%0A0D04gTwJx%2BTiENK8vU76AT%2FIbS%2B2VOMj%2F1qh27PC3pQokZcKuH6h4bhxoZXvJ33M%0Ab9Np5woW8QmQKWleqeEQGaO1YbE7qK92Uk31tm6EnhwyVdRrUuIDCtW4jgjqEcMs%0A%2FpltJqF%2F0FHiMwqXH%2BiWLJ6UK98TKfN2Z6%2FJ7EKBTKXL5ul4NHkvZbJOX4zn%2F8Sj%0AetSG%2FzygEZo9NJzkWQrkcRidHL7Lu6BPgSP2GtpFz5V%2FKUcxGogWtSDDEbDBxZFY%0Al28k%0A-----END%20CERTIFICATE-----%0A\";Chain=\"-----BEGIN%20CERTIFICATE-----%0AMIIErzCCApegAwIBAgIUe21S8zL0qeDRNcydSAnHH6E8MpUwDQYJKoZIhvcNAQEL%0ABQAwczELMAkGA1UEBhMCUlUxDzANBgNVBAgMBk1vc2NvdzEPMA0GA1UEBwwGTW9z%0AY293MRcwFQYDVQQKDA5TYmVyVHJvaWthIExURDELMAkGA1UECwwCSVQxHDAaBgNV%0ABAMME3Rtcy5zYmVydHJvaWthLnRlY2gwHhcNMjMxMTA5MDM0MzQzWhcNMjMxMTE2%0AMTU0MzQzWjAbMRkwFwYDVQQDExAxMDAzMzUyMDcwMDQwMjMwMIIBIjANBgkqhkiG%0A9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4hk88MAoATXZQSkZyBKhV%2FnFEDuIiPL4wUYj%0AH1j04%2FV38H8EMpsxYMUaS%2B1DsXHPp1gW5dp%2FWdN99z38f9wRDz6aENqnGUhBqxB%2F%0AKpOrg%2FrEG8yetLP2rEEE39Z0v7jKOkhH3K4ezeWO4Zu1d1ZEL7PP64%2BjCVUil3tM%0AheaX2DNy9qs4Cu50EVjs3js%2F1Ew2SZCQH3gY5EObqBRPxeSHNvuBhTg0xr3FkKjh%0Azybx2TZchs%2BHwb6KIvDCnRX5W5wUn0aUvJlXumEYOmpR4N4OBSfB5fWnHLofOl%2FI%0ADWRi0WCGdYvgsZA4BEih%2B4kpSYSG6DbR1kQP1kxI0YKt3aafpQIDAQABo4GSMIGP%0AMA4GA1UdDwEB%2FwQEAwIBrjAdBgNVHSUEFjAUBggrBgEFBQcDAQYIKwYBBQUHAwIw%0AHQYDVR0OBBYEFE%2BhgSckU%2BJ9h%2BQsQCyL%2Bwo%2FucbpMB8GA1UdIwQYMBaAFHwQQiEi%0A3opIZMFvZaQfkf%2BczoMUMB4GA1UdEQQXMBWCE3Rtcy5zYmVydHJvaWthLnRlY2gw%0ADQYJKoZIhvcNAQELBQADggIBABblD6UR1kZ20ZqV6vQaDKzOEcR4RxlRpfNEBtjr%0A5qjJAARXxGj65M2UuYPnv%2BcIvC9QyFP8tqJhAQ5PDReEHC%2FkmGUqau%2Bw3cldd6uE%0ABwYIajilrB2yaXWY6cBo%2BrPYgZ0tx%2FXn6hAf16aAOND6L0Xlc4xDx9%2BxK8VJmjxi%0AAlCvYSHBO9sQN5YXb8JJkRfqrZO96KmNpSQ%2BKiE%2BMyydRtgdxseOEfjh6cQHe3Fl%0AwKEQ3emj4rkj8vVuG2M0PFb8hrQ6ukbGs8kPFB0lhJjmO7%2BwfOgz3L0XF%2FHHmgBg%0A7xI51hciePEIXAHsF%2FaIIYg8u9Pk99sM3KDAazyg%2BAJcaFVhAhRdeqI%2B4AZMpkxt%0AnYmNckCuQnFLjRLorQO%2F8MO86MrSnD0WyRqdGxLDv8qmJD5A2vc6Xsooj%2F8tMFYe%0A0D04gTwJx%2BTiENK8vU76AT%2FIbS%2B2VOMj%2F1qh27PC3pQokZcKuH6h4bhxoZXvJ33M%0Ab9Np5woW8QmQKWleqeEQGaO1YbE7qK92Uk31tm6EnhwyVdRrUuIDCtW4jgjqEcMs%0A%2FpltJqF%2F0FHiMwqXH%2BiWLJ6UK98TKfN2Z6%2FJ7EKBTKXL5ul4NHkvZbJOX4zn%2F8Sj%0AetSG%2FzygEZo9NJzkWQrkcRidHL7Lu6BPgSP2GtpFz5V%2FKUcxGogWtSDDEbDBxZFY%0Al28k%0A-----END%20CERTIFICATE-----%0A\";Subject=\"CN=****************\";URI=;DNS=tms.sbertroika.tech,By=spiffe://cluster.local/ns/tms-gate/sa/tms-gate;Hash=904d8d3702b52bf86cf396f289806a96ddf2457547924cf9030f9a297334381c;Subject=\"\";URI=spiffe://cluster.local/ns/istio-system/sa/istio-ingressgateway-service-account"
        Assertions.assertEquals("****************", TerminalInterceptor.getCNFromHeader(header))
    }
}