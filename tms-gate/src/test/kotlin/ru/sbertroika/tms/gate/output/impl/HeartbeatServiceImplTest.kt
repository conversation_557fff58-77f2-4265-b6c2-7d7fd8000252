package ru.sbertroika.tms.gate.output.impl

import org.junit.jupiter.api.Test
import ru.sbertroika.tms.gate.model.MetaInformation
import java.sql.Timestamp
import java.text.SimpleDateFormat
import java.util.*

class HeartbeatServiceImplTest {

    @Test
    fun test() {
        println(
            MetaInformation(
                serverTime = Timestamp(System.currentTimeMillis()),
                timeZone = SimpleDateFormat("XXX", Locale.getDefault()).format(Calendar.getInstance(TimeZone.getTimeZone("UTC"), Locale.getDefault()).time),
                timeZoneName = TimeZone.getDefault().id
            )
        )
    }
}