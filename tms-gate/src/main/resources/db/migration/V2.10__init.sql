CREATE FUNCTION refresh_mv_terminal_settings_view() <PERSON><PERSON><PERSON><PERSON> trigger LANGUAGE plpgsql AS $$
BEGIN
    REFRESH MATERIALIZED VIEW terminal_settings_view;
    RETURN null;
END $$;

CREATE TRIGGER refresh_mv_terminal_settings_view
    AFTER insert OR update OR delete OR truncate
    ON settings__terminal_profile
EXECUTE PROCEDURE refresh_mv_terminal_settings_view();

CREATE TRIGGER refresh_mv_terminal_settings_view_second
    AFTER insert OR update OR delete OR truncate
    ON settings
EXECUTE PROCEDURE refresh_mv_terminal_settings_view();