ALTER TABLE project_organization DROP CONSTRAINT IF EXISTS project_organization_po_organization_id_po_organization_ve_fkey;
ALTER TABLE project_organization DROP CONSTRAINT IF EXISTS  project_organization_po_project_id_po_version_fkey;
ALTER TABLE project_function DROP CONSTRAINT IF EXISTS  project_function_pf_project_id_pf_project_version_fkey;

ALTER TABLE project_organization DROP COLUMN IF EXISTS  po_project_version;
ALTER TABLE project_organization DROP COLUMN IF EXISTS  po_organization_version;
ALTER TABLE project_function DROP COLUMN IF EXISTS  pf_project_version;
ALTER TABLE terminal_organization DROP COLUMN IF EXISTS to_organization_version;

CREATE INDEX IF NOT EXISTS idx_project_organization_project_id ON project_organization(po_project_id);
CREATE INDEX IF NOT EXISTS idx_project_organization_organization_id ON project_organization(po_organization_id);
CREATE INDEX IF NOT EXISTS idx_project_function_project_id ON project_function(pf_project_id);
CREATE INDEX IF NOT EXISTS idx_terminal_organization_organization_id ON terminal_organization(to_organization_id);


DROP TABLE IF EXISTS organization;
DROP TABLE IF EXISTS project;



