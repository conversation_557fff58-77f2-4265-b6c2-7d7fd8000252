DROP TABLE IF EXISTS terminal_connections;

CREATE TYPE terminal_connection_status AS ENUM ('ACTIVE', 'DISABLED', 'BLOCKED', 'IS_DELETED');

CREATE TABLE terminal_connections
(
    tc_id                      uuid             default gen_random_uuid() not null,
    tc_version                 int              default 1                 not null,
    PRIMARY KEY (tc_id, tc_version),
    tc_version_created_at      TIMESTAMP        default now()             not null,
    tc_version_created_by      uuid                                       not null,
    tc_terminal_id             uuid                                       not null,
    tc_ip_address              varchar(45)                                not null,
    tc_port                    int                                        not null,
    tc_type                    int,
    tc_connection_status       terminal_connection_status                 not null
);

CREATE INDEX idx_tc_terminal_id ON terminal_connections (tc_terminal_id);

