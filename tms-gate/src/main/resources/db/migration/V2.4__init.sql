CREATE TABLE terminal_user(
     tu_profile_id                  uuid                                       not null,
     tu_version                     int              default 1                 not null,
     PRIMARY KEY (tu_profile_id, tu_version),
     tu_version_created_at          TIMESTAMP        default now()             not null,
     tu_version_created_by          uuid,
     tu_organization_id             uuid                                       not null,
     tu_role                        varchar(32)                                not null,
     tu_surname                     varchar(64)                                not null,
     tu_name                        varchar(64)                                not null,
     tu_middle_name                 varchar(64)      default null,
     tu_personal_number             varchar(32)      default null,
     tu_pin_hash                    varchar(128)     default null,
     tu_enabled                     boolean          default true,
     tu_login                       varchar(64)      default null,
     tu_is_deleted                  boolean          default false               not null
);

CREATE INDEX idx_organization_id_in_terminal_user ON terminal_user (tu_organization_id);
CREATE INDEX idx_surname_in_terminal_user ON terminal_user (tu_surname);
CREATE INDEX idx_personal_number_in_terminal_user ON terminal_user (tu_personal_number);
CREATE INDEX idx_login_in_terminal_user ON terminal_user (tu_login);
