ALTER TABLE terminal_settings
    RENAME TO template_settings;
ALTER TABLE terminal_settings__settings_group
    RENAME TO template_settings__settings_group;

CREATE MATERIALIZED VIEW terminal_settings_view AS
SELECT
    stp.stp_id as id,
    stp.tp_id as profile_id,
    sett.s_value as value,
    sett.s_name as name,
    sett.s_alias as alias
FROM (
         (SELECT o.s_id, o.stp_id, o.tp_id FROM settings__terminal_profile o INNER JOIN (SELECT stp_id, MAX(stp_version) vers FROM settings__terminal_profile GROUP BY stp_id) o2 ON o.stp_id = o2.stp_id AND o.stp_version = o2.vers and o.stp_actual_till is null) stp
             LEFT JOIN
             (SELECT s.s_id, s.s_alias, s.s_name, s.s_value FROM settings s INNER JOIN (SELECT s_id, MAX(s_version) vers FROM settings GROUP BY s_id) s2 ON s.s_id = s2.s_id AND s.s_version = s2.vers AND s.s_is_deleted = false) sett
         ON stp.s_id = sett.s_id);

