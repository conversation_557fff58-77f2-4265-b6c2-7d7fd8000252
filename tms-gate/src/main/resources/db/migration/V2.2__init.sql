create table terminal_profile
(
    tp_id                 uuid             default gen_random_uuid() not null,
    tp_version            int              default 1                 not null,
    PRIMARY KEY (tp_id, tp_version),
    tp_version_created_at TIMESTAMP        default now()             not null,
    tp_version_created_by uuid,
    tp_name               varchar(32)                                not null,
    tp_active_from        TIMESTAMP        default now()             not null,
    tp_active_till        TIMESTAMP        default null,
    tp_status             int                                        not null
);
create table settings_group
(
    sg_id                 uuid             default gen_random_uuid() not null,
    sg_version            int              default 1                 not null,
    PRIMARY KEY (sg_id, sg_version),
    sg_version_created_at TIMESTAMP        default now()             not null,
    sg_version_created_by uuid,
    sg_is_deleted         boolean          default false             not null,
    sg_name               varchar(32)                                not null,
    sg_comment            varchar(512)
);

create table terminal_settings(
      ts_id                 uuid             default gen_random_uuid() not null,
      ts_version            int              default 1                 not null,
      PRIMARY KEY (ts_id, ts_version),
      ts_version_created_at TIMESTAMP        default now()             not null,
      ts_version_created_by uuid,
      ts_is_deleted         boolean          default false             not null,
      ts_name               varchar(32)                                not null,
      ts_slug               varchar(32)                                not null,
      ts_type               int                                        not null,
      ts_is_required        boolean          default false             not null,
      ts_comment            varchar(512),
      ts_valid_fn           varchar(32),
      ts_default_value      varchar(32)
);

create table settings(
     s_id                 uuid             default gen_random_uuid() not null,
     s_version            int              default 1                 not null,
     PRIMARY KEY (s_id, s_version),
     s_version_created_at TIMESTAMP        default now()             not null,
     s_version_created_by uuid,
     s_is_deleted         boolean          default false             not null,
     s_name               varchar(32),
     s_alias              varchar(32),
     s_value              varchar(32)
);


create table settings_group__terminal_profile(
     sgtp_id                 uuid             default gen_random_uuid() not null,
     sgtp_version            int              default 1                 not null,
     PRIMARY KEY (sgtp_id, sgtp_version),
     sgtp_version_created_at TIMESTAMP        default now()             not null,
     sgtp_version_created_by uuid,
     tp_id                   uuid                                       not null,
     sg_id                   uuid                                       not null,
     sgtp_actual_from        TIMESTAMP        default now()             not null,
     sgtp_actual_till        TIMESTAMP        default null
);
CREATE INDEX idx_sgtp_to_tp_id ON terminal_profile (tp_id);
CREATE INDEX idx_sgtp_to_sg_id ON settings_group (sg_id);

create table settings__terminal_profile(
   stp_id                 uuid             default gen_random_uuid() not null,
   stp_version            int              default 1                 not null,
   PRIMARY KEY (stp_id, stp_version),
   stp_version_created_at TIMESTAMP        default now()             not null,
   stp_version_created_by uuid,
   s_id                   uuid                                       not null,
   tp_id                  uuid                                       not null,
   stp_actual_from        TIMESTAMP        default now()             not null,
   stp_actual_till        TIMESTAMP        default null
);
CREATE INDEX idx_stp_to_tp_id ON terminal_profile (tp_id);
CREATE INDEX idx_stp_to_s_id ON settings (s_id);


create table terminal_settings__settings_group(
     tssg_id                 uuid             default gen_random_uuid() not null,
     tssg_version            int              default 1                 not null,
     PRIMARY KEY (tssg_id, tssg_version),
     tssg_version_created_at TIMESTAMP        default now()             not null,
     tssg_version_created_by uuid,
     ts_id                   uuid                                       not null,
     sg_id                   uuid                                       not null,
     tssg_actual_from        TIMESTAMP        default now()             not null,
     tssg_actual_till        TIMESTAMP        default null
);

CREATE INDEX idx_tssg_to_sg_id ON settings_group (sg_id);
CREATE INDEX idx_tssg_to_s_id ON settings (s_id);

alter table terminal add column tp_id uuid;
alter table terminal add column tp_version int;
alter table terminal add constraint fk_tp_id_version foreign key (tp_id, tp_version) references terminal_profile(tp_id, tp_version);