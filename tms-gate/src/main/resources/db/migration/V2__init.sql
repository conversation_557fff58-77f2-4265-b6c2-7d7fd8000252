create table terminal_type
(
    ts_id                 uuid             default gen_random_uuid() not null,
    ts_version            int              default 1                 not null,
    PRIMARY KEY (ts_id, ts_version),
    ts_version_created_at TIMESTAMP        default now()             not null,
    ts_version_created_by uuid,
    ts_slug               varchar(64)                                not null,
    ts_name               varchar(32)                                not null,
    ts_comment            varchar(512)     default ''                not null
);

create table organization
(
    o_id                 uuid             default gen_random_uuid() not null,
    o_version            int              default 1                 not null,
    PRIMARY KEY (o_id, o_version),
    o_version_created_at TIMESTAMP        default now()             not null,
    o_version_created_by uuid                                       not null,
    o_name               varchar(64)                                not null,
    o_short_name         varchar(32)                                not null,
    o_inn                varchar(16)                                not null,
    o_kpp                varchar(16)                                not null
);

create table terminal
(
    t_id                       uuid             default gen_random_uuid()    not null,
    t_version                  int              default 1                    not null,
    PRIMARY KEY (t_id, t_version),
    t_version_created_at       TIMESTAMP        default now()                not null,
    t_version_created_by       uuid                                          not null,
    t_serial_number            varchar(32)                                   not null,
    t_title                    varchar(64)      default ''                   not null,
    t_status                   int              default 0                    not null,
    t_version_po               varchar(32)      default ''                   not null,
    t_type_id                  uuid                                          not null,
    t_type_version             int              default 1                    not null,
    t_active_from              TIMESTAMP        default now()                not null,
    t_active_till              TIMESTAMP,
    t_wifi_mac                 varchar(64)                                   not null,
    t_bluetooth_mac            varchar(64)                                   not null,
    t_ethernet_mac             varchar(64)                                   not null,
    t_activated                BOOLEAN                                       not null,
    FOREIGN KEY (t_type_id, t_type_version) REFERENCES terminal_type (ts_id, ts_version)
);
CREATE INDEX idx_terminal_by_sn_and_last_version ON terminal (t_serial_number, t_version DESC);

create table terminal_organization
(
    to_id                   uuid             default gen_random_uuid() not null,
    to_version              int              default 1                 not null,
    PRIMARY KEY (to_id, to_version),
    to_version_created_at   TIMESTAMP        default now()             not null,
    to_version_created_by   uuid                                       not null,
    to_project_id           uuid,
    to_terminal_id          uuid,
    to_terminal_version     int              default 1,
    to_organization_id      uuid                                       not null,
    to_organization_version int              default 1                 not null,
    to_active_from          TIMESTAMP        default now()             not null,
    to_active_till          TIMESTAMP,
    FOREIGN KEY (to_organization_id, to_organization_version) REFERENCES organization (o_id, o_version),
    FOREIGN KEY (to_terminal_id, to_terminal_version) REFERENCES terminal (t_id, t_version)
);
CREATE INDEX idx_to_terminal_id ON terminal_organization (to_terminal_id, to_terminal_version);
CREATE INDEX idx_to_org_id ON terminal_organization (to_organization_id, to_organization_version);


create table terminal_connections
(
    tc_id                      uuid             default gen_random_uuid() not null,
    tc_version                 int              default 1                 not null,
    PRIMARY KEY (tc_id, tc_version),
    tc_version_created_at      TIMESTAMP        default now()             not null,
    tc_version_created_by      uuid                                       not null,
    tc_terminal_id             uuid                                       not null,
    tc_terminal_version        int              default 1                 not null,
    tc_parent_terminal_id      uuid,
    tc_parent_terminal_version int              default 1,
    tc_ip_address              varchar(45)                                not null,
    tc_port                    varchar(5)                                 not null,
    tc_connection_status       int              default 0                 not null,
    FOREIGN KEY (tc_terminal_id, tc_terminal_version) REFERENCES terminal (t_id, t_version),
    FOREIGN KEY (tc_parent_terminal_id, tc_parent_terminal_version) REFERENCES terminal (t_id, t_version)
);

create table project
(
    p_id                 uuid             default gen_random_uuid() not null,
    p_version            int              default 1                 not null,
    PRIMARY KEY (p_id, p_version),
    p_version_created_at TIMESTAMP        default now()             not null,
    p_version_created_by uuid                                       not null,
    p_title              varchar(64)      default ''                not null,
    p_active_from        TIMESTAMP        default now()             not null,
    p_active_till        TIMESTAMP,
    p_status             int              default 0                 not null
);

create table project_organization
(
    po_id                   uuid             default gen_random_uuid() not null,
    po_version              int              default 1                 not null,
    PRIMARY KEY (po_id, po_version),
    po_version_created_at   TIMESTAMP        default now()             not null,
    po_version_created_by   uuid                                       not null,
    po_project_id           uuid                                       not null,
    po_project_version      int              default 1                 not null,
    po_organization_id      uuid                                       not null,
    po_organization_version int              default 1                 not null,
    po_active_from          TIMESTAMP        default now()             not null,
    po_active_till          TIMESTAMP,
    FOREIGN KEY (po_project_id, po_version) REFERENCES project (p_id, p_version),
    FOREIGN KEY (po_organization_id, po_organization_version) REFERENCES organization (o_id, o_version)
);

create table project_function
(
    pf_id                 uuid             default gen_random_uuid() not null,
    pf_version            int              default 1                 not null,
    PRIMARY KEY (pf_id, pf_version),
    pf_version_created_at TIMESTAMP        default now()             not null,
    pf_version_created_by uuid                                       not null,
    pf_type               int                                        not null,
    pf_project_id         uuid                                       not null,
    pf_project_version    int              default 1                 not null,
    pf_active_from        TIMESTAMP        default now()             not null,
    pf_active_till        TIMESTAMP,
    pf_status             int              default 0                 not null,
    FOREIGN KEY (pf_project_id, pf_project_version) REFERENCES project (p_id, p_version)
);

insert into terminal_type (ts_id, ts_slug, ts_name, ts_comment) values ('00000000-a479-406f-a1d5-6bab827f257e', '', 'NewPos8210', '');
insert into terminal_type (ts_id, ts_slug, ts_name, ts_comment) values ('00000000-6dad-11ee-b962-0242ac120002', '', 'NewPos9220', '');
insert into terminal_type (ts_id, ts_slug, ts_name, ts_comment) values ('00000000-bda6-481d-a251-99d9764cebca', '', 'aQsi5f', '');
insert into terminal_type (ts_id, ts_slug, ts_name, ts_comment) values ('00000000-5f22-4d80-b4e9-1be0f08aaeae', '', 'VendotekLite', '');
insert into terminal_type (ts_id, ts_slug, ts_name, ts_comment) values ('00000000-c203-4f93-8cb7-7b4ac832c0be', '', 'VendotekFullAndroid', '');
insert into terminal_type (ts_id, ts_slug, ts_name, ts_comment) values ('00000000-3043-4ef3-8525-15f7cd6e5f0d', '', 'VendotekFullLinux', '');
insert into terminal_type (ts_id, ts_slug, ts_name, ts_comment) values ('00000000-89dd-4115-8e37-8b1a8d4848a9', '', 'ВМ18', '');
insert into terminal_type (ts_id, ts_slug, ts_name, ts_comment) values ('00000000-cc09-4983-b008-bd8b12345802', '', 'ВМ20', '');