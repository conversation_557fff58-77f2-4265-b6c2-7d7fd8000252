DROP FUNCTION IF EXISTS refresh_mv_terminal_type_view() CASCADE;
DROP FUNCTION IF EXISTS refresh_mv_terminal_user_view() CASCADE;
DROP FUNCTION IF EXISTS refresh_mv_terminal_view() CASCADE;
DROP FUNCTION IF EXISTS refresh_mv_settings_view() CASCADE;
DROP FUNCTION IF EXISTS refresh_mv_settings_group_view() CASCADE;
DROP FUNCTION IF EXISTS refresh_mv_terminal_profile_view() CASCADE;
DROP FUNCTION IF EXISTS refresh_mv_template_settings_view() CASCADE;

DROP MATERIALIZED VIEW IF EXISTS terminal_type_view;
DROP MATERIALIZED VIEW IF EXISTS terminal_view;
DROP MATERIALIZED VIEW IF EXISTS terminal_user_view;
DROP MATERIALIZED VIEW IF EXISTS settings_view;
DROP MATERIALIZED VIEW IF EXISTS settings_group_view;
DROP MATERIALIZED VIEW IF EXISTS terminal_profile_view;
DROP MATERIALIZED VIEW IF EXISTS template_settings_view;

CREATE INDEX IF NOT EXISTS idx_project_organization_ids ON project_organization (po_organization_id, po_project_id);

CREATE VIEW project_organization_view AS
SELECT o.* FROM project_organization o
            INNER JOIN (SELECT po_id, MAX(po_version) vers FROM project_organization GROUP BY po_id) o2
                ON o.po_id = o2.po_id AND o.po_version = o2.vers;

CREATE VIEW terminal_type_view AS
SELECT t.ts_name, t.ts_slug, t.ts_comment, t.ts_version, t.ts_id
FROM terminal_type as t
         INNER JOIN (
    SELECT ts_id, MAX(ts_version) vers
    FROM terminal_type
    GROUP BY ts_id
) t2 ON t.ts_id = t2.ts_id AND t.ts_version = t2.vers;

CREATE VIEW terminal_view AS
SELECT t.t_id, t.t_serial_number, t.t_version, t.t_title, t.t_type_id, t.t_active_from, t.t_active_till, t.t_bluetooth_mac, t.t_ethernet_mac, t.t_wifi_mac, t.t_version_po,
       t.t_status, t.t_version_created_at, t.t_version_created_by, t.t_activated, t.t_tid, o.to_organization_id as org_id
FROM terminal as t
         INNER JOIN (
    SELECT t_serial_number, MAX(t_version) vers
    FROM terminal
    GROUP BY t_serial_number
) t2 ON t.t_serial_number = t2.t_serial_number AND t.t_version = t2.vers
         INNER JOIN (
    SELECT to_terminal_id, to_organization_id, to_version
    FROM terminal_organization
) o ON o.to_terminal_id = t.t_id;

CREATE VIEW terminal_user_view AS
SELECT o.* FROM terminal_user o
                    INNER JOIN (
    SELECT tu_profile_id, MAX(tu_version) vers
    FROM terminal_user
    GROUP BY tu_profile_id
) o2 ON o.tu_profile_id = o2.tu_profile_id AND o.tu_version = o2.vers;

CREATE VIEW settings_view AS
SELECT o.s_id, o.s_version, o.s_name, o.s_alias, o.s_value, o.s_is_deleted
FROM settings o
         INNER JOIN (
    SELECT s_id, MAX(s_version) vers
    FROM settings
    GROUP BY s_id
) o2 ON o.s_id = o2.s_id AND o.s_version = o2.vers;

CREATE VIEW settings_group_view AS
SELECT o.sg_id, o.sg_version, o.sg_name, o.sg_comment, o.sg_is_deleted
FROM settings_group o
         INNER JOIN (
    SELECT sg_id, MAX(sg_version) vers
    FROM settings_group
    GROUP BY sg_id
) o2 ON o.sg_id = o2.sg_id AND o.sg_version = o2.vers;

CREATE VIEW terminal_profile_view AS
SELECT o.tp_id, o.tp_version, o.tp_name, o.tp_status, o.tp_active_from, o.tp_active_till
FROM terminal_profile o
         INNER JOIN (
    SELECT tp_id, MAX(tp_version) vers
    FROM terminal_profile
    GROUP BY tp_id
) o2 ON o.tp_id = o2.tp_id AND o.tp_version = o2.vers;

CREATE VIEW template_settings_view AS
SELECT o.ts_id, o.ts_version, o.ts_name, o.ts_comment, o.ts_default_value, o.ts_slug, o.ts_is_required, o.ts_type, o.ts_valid_fn, o.ts_is_deleted
FROM template_settings o
         INNER JOIN (
    SELECT ts_id, MAX(ts_version) vers
    FROM template_settings
    GROUP BY ts_id
) o2 ON o.ts_id = o2.ts_id AND o.ts_version = o2.vers;