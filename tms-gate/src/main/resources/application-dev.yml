spring:
  kafka:
#    bootstrap-servers: ${KAFKA_SERVERS:localhost:29092}
    bootstrap-servers: ${KAFKA_SERVERS:**********:9092;***********:9092;**********:9092}

stop_list_service: ${STOP_LIST_SERVICE:localhost:5008}
pro_gate_service: ${PRO_GATE_SERVICE:localhost:5009}

grpc:
  port: 5010
server:
  port: 8787

keycloak:
  host: ${TMS_KEYCLOAK_HOST:http://localhost:8080/}
  realm: ${TMS_KEYCLOAK_REALM:master}
  secret: ${TMS_KEYCLOAK_SECRET:qyJsEh0BHyGITh5NfgWE4l0i6uyBBba7}
  password: ${TMS_KEYCLOAK_PASSWORD:12345}
  username: ${TMS_KEYCLOAK_USERNAME:user}
  clientId: ${TMS_KEYCLOAK_CLIENT_ID:test-client}
  userRealm: ${TMS_KEYCLOAK_HOST_HOST:test-realm}

zookeeper:
  nodes: ${ZOOKEEPER_NODES:localhost:2181}
  namespace: ${ZOOKEEPER_NAMESPACE:tkp3}

s3:
  url: ${S3_URL:http://s3:9001}
  access_key_id: ${S3_ACCESS_KEY_ID:s3__user}
  secret_access_key: ${S3_SECRET_ACCESS_KEY:s3__pass}
  region: ${S3_REGION:ru-moscow}
  bucket: ${S3_BUCKET:tkp3-manifest}