spring:
  application:
    name: tms-gate
  main:
    allow-bean-definition-overriding: true

  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: ${KEYCLOAK_REALM_URL:https://dev-auth.sbertroika.tech/realms/test-asop}
    client_id: ${KEYCLOAK_CLIENT_ID:test-auth}

  kafka:
    bootstrap-servers: ${KAFKA_SERVERS:localhost:9092;localhost:9093}
    listener:
      ack-mode: manual
    consumer:
      enable-auto-commit: false
      properties:
        spring:
          json:
            trusted:
              packages: "*"
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    heartbeat_in_topic: ${TMS_HEARTBEAT_IN_TOPIC:TMS.HEARTBEAT.IN}
    batches_in_topic: ${TMS_BATCHES_IN_TOPIC:TMS.BATCHES.IN}
    terminal_register_in_topic: ${TMS_TERMINAL_REGISTER_IN_TOPIC:TMS.TERMINAL.REGISTER.IN}

  r2dbc:
    url: r2dbc:pool:${R2DB_URL:postgresql://postgres:postgres@localhost:5432/tms}
    username: ${DB_USER:postgres}
    password: ${DB_PASSWORD:postgres}

  flyway:
    enabled: ${DB_MIGRATION_ENABLE:true}
    validate-migration-naming: true
    url: jdbc:${DB_URL:postgresql://localhost:5432/tms}
    user: ${DB_USER:postgres}
    password: ${DB_PASSWORD:postgres}
    locations:
      - classpath:db/migration
    mixed: true
    baseline-on-migrate: true

  clickhouse:
    url: r2dbc:clickhouse:${CLICKHOUSE_URL:http://click-0.tkp2.prod:8123/dev}

vault:
  host: ${VAULT_HOST:http://************:8200}
  engine: ${VAULT_ENGINE:tms-dev}
  role: ${VAULT_ROLE:tms-gate}
  certDns: ${VAULT_CERT_DNS:tms.sbertroika.tech}
  token: ${VAULT_TOKEN:}

grpc:
  port: 5000

server:
  port: 8080

systemUserId: ${SYSTEM_USER_ID:166f3c80-8146-448c-9b61-bc5b19ae9fc8}
stop_list_service: ${STOP_LIST_SERVICE:stop-list-gate.stop-list-gate.svc.cluster.local:5000}

keycloak:
  host: ${TMS_KEYCLOAK_HOST:http://localhost:8080/}
  realm: ${TMS_KEYCLOAK_REALM:test-realm}
  secret: ${TMS_KEYCLOAK_SECRET:7o9WDLBhIiCvAKHbyQSXzDAXxIgRSK0y}
  password: ${TMS_KEYCLOAK_PASSWORD:12345}
  username: ${TMS_KEYCLOAK_USERNAME:tardyon}
  clientId: ${TMS_KEYCLOAK_CLIENT_ID:test-client}
  userRealm: ${TMS_KEYCLOAK_USER_REALM:test-realm}

s3:
  url: ${S3_URL:localhost:8080}
  access_key_id: ${S3_ACCESS_KEY_ID:fsdfsdf}
  secret_access_key: ${S3_SECRET_ACCESS_KEY:fsdfsdfsd}
  region: ${S3_REGION:ru-moscow}
  bucket: ${S3_BUCKET:tkp3-manifest}

zookeeper:
  nodes: ${ZOOKEEPER_NODES:localhost:2181,localhost:2182,localhost:2183}
  namespace: ${ZOOKEEPER_NAMESPACE:tkp3}

logging:
  structured:
    format:
      console: ecs
      file: ecs
  level:
    root: ${LOG_LEVEL:INFO}
    io.r2dbc.postgresql: ${LOG_LEVEL:INFO}
    org.zalando.logbook: TRACE

logbook:
  predicate:
    include:
      - path: /**
        methods:
          - GET
          - POST
  filter.enabled: false
  secure-filter.enabled: true
  format.style: json
  strategy: default
  minimum-status: 200
  obfuscate:
    headers:
      - Authorization

management:
  endpoint:
    health:
      show-details: always
      probes:
        enabled: true
      group:
        liveness:
          include: '*'
        readiness:
          include: '*'
