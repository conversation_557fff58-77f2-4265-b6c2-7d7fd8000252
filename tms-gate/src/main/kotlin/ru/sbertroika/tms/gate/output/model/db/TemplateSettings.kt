package ru.sbertroika.tms.gate.output.model.db

import org.springframework.data.relational.core.mapping.Column
import java.sql.Timestamp
import java.util.*

data class TemplateSettingsPK(
    val tsId: UUID? = null,
    val tsVersion:Int? = null,
)

enum class TemplateSettingsType{
    STRING,
    BOOLEAN,
    INT,
    UINT
}

data class TemplateSettings(
    
    @Column("ts_id")
    var tsId: UUID? = null,

    @Column("ts_version")
    var tsVersion: Int ,

    @Column("ts_version_created_at")
    var tsVersionCreatedAt: Timestamp? = null,

    @Column("ts_version_created_by")
    var tsVersionCreatedBy: UUID? = null,

    @Column("ts_name")
    var tsName: String? = null,

    @Column("ts_slug")
    var tsSlug: String? = null,

    @Column("ts_type")
    var tsType: Int,

    @Column("ts_is_required")
    var tsIsRequired: Boolean = false,

    @Column("ts_comment")
    var tsComment: String? = null,

    @Column("ts_valid_fn")
    var tsValidFN: String? = null,

    @Column("ts_default_value")
    var tsDefaultValue: String? = null,

    @Column("ts_is_deleted")
    var tsIsDeleted: Boolean = false
)
