package ru.sbertroika.tms.gate.input

import arrow.core.flatMap
import com.google.protobuf.Empty
import com.google.protobuf.Timestamp
import org.lognet.springboot.grpc.GRpcService
import org.slf4j.LoggerFactory
import ru.sbertroika.common.NotFound
import ru.sbertroika.common.ServiceError
import ru.sbertroika.common.toOperationError
import ru.sbertroika.common.v1.ErrorType
import ru.sbertroika.common.v1.OperationError
import ru.sbertroika.tms.gate.*
import ru.sbertroika.tms.gate.config.ManifestNotFound
import ru.sbertroika.tms.gate.config.ManifestStateError
import ru.sbertroika.tms.gate.config.TerminalNotProject
import ru.sbertroika.tms.gate.model.Heartbeat
import ru.sbertroika.tms.gate.model.Position
import ru.sbertroika.tms.gate.model.RegisterTerminalRequest
import ru.sbertroika.tms.gate.output.*
import ru.sbertroika.tms.gate.v1.*
import java.time.Instant
import java.time.ZoneId
import kotlin.coroutines.CoroutineContext
import kotlin.coroutines.coroutineContext

@GRpcService(interceptors = [TerminalInterceptor::class])
class TMSGateServiceGrpc(
    private val heartbeatService: HeartbeatService,
    private val keycloakService: KeycloakService,
    private val batchService: BatchService,
    private val terminalService: TerminalService,
    private val settingService: SettingService,
    private val stopListService: StopListService,
    private val dicService: DicService
) : TMSGateServiceGrpcKt.TMSGateServiceCoroutineImplBase() {

    private val logger = LoggerFactory.getLogger(this.javaClass.name)

    private val log = LoggerFactory.getLogger(this.javaClass.name)

    override suspend fun heartbeat(request: HeartbeatRequest): HeartbeatResponse {
        return terminalExec(coroutineContext).map { serial ->
            Heartbeat(
                terminalSerial = serial,
                terminalTime = Instant.ofEpochSecond(request.terminalTime.seconds, request.terminalTime.nanos.toLong())
                    .atZone(ZoneId.of(request.timeZoneName))
                    .toLocalDateTime()
                    .format(DT_FORMAT),
                timeZone = request.timeZone,
                timeZoneName = request.timeZoneName,
                imei = request.imeiList.toList(),
                charge = request.charge.toShort(),
                position = Position(
                    request.position.latitude,
                    request.position.longitude
                ),
                state = request.state.toLong()
            )
        }.flatMap {
            heartbeatService.heartbeat(it)
        }.fold(
            {
                log.error("Error heartbeat", it)
                HeartbeatResponse.newBuilder()
                    .setError(authError())
            },
            { res ->
                HeartbeatResponse.newBuilder()
                    .setResult(toMetaInformation(res))

            }
        ).build()
    }

    override suspend fun registration(request: RegistrationRequest): RegistrationResponse {
        return terminalService.register(
            RegisterTerminalRequest(
                serialNumber = request.serialNumber,
                csr = request.csr,
                imei = request.imeiList,
                wifiMac = request.wifiMac,
                bluetoothMac = request.bluetoothMac,
                ethernetMac = request.ethernetMac,
                versionPO = request.versionPO
            )
        ).fold(
            {
                log.error("Error registration", it)
                registrationResponse {
                    error = if (it is NotFound)
                        OperationError.newBuilder()
                            .setCode(100)
                            .setMessage(it.message)
                            .setType(ErrorType.NOT_FOUND).build()
                    else
                        toOperationError(Error(it))
                }
            },
            { res ->
                registrationResponse {
                    result = registrationResult {
                        certificate = res.certificate
                        index = res.index
                    }
                }
            }
        )
    }

    override suspend fun eventsBatch(request: EventsBatchRequest): EventsBatchResponse {
        return terminalExec(coroutineContext).flatMap { serial ->
            batchService.saveBatch(toBatch(request.batch, serial))
        }.fold(
            {
                log.error("Error eventsBatch", it)
                EventsBatchResponse.newBuilder().setError(authError())
            },
            {
                EventsBatchResponse.newBuilder()
            }
        ).build()
    }

    override suspend fun terminalUserList(request: TerminalUserListRequest): TerminalUserListResponse {
        logger.info("TERMINAL_USER_LIST", "public grpc call")
        val isTerminal = coroutineContext[TerminalElement] != null && !coroutineContext[TerminalElement]?.serial.isNullOrEmpty()
        val serial = if (coroutineContext[TerminalElement] != null) coroutineContext[TerminalElement]?.serial else null

        return keycloakService.terminalUserList(terminalUserListRequest{}, isTerminal, serial)
            .fold({
                log.error("Error terminalUserList", it)
                TerminalUserListResponse
                    .newBuilder()
                    .setError(
                        OperationError
                            .newBuilder()
                            .setType(ErrorType.BAD_REQUEST)
                            .setMessage(it.message)
                    )
            }, {
                TerminalUserListResponse.newBuilder().setResult(it.result)
            }).build()

    }

    override suspend fun getStopListUpdate(request: StopListUpdateRequest): StopListUpdateResponse {
        return terminalExec(coroutineContext).flatMap { serial ->
            stopListService.getStopListUpdate(
                ru.sbertroika.tms.gate.model.StopListUpdateRequest(
                    type = request.type,
                    version = request.version
                )
            )
        }.fold(
            {
                log.error("Error getStopListUpdate", it)
                stopListUpdateResponse {
                    error = authError()!!.build()
                }
            },
            { list ->
                stopListUpdateResponse {
                    result = stopListUpdateResult {
                        update += list
                    }
                }
            }
        )
    }

    override suspend fun getManifest(request: Empty): ManifestResponse {
        return terminalExec(coroutineContext).flatMap { serial ->
            dicService.getManifest(serial)
        }.fold(
            {
                log.error("Error getManifest", it)
                manifestResponse {
                    error = when(it) {
                        is ManifestNotFound, is NotFound -> toOperationError(NotFound(it.message))
                        is ManifestStateError -> toOperationError(ServiceError(it.message))
                        is TerminalNotProject -> toOperationError(ServiceError(it.message))
                        else -> authError()!!.build()
                    }
                }
            },
            { res ->
                manifestResponse {
                    manifest = res
                }
            }
        )
    }

    override suspend fun getStationList(request: Manifest): StationListResponse {
        return terminalExec(coroutineContext).flatMap { serial ->
            dicService.getStationList(request)
        }.fold(
            {
                log.error("Error getStationList", it)
                stationListResponse {
                    error = when(it) {
                        is ManifestNotFound, is NotFound -> toOperationError(NotFound(it.message))
                        is ManifestStateError -> toOperationError(ServiceError(it.message))
                        is TerminalNotProject -> toOperationError(ServiceError(it.message))
                        else -> authError()!!.build()
                    }
                }
            },
            { res ->
                stationListResponse {
                    result = res
                }
            }
        )
    }

    override suspend fun getProductList(request: Manifest): ProductListResponse {
        return terminalExec(coroutineContext).flatMap { serial ->
            dicService.getProductList(request)
        }.fold(
            {
                log.error("Error getProductList", it)
                productListResponse {
                    error = when(it) {
                        is ManifestNotFound, is NotFound -> toOperationError(NotFound(it.message))
                        is ManifestStateError -> toOperationError(ServiceError(it.message))
                        is TerminalNotProject -> toOperationError(ServiceError(it.message))
                        else -> authError()!!.build()
                    }
                }
            },
            { res ->
                productListResponse {
                    result = res
                }
            }
        )
    }

    override suspend fun getTariffList(request: Manifest): TariffListResponse {
        return terminalExec(coroutineContext).flatMap { serial ->
            dicService.getTariffList(request)
        }.fold(
            {
                log.error("Error getTariffList", it)
                tariffListResponse {
                    error = when(it) {
                        is ManifestNotFound, is NotFound -> toOperationError(NotFound(it.message))
                        is ManifestStateError -> toOperationError(ServiceError(it.message))
                        is TerminalNotProject -> toOperationError(ServiceError(it.message))
                        else -> authError()!!.build()
                    }
                }
            },
            { res ->
                tariffListResponse {
                    result = res
                }
            }
        )
    }

    override suspend fun getRouteList(request: Manifest): RouteListResponse {
        return terminalExec(coroutineContext).flatMap { serial ->
            dicService.getRouteList(request)
        }.fold(
            {
                log.error("Error getRouteList", it)
                routeListResponse {
                    error = when(it) {
                        is ManifestNotFound, is NotFound -> toOperationError(NotFound(it.message))
                        is ManifestStateError -> toOperationError(ServiceError(it.message))
                        is TerminalNotProject -> toOperationError(ServiceError(it.message))
                        else -> authError()!!.build()
                    }
                }
            },
            { res ->
                routeListResponse {
                    result = res
                }
            }
        )
    }

    override suspend fun getTransportList(request: Manifest): TransportListResponse {
        return terminalExec(coroutineContext).flatMap { serial ->
            dicService.getTransportList(request)
        }.fold(
            {
                log.error("Error getTransportList", it)
                transportListResponse {
                    error = when(it) {
                        is ManifestNotFound, is NotFound -> toOperationError(NotFound(it.message))
                        is ManifestStateError -> toOperationError(ServiceError(it.message))
                        is TerminalNotProject -> toOperationError(ServiceError(it.message))
                        else -> authError()!!.build()
                    }
                }
            },
            { res ->
                transportListResponse {
                    result = res
                }
            }
        )
    }

    override suspend fun getProductMenu(request: Manifest): ProductMenuResponse {
        return terminalExec(coroutineContext).flatMap { serial ->
            dicService.getProductMenu(request)
        }.fold(
            {
                log.error("Error getProductMenu", it)
                productMenuResponse {
                    error = when(it) {
                        is ManifestNotFound, is NotFound -> toOperationError(NotFound(it.message))
                        is ManifestStateError -> toOperationError(ServiceError(it.message))
                        is TerminalNotProject -> toOperationError(ServiceError(it.message))
                        else -> authError()!!.build()
                    }
                }
            },
            { res ->
                productMenuResponse {
                    result = res
                }
            }
        )
    }

    override suspend fun getSettingsList(request: Empty): TerminalSettingsListResponse {
        return terminalExec(coroutineContext).flatMap { serial ->
            settingService.getTerminalSettings(serial)
        }.fold(
            {
                log.error("Error getSettingsList", it)
                terminalSettingsListResponse {
                    error = authError()!!.build()
                }
            },
            { res ->
                terminalSettingsListResponse {
                    result = res
                }
            }
        )
    }

    override suspend fun getSubscriptionSettings(request: Manifest): SubscriptionSettingsResponse {
        return terminalExec(coroutineContext).flatMap { serial ->
            dicService.getSubscriptionSettings(request)
        }.fold(
            {
                log.error("Error getSubscriptionSettings", it)
                subscriptionSettingsResponse {
                    error = when(it) {
                        is ManifestNotFound, is NotFound -> toOperationError(NotFound(it.message))
                        is ManifestStateError -> toOperationError(ServiceError(it.message))
                        is TerminalNotProject -> toOperationError(ServiceError(it.message))
                        else -> authError()!!.build()
                    }
                }
            },
            { res ->
                subscriptionSettingsResponse {
                    result = res
                }
            }
        )
    }

    private fun authError(): OperationError.Builder? = OperationError.newBuilder().setType(ErrorType.AUTHENTICATION_ERROR)

    private fun toMetaInformation(data: ru.sbertroika.tms.gate.model.MetaInformation): MetaInformation {
        return MetaInformation.newBuilder()
            .setServerTime(toProtoTimestamp(data.serverTime))
            //TODO проставлять из проекта
            .setTimeZone("+03:00")
            .setTimeZoneName("Europe/Moscow")
            .build()
    }

    private fun toBatch(data: Batch, serial: String): ru.sbertroika.tms.model.Batch = ru.sbertroika.tms.model.Batch(
        terminalSerial = serial,
        events = data.eventsList.map(this::toTerminalEvent).toList(),
        tickets = data.ticketsList.map(this::toTicket).toList(),
        timeZone = data.timeZone,
        timeZoneName = data.timeZoneName
    )

    private fun toTerminalEvent(data: TerminalEvent): ru.sbertroika.tms.model.TerminalEvent = ru.sbertroika.tms.model.TerminalEvent(
        type = data.type.ordinal,
        createdAt = fromProtoTimestamp(data.createdAt),
        attributes = data.attributesMap
    )

    private fun toTicket(data: Ticket): ru.sbertroika.tms.model.Ticket = ru.sbertroika.tms.model.Ticket(
        ticketSeries = data.ticketSeries,
        ticketNumber = data.ticketNumber,
        createdAt = fromProtoTimestamp(data.createdAt),
        manifest = data.manifest,
        manifestVersion = data.manifestVersion.toLong(),
        shiftNumber = data.shiftNumber.toLong(),
        serviceId = data.serviceId,
        tariffId = data.tariffId,
        productId = data.productId,
        amount = data.amount.toLong(),
        stationFromId = data.stationFromId,
        stationToId = data.stationToId
    )

    private fun toProtoTimestamp(time: java.sql.Timestamp): Timestamp {
        val instant = time.toInstant()
        return Timestamp.newBuilder()
            .setSeconds(instant.epochSecond)
            .setNanos(instant.nano)
            .build()
    }

    private fun fromProtoTimestamp(time: Timestamp): java.sql.Timestamp {
        val timestamp = java.sql.Timestamp(time.seconds * 1000)
        timestamp.nanos = time.nanos
        return timestamp
    }

    private fun terminalExec(coroutineContext: CoroutineContext) = coroutineContext.param(TerminalElement).flatMap { el ->
        el.notNull { it.serial }
    }.flatMap { el2 ->
        el2.notNull { it }
    }
}