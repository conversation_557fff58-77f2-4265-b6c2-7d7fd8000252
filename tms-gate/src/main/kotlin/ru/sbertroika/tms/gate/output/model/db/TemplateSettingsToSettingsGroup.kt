package ru.sbertroika.tms.gate.output.model.db

import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.sql.Timestamp
import java.util.*

data class TemplateSettingsToSettingsGroupPK(
    val tssgId: UUID? = null,
    val tssgVersion:Int? = null
)

@Table(name = "template_settings__settings_group")
data class TemplateSettingsToSettingsGroup(

    @Column("tssg_id")
    var tssgId: UUID? = null,

    @Column("tssg_version")
    var tssgVersion: Int,

    @Column("tssg_version_created_at")
    var tssgVersionCreatedAt: Timestamp? = null,

    @Column("tssg_version_created_by")
    var tssgVersionCreatedBy: UUID? = null,

    @Column("ts_id")
    var tsId: UUID? = null,

    @Column("sg_id")
    var sgId: UUID? = null,

    @Column("tssg_actual_from")
    var tssgActualFrom: Timestamp? = null,

    @Column("tssg_actual_till")
    var tssgActualTill: Timestamp? = null
)