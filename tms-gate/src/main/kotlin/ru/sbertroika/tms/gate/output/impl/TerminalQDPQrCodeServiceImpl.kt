package ru.sbertroika.tms.gate.output.impl

import arrow.core.Either
import kotlinx.coroutines.flow.toList
import org.springframework.stereotype.Service
import ru.sbertroika.tms.gate.model.QRLinkResult
import ru.sbertroika.tms.gate.output.TerminalQDPQrCodeService
import ru.sbertroika.tms.gate.output.repository.TerminalQDPQrCodeCrudRepository
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

@Service
class TerminalQDPQrCodeServiceImpl(
    private val repository: TerminalQDPQrCodeCrudRepository
) : TerminalQDPQrCodeService {

    private val dataFormat = "yyyy-MM-dd'T'HH:mm:ss'Z'"
    override suspend fun getQrBySerialNumber(terminalSerial: String): Either<Error, QRLinkResult> {
        try {
            val qrLinkTerminal = repository.findFirstBySerialNumber(terminalSerial).toList()
            if(qrLinkTerminal.isEmpty() || qrLinkTerminal[0]?.qrLink == null)
                return Either.Left(Error("Терминал не зарегистрирован в системе"))
            val date =  LocalDateTime.now().plusDays(3)
            val formatter = DateTimeFormatter.ofPattern(dataFormat)
            return Either.Right(QRLinkResult(qrLinkTerminal[0]!!.qrLink, date.format(formatter), qrLinkTerminal[0]!!.amount))
        } catch (e: Exception) {
            return Either.Left(Error(e))
        }
    }
}