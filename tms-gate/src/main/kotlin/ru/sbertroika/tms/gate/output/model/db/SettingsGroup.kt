package ru.sbertroika.tms.gate.output.model.db

import org.springframework.data.relational.core.mapping.Column
import java.sql.Timestamp
import java.util.*


data class SettingsGroupPK(
    val sgId: UUID? = null,
    val sgVersion:Int? = null,
)
data class SettingsGroup(

    @Column("sg_id")
    var sgId: UUID? = null,

    @Column("sg_version")
    var sgVersion: Int? = null,

    @Column("sg_version_created_at")
    var sgVersionCreatedAt: Timestamp? = null,

    @Column("sg_version_created_by")
    var sgVersionCreatedBy: UUID? = null,

    @Column("sg_name")
    var sgName: String? = null,

    @Column("sg_comment")
    var sgComment: String? = null,

    @Column("sg_is_deleted")
    var sgIsDeleted: Boolean = false
)
