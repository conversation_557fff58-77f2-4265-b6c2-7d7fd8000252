package ru.sbertroika.tms.gate.output.impl

import arrow.core.Either
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.toList
import org.springframework.stereotype.Service
import ru.sbertroika.tms.gate.output.SettingService
import ru.sbertroika.tms.gate.output.repository.TerminalConnectionsRepository
import ru.sbertroika.tms.gate.output.repository.TerminalCrudRepo
import ru.sbertroika.tms.gate.output.repository.TerminalSettingsViewRepository
import ru.sbertroika.tms.gate.v1.TerminalConnection
import ru.sbertroika.tms.gate.v1.TerminalSetting
import ru.sbertroika.tms.gate.v1.TerminalSettingResult

@Service
class SettingServiceImpl(
    private val terminalRepo: TerminalCrudRepo,
    private val terminalSettingsViewRepository: TerminalSettingsViewRepository,
    private val terminalConnectionsRepository: TerminalConnectionsRepository
) : SettingService {


    override suspend fun getTerminalSettings(serial: String?): Either<Error, TerminalSettingResult> {
        return try {
            if (serial != null) {
                val terminal = terminalRepo.findBySerialNumber(serial)
                if (terminal != null) {
                    val connections = terminalConnectionsRepository.findByTerminalId(terminal.tId!!).map {
                        mapTerminalConnectionsToGrps(it)
                    }.toList()
                    if (terminal.tpId == null) {
                        Either.Right(
                            TerminalSettingResult
                                .newBuilder()
                                .addAllSettings(arrayListOf())
                                .addAllConnections(connections)
                                .build()
                        )
                    } else {
                        val findAllByProfileId =
                            terminalSettingsViewRepository.findAllByProfileId(terminal.tpId!!).map {
                                mapTerminalSettingsToGrps(it)
                            }.toList().toMutableList()
                        findAllByProfileId.add(
                            TerminalSetting
                                .newBuilder()
                                .setAlias("P_KRS_BT_DEVICE_NAME")
                                .setValue(String.format(KRS_BT_DEVICE_NAME_TPL_NAME, terminal.index))
                                .build()
                        )
                        findAllByProfileId.add(
                            TerminalSetting
                                .newBuilder()
                                .setAlias("P_KRS_BT_TRUSTED_NAMES")
                                .setValue(P_KRS_BT_TRUSTED_NAMES_TPL)
                                .build()
                        )
                        Either.Right(
                            TerminalSettingResult
                                .newBuilder()
                                .addAllSettings(findAllByProfileId)
                                .addAllConnections(connections)
                                .build()
                        )
                    }
                } else {
                    Either.Left(Error("Termianl is null"))
                }
            } else {
                Either.Left(Error("Serial is null"))
            }

        } catch (e: Exception) {
            Either.Left(Error(e))
        }
    }

    private fun mapTerminalSettingsToGrps(entity: ru.sbertroika.tms.gate.output.model.db.TerminalSettingsView): TerminalSetting {
        return TerminalSetting
            .newBuilder()
            .setValue(entity.value)
            .setAlias(entity.alias)
            .build()
    }

    private fun mapTerminalConnectionsToGrps(entity: ru.sbertroika.tms.gate.output.model.db.TerminalConnections): TerminalConnection {
        return TerminalConnection
            .newBuilder()
            .setType(entity.tcType ?: 0)
            .setIpAddress(entity.tcIpAddress)
            .setPort(entity.tcPort ?: 0)
            .build()
    }

    companion object {
        private const val KRS_BT_DEVICE_NAME_TPL_NAME = "ST-%s"
        private const val P_KRS_BT_TRUSTED_NAMES_TPL = "ST-[0-9]{1,5}"
    }
}