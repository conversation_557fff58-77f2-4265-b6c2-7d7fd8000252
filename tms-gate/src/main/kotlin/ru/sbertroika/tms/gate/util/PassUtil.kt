package ru.sbertroika.tms.gate.util

import java.math.BigInteger
import java.security.MessageDigest
import java.security.NoSuchAlgorithmException

fun passHashing(password: String): String {
    //hash(pin) = SHA1(pin + SHA1(pin + MD5(pin)))
    return sha1(password + sha1(password + md5(password)))
}

fun passVerify(password: String, passHash: String): Boolean {
    return passHashing(password) == passHash
}

fun md5(input:String): String {
    val md = MessageDigest.getInstance("MD5")
    return BigInteger(1, md.digest(input.toByteArray())).toString(16).padStart(32, '0')
}

fun sha1(input: String): String {
    return try {
        val md = MessageDigest.getInstance("SHA-1")
        val messageDigest = md.digest(input.toByteArray())
        val no = BigInteger(1, messageDigest)
        var hashtext = no.toString(16)
        while (hashtext.length < 32) {
            hashtext = "0$hashtext"
        }
        hashtext
    } catch (e: NoSuchAlgorithmException) {
        throw RuntimeException(e)
    }
}