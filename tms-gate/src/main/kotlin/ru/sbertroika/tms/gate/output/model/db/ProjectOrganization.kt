package ru.sbertroika.tms.gate.output.model.db

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.sql.Timestamp
import java.util.*

@Table("project_organization")
data class ProjectOrganization(

    @Id
    @Column("po_id")
    var poId: UUID? = null,

    @Column("po_version")
    var poVersion: Int? = null,

    @Column("po_version_created_at")
    var poVersionCreatedAt: Timestamp? = null,

    @Column("po_version_created_by")
    var poVersionCreatedBy: UUID? = null,

    @Column("po_project_id")
    var poProjectId: UUID? = null,

    @Column("po_organization_id")
    var poOrganizationId: UUID? = null,

    @Column("po_active_from")
    var poActiveFrom: Timestamp? = null,

    @Column("po_active_till")
    var poActiveTill: Timestamp? = null
)
