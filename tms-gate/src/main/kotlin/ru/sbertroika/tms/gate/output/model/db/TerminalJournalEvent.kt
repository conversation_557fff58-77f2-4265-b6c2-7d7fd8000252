package ru.sbertroika.tms.gate.output.model.db

import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import ru.sbertroika.common.tms.EventType
import java.time.ZonedDateTime

@Table("tms_journal")
data class TerminalJournalEvent(
    @Column("project_id")
    var projectId: String? = null,

    @Column("event_type")
    var eventType: EventType? = null,

    @Column("created_at")
    var createdAt: ZonedDateTime? = null,

    @Column("terminal_serial")
    var terminalSerial: String? = null,

    @Column("ern")
    var ern: Int? = null,

    @Column("user_id")
    var userId: String? = null,

    @Column("shift_num")
    var shiftNum: Int? = null,

    @Column("stop_list_version")
    var stopListVersion: Int? = null,

    @Column("stop_list_update")
    var stopListUpdate: ZonedDateTime? = null,

    @Column("error_code")
    var errorCode: Int? = null,

    @Column("error_message")
    var errorMessage: String? = null,

    @Column("value")
    var value: String? = null
)
