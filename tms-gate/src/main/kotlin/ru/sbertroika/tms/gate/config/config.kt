package ru.sbertroika.tms.gate.config

import org.jboss.resteasy.client.jaxrs.internal.ResteasyClientBuilderImpl
import org.keycloak.OAuth2Constants
import org.keycloak.admin.client.Keycloak
import org.keycloak.admin.client.KeycloakBuilder
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
open class KeycloakConfig(
    private val properties: KeycloakProperties
) {

    @Bean
    open fun keycloak(): Keycloak {
        return KeycloakBuilder.builder()
            .serverUrl(properties.host)
            .realm(properties.realm)
            .grantType(OAuth2Constants.PASSWORD)
            .username(properties.username)
            .password(properties.password)
            .clientId(properties.clientId)
            .clientSecret(properties.secret)
            .resteasyClient(
                ResteasyClientBuilderImpl()
                    .connectionPoolSize(10)
                    .build())
            .build()
    }
}