package ru.sbertroika.tms.gate.model

import ru.sbertroika.tms.gate.output.model.db.TerminalStatus
import java.util.*

data class RegisterTerminalRequest(
    val serialNumber: String,
    val csr: String,
    val imei: List<String>,
    val wifiMac: String,
    val bluetoothMac: String,
    val ethernetMac: String,
    val versionPO: String
)

data class SignedCert(
    val id: String,
    val sn: String,
    val certSN: String,
    val validUntil: String
)

data class CreateUpdateTerminalRequest(
    val serialNumber: String,
    val tid: String?,
    val title: String,
    val typeId: String,
    val organizationId: String,
    val status: TerminalStatus,
    val wifiMac: String,
    val bluetoothMac: String,
    val ethernetMac: String,
    val userId: String, // transient
    val projectId: String
)

data class TerminalResponse(
    val id: String,
    val serialNumber: String,
    val title: String,
    val type: TerminalType,
    val status: TerminalStatus,
    val organizationId: String,
    val activeFrom: Long,
    val activeTill: Long?,
    val wifiMac: String,
    val bluetoothMac: String,
    val ethernetMac: String,
    val versionPO: String,
    val version: Int,
    val versionCreatedAt: Long?,
    val versionCreatedBy: UUID?,
    val isActivated: Boolean,
    val tid: String?
)

data class TerminalListFilter(
    val serialNumber: String,
    val organizationId: String,
    val status: TerminalStatus
)

data class CreateTypeRequest(
    val slug: String,
    val name: String,
    val comment: String,
    val userId: String
)
data class TerminalType(
    val id: UUID,
    val slug: String,
    val name: String,
    val comment: String,
    val version: Int? = null,
    val userId: String? = null
)

data class CreateOrganization(
    val name: String,
    val shortName: String,
    val inn: String,
    val kpp: String,
    val userId: String
)

data class Organization(
    val id: UUID,
    val name: String,
    val shortName: String,
    val inn: String,
    val kpp: String,
    val userId: String? = null
)

data class TerminalUser(
    var profileId: UUID,
    var organizationId: UUID,
    var roles: List<String>,
    var groups: List<String>,
    var name: String,
    var surname: String,
    var middleName: String? = null,
    var personalNumber: String? = null,
    var pinHash: String? = null,
    var enabled: Boolean = true,
    var login: String? = null
)