package ru.sbertroika.tms.gate.output.repository

import io.r2dbc.spi.Readable
import kotlinx.coroutines.flow.Flow
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.r2dbc.core.DatabaseClient
import org.springframework.r2dbc.core.awaitOneOrNull
import org.springframework.r2dbc.core.flow
import org.springframework.stereotype.Repository
import ru.sbertroika.tms.gate.output.model.db.TerminalProfile
import ru.sbertroika.tms.gate.output.model.db.TerminalProfilePK
import ru.sbertroika.tms.gate.output.model.db.TerminalProfileStatus
import ru.sbertroika.tms.gate.util.timestampNow
import java.sql.Timestamp
import java.time.LocalDateTime
import java.util.*

@Repository
open class TerminalProfileRepository(
    @Qualifier("mainDatabaseClient")
    override val dbClient: DatabaseClient,
    override val repository: TerminalProfileCrudRepository
): AbstractRepository<TerminalProfile, TerminalProfilePK>(dbClient, repository) {
    override fun getQuery(isCount: Boolean) = (if (isCount) "SELECT COUNT(*) " else "SELECT * ") + "FROM terminal_profile_view o"

    override fun toEntity(t: Readable) = TerminalProfile(
        tpId = t.get("tp_id") as UUID,
        tpVersion = t.get("tp_version") as Int,
        tpName = t.get("tp_name") as String,
        tpActiveFrom = if (t.get("tp_active_from") == null) null else Timestamp.valueOf(t.get("tp_active_from") as LocalDateTime),
        tpActiveTill = if (t.get("tp_active_till") == null) null else Timestamp.valueOf(t.get("tp_active_till") as LocalDateTime),
        tpStatus = t.get("tp_status") as Int
    )
    override suspend fun findById(id: String): TerminalProfile? {
        return dbClient.sql("${getQuery()} WHERE o.tp_id = '$id'")
            .map(::toEntity).awaitOneOrNull()
    }

    override fun findAll(page: Int, limit: Int): Flow<TerminalProfile> {
        return dbClient.sql(getPageRequest(page, limit))
            .map(::toEntity).flow()
    }

    override fun findAll(): Flow<TerminalProfile> {
        return dbClient.sql(getQuery())
            .map(::toEntity).flow()
    }

    override suspend fun deleted(id: String, userId: UUID) {
        val entity = findById(id)
        if(entity != null && entity.tpStatus != TerminalProfileStatus.DELETED.ordinal) {
            repository.save(entity.copy(
                tpVersion = entity.tpVersion!! + 1,
                tpVersionCreatedBy = userId,
                tpVersionCreatedAt = timestampNow(),
                tpStatus = TerminalProfileStatus.DELETED.ordinal
            ))
        }
    }
}