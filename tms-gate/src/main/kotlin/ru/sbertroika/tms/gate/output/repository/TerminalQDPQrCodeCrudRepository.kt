package ru.sbertroika.tms.gate.output.repository

import kotlinx.coroutines.flow.Flow
import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import ru.sbertroika.tms.gate.output.model.db.TerminalQDPQrCode
import java.util.*

interface TerminalQDPQrCodeCrudRepository: CoroutineCrudRepository<TerminalQDPQrCode, UUID> {
    fun findFirstBySerialNumber(serialNumber: String): Flow<TerminalQDPQrCode?>
}