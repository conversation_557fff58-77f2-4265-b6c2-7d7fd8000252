package ru.sbertroika.tms.gate.output.repository

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import org.springframework.data.repository.query.Param
import ru.sbertroika.tms.gate.output.model.db.TerminalConnections
import ru.sbertroika.tms.gate.output.model.db.TerminalConnectionsPK
import ru.sbertroika.tms.model.db.Terminal
import ru.sbertroika.tms.model.db.TerminalPK


interface TerminalCrudRepo : CoroutineCrudRepository<Terminal, TerminalPK> {
    @Query(
        "SELECT distinct on (t_serial_number) t_id, t_version, t_title, t_status, t_version_po, t_serial_number, t_tid,\n " +
                " t_version_po, t_type_id, t_active_from, t_active_till, t_wifi_mac, \n" +
                " t_bluetooth_mac, t_ethernet_mac, t_activated, t_version_created_at, t_version_created_by, t_index, tp_id, tp_version \n" +
                "FROM terminal WHERE t_serial_number = :sn \n " +
                "ORDER BY t_serial_number, t_version DESC"
    )
    suspend fun findBySerialNumber(@Param("sn") sn: String): Terminal?
}


interface TerminalConnectionsRepo : CoroutineCrudRepository<TerminalConnections, TerminalConnectionsPK>
