package ru.sbertroika.tms.gate.output.model.db

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.sql.Timestamp
import java.util.*

@Table("project")
data class Project(

    @Id
    @Column("p_id")
    var pId: UUID? = null,

    @Column("p_version")
    var pVersion: Int? = null,

    @Column("p_version_created_at")
    var pVersionCreatedAt: Timestamp? = null,

    @Column("p_version_created_by")
    var pVersionCreatedBy: UUID? = null,

    @Column("p_title")
    var pTitle: String? = null,

    @Column("p_active_from")
    var pActiveFrom: Timestamp? = null,

    @Column("p_active_till")
    var pActiveTill: Timestamp? = null,

    @Column("p_status")
    var pStatus: Int? = null,
)
