package ru.sbertroika.tms.gate.output.model.db

import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.io.Serializable
import java.sql.Timestamp
import java.util.*

data class TerminalTypePK(
    val tsId: UUID? = null,
    val tsVersion:Int? = null,
): Serializable

@Table("terminal_type")
data class TerminalType(

    @Column("ts_id")
    var tsId: UUID? = null,

    @Column("ts_version")
    var tsVersion: Int? = null,

    @Column("ts_version_created_at")
    var tsVersionCreatedAt: Timestamp? = null,

    @Column("ts_version_created_by")
    var tsVersionCreatedBy: UUID? = null,

    @Column("ts_name")
    var tsName: String? = null,

    @Column("ts_slug")
    var tsSlug: String? = null,

    @Column("ts_comment")
    var tsComment: String? = null,
)