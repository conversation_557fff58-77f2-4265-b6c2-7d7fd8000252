package ru.sbertroika.tms.gate.output.repository

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import org.springframework.data.repository.query.Param
import ru.sbertroika.tms.model.db.TerminalOrganization
import ru.sbertroika.tms.model.db.TerminalOrganizationPK
import java.util.*

interface TerminalToOrganizationRepository: CoroutineCrudRepository<TerminalOrganization, TerminalOrganizationPK> {

    @Query("SELECT o.* \n" +
            "FROM terminal_organization o\n" +
            "INNER JOIN (\n" +
            "    SELECT to_terminal_id, MAX(to_version) vers\n" +
            "    FROM terminal_organization\n" +
            "    GROUP BY to_terminal_id\n" +
            ") o2 ON o.to_terminal_id = o2.to_terminal_id AND o.to_version = o2.vers\n" +
            "WHERE o.to_terminal_id = :terminal_id")
    suspend fun findTerminalOrg(@Param("terminal_id") terminalId: UUID): TerminalOrganization?
}
