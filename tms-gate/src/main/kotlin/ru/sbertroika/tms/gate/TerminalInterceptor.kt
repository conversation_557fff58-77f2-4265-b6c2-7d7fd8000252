package ru.sbertroika.tms.gate

import io.grpc.Metadata
import io.grpc.ServerCall
import io.grpc.kotlin.CoroutineContextServerInterceptor
import java.util.regex.Pattern
import kotlin.coroutines.CoroutineContext

class TerminalInterceptor : CoroutineContextServerInterceptor() {
    override fun coroutineContext(call: ServerCall<*, *>, headers: Metadata): CoroutineContext {
        val serial = headers.get(Metadata.Key.of(TMS_TERMINAL_SERIAL, Metadata.ASCII_STRING_MARSHALLER))
        //println("coroutineContext, serial: $serial")

        if (serial.isNullOrEmpty()) {
            val cn = getCNFromHeader(headers.get(Metadata.Key.of(HEADER_CLIENT_CERT, Metadata.ASCII_STRING_MARSHALLER)))
            //println("coroutineContext, CN: $cn")
            if (cn.isNotEmpty()) {
                return TerminalElement(cn)
            }
        }

        return TerminalElement(serial)
    }

    companion object {
        fun getCNFromHeader(header: String?): String {
            if (header.isNullOrEmpty()) return ""
            val match = CH_RGX.matcher(header)
            if (match.matches()) {
                return match.group(1)
            }
            return ""
        }

        private val CH_RGX = Pattern.compile(".*CN=(\\w+).*")
    }
}

class TerminalElement(val serial: String?) : CoroutineContext.Element {
    companion object Key : CoroutineContext.Key<TerminalElement>

    override val key: CoroutineContext.Key<TerminalElement>
        get() = Key
}

const val TMS_TERMINAL_SERIAL = "tms.terminal.serial"
const val HEADER_CLIENT_CERT = "x-forwarded-client-cert"