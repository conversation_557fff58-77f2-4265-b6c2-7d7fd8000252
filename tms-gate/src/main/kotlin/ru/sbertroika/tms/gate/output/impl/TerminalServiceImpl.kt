package ru.sbertroika.tms.gate.output.impl

import arrow.core.Either
import arrow.core.flatMap
import arrow.core.left
import arrow.core.right
import org.apache.kafka.clients.producer.ProducerRecord
import org.springframework.beans.factory.annotation.Value
import org.springframework.kafka.core.ProducerFactory
import org.springframework.stereotype.Service
import ru.sbertroika.common.NotFound
import ru.sbertroika.libs.certs.SignCertBody
import ru.sbertroika.libs.certs.VaultClient
import ru.sbertroika.tms.gate.mapper
import ru.sbertroika.tms.gate.model.*
import ru.sbertroika.tms.gate.output.TerminalService
import ru.sbertroika.tms.gate.output.repository.*
import ru.sbertroika.tms.model.db.Terminal
import java.security.cert.CertificateFactory
import java.security.cert.X509Certificate
import java.sql.Timestamp
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.*

@Service
class TerminalServiceImpl(
    @Value("\${vault.host}")
    final val vaultURL: String,

    @Value("\${vault.engine}")
    final val engine: String,

    @Value("\${vault.role}")
    final val role: String,

    @Value("\${vault.token}")
    final val token: String,

    @Value("\${vault.certDns}")
    final val certDns: String,

    @Value("\${spring.kafka.terminal_register_in_topic}")
    val registerInTopic: String,

    @Value("\${systemUserId}")
    val systemUserId: String,

    kafkaProducerFactory: ProducerFactory<String, Any>,
    val terminalRepo: TerminalCrudRepo
) : TerminalService {

    private val client = VaultClient(
        vaultUrl = vaultURL,
        engine = engine,
        token = token
    )

    private val producer = kafkaProducerFactory.createProducer()

    private val certFactory = CertificateFactory.getInstance("X.509")

    private val mapper = mapper()

    private val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")

    override suspend fun register(terminal: RegisterTerminalRequest): Either<Throwable, RegisterTerminalResult> = Either.catch {
        return findTerminal(terminal.serialNumber)
            .flatMap { terminalEntity ->
                updateTerminal(
                    terminalEntity = terminalEntity,
                    dataToUpdate = terminal
                )
                    .flatMap {
                        client.signCert(
                            role = role,
                            body = SignCertBody(
                                csr = terminal.csr,
                                commonName = terminal.serialNumber,
                                ttl = 60 * 60 * 24 * 7, // sec TODO take from manifest
                                altNames = certDns
                            )
                        )
                    }.flatMap { signedCert ->
                        getCertSN(signedCert.data.certificate)
                            .flatMap { certSN ->
                                postCertInfoToKafka(
                                    id = terminalEntity.tId!!,
                                    sn = terminal.serialNumber,
                                    certSN = certSN,
                                    validUntil = signedCert.data.expiration
                                )
                            }
                            .map {
                                RegisterTerminalResult(
                                    certificate = Base64.getEncoder().encodeToString(signedCert.data.certificate.toByteArray()),
                                    index = terminalEntity.index ?: 1
                                )
                            }
                    }
            }
    }

    private suspend fun findTerminal(serialNumber: String) = Either.catch {
        terminalRepo.findBySerialNumber(serialNumber)
            ?: return NotFound("Терминал $serialNumber не зарегистрирован в TMS").left()
    }

    private suspend fun updateTerminal(terminalEntity: Terminal, dataToUpdate: RegisterTerminalRequest) = Either.catch {
        var isChanged = false
        if (dataToUpdate.bluetoothMac.isNotEmpty() && !dataToUpdate.bluetoothMac.equals(terminalEntity.tBluetoothMac)) {
            terminalEntity.tBluetoothMac = dataToUpdate.bluetoothMac
            isChanged = true
        }
        if (dataToUpdate.ethernetMac.isNotEmpty() && !dataToUpdate.ethernetMac.equals(terminalEntity.tEthernetMac)) {
            terminalEntity.tEthernetMac = dataToUpdate.ethernetMac
            isChanged = true
        }
        if (dataToUpdate.wifiMac.isNotEmpty() && !dataToUpdate.wifiMac.equals(terminalEntity.tWifiMac)) {
            terminalEntity.tWifiMac = dataToUpdate.wifiMac
            isChanged = true
        }
        if (dataToUpdate.versionPO.isNotEmpty() && !dataToUpdate.versionPO.equals(terminalEntity.tVersionPO)) {
            terminalEntity.tVersionPO = dataToUpdate.versionPO
            isChanged = true
        }
        if (isChanged)
            terminalRepo.save(
                terminalEntity.apply {
                    tVersion = terminalEntity.tVersion!! + 1
                    tVersionCreatedBy = UUID.fromString(systemUserId)
                    tVersionCreatedAt = timestampNow()
                }
            )
    }

    private fun getCertSN(certBody: String): Either<Throwable, String> = Either.catch {
        val x509 = certFactory.generateCertificate(certBody.byteInputStream()) as X509Certificate
        x509.serialNumber.toString()
    }

    private suspend fun postCertInfoToKafka(id: UUID, sn: String, certSN: String, validUntil: String): Either<Throwable, Unit> = Either.catch {
        val out = ProducerRecord<String, Any>(
            registerInTopic, UUID.randomUUID().toString(),
            mapper.writeValueAsString(
                SignedCert(
                    id = id.toString(),
                    sn = sn,
                    certSN = certSN,
                    validUntil = formatter.format(
                        LocalDateTime.ofInstant(Instant.ofEpochSecond(validUntil.toLong()), ZoneId.systemDefault())
                            .toLocalDate().atStartOfDay()
                    )
                )
            )
        )
        producer.send(out)
        Unit.right()
    }

    private fun timestampNow() = Timestamp(Instant.now().toEpochMilli())
}