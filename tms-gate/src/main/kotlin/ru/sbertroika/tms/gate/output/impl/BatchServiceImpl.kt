package ru.sbertroika.tms.gate.output.impl

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import org.apache.kafka.clients.producer.ProducerRecord
import org.springframework.beans.factory.annotation.Value
import org.springframework.kafka.core.ProducerFactory
import org.springframework.stereotype.Service
import ru.sbertroika.tms.gate.mapper
import ru.sbertroika.tms.gate.output.BatchService
import ru.sbertroika.tms.model.Batch
import java.util.*

@Service
class BatchServiceImpl(
    kafkaProducerFactory: ProducerFactory<String, Any>,

    @Value("\${spring.kafka.batches_in_topic}")
    val batchesInTopic: String,
) : BatchService {

    private val producer = kafkaProducerFactory.createProducer()

    private val mapper = mapper()

    override suspend fun saveBatch(batch: Batch): Either<Error, Unit> = try {
        val out = ProducerRecord<String, Any>(batchesInTopic, UUID.randomUUID().toString(), mapper.writeValueAsString(batch))
        producer.send(out)

        Unit.right()
    } catch (e: Exception) {
        Error(e).left()
    }
}