package ru.sbertroika.tms.gate.output.repository

import io.r2dbc.spi.Readable
import kotlinx.coroutines.flow.Flow
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.r2dbc.core.DatabaseClient
import org.springframework.r2dbc.core.awaitOneOrNull
import org.springframework.r2dbc.core.flow
import org.springframework.stereotype.Repository
import ru.sbertroika.tms.gate.output.model.db.TemplateSettings
import ru.sbertroika.tms.gate.output.model.db.TemplateSettingsPK
import ru.sbertroika.tms.gate.util.timestampNow
import java.util.*

@Repository
open class TemplateSettingsRepository(
    @Qualifier("mainDatabaseClient")
    override val dbClient: DatabaseClient,
    override val repository: TemplateSettingsCrudRepository
): AbstractRepository<TemplateSettings, TemplateSettingsPK>(dbClient, repository) {
    override fun getQuery(isCount: Boolean) = (if (isCount) "SELECT COUNT(*) " else "SELECT * ") + "FROM template_settings_view o"

    override fun toEntity(t: Readable) = TemplateSettings(
        tsId = t.get("ts_id") as UUID,
        tsVersion = t.get("ts_version") as Int,
        tsName = t.get("ts_name") as String,
        tsComment = t.get("ts_comment") as String?,
        tsDefaultValue = t.get("ts_default_value") as String?,
        tsSlug = t.get("ts_slug") as String,
        tsIsRequired = t.get("ts_is_required") as Boolean,
        tsType = t.get("ts_type") as Int,
        tsValidFN = t.get("ts_valid_fn") as String?
    )

    override suspend fun findById(id: String): TemplateSettings? {
        return dbClient.sql("${getQuery()} WHERE o.ts_id = '$id'")
            .map(::toEntity).awaitOneOrNull()
    }

    override fun findAll(page: Int, limit: Int): Flow<TemplateSettings> {
        return dbClient.sql(getPageRequest(page, limit))
            .map(::toEntity).flow()
    }

    override fun findAll(): Flow<TemplateSettings> {
        return dbClient.sql(getQuery())
            .map(::toEntity).flow()
    }

    override suspend fun deleted(id: String, userId: UUID) {
        val entity = findById(id)
        if(entity != null && !entity.tsIsDeleted) {
            repository.save(entity.copy(
                tsVersion = entity.tsVersion + 1,
                tsVersionCreatedBy = userId,
                tsVersionCreatedAt = timestampNow(),
                tsIsDeleted = true
            ))
        }
    }
}