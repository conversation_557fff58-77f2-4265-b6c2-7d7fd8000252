package ru.sbertroika.tms.gate.output.model.db

import org.springframework.data.relational.core.mapping.Column
import java.sql.Timestamp
import java.util.*

data class SettingsPK(
    val sId: UUID? = null,
    val sVersion:Int? = null,
)
data class Settings(

    @Column("s_id")
    var sId: UUID? = null,

    @Column("s_version")
    var sVersion: Int? = null,

    @Column("s_version_created_at")
    var sVersionCreatedAt: Timestamp? = null,

    @Column("s_version_created_by")
    var sVersionCreatedBy: UUID? = null,

    @Column("s_name")
    var sName: String? = null,

    @Column("s_alias")
    var sAlias: String? = null,

    @Column("s_value")
    var sValue: String? = null,

    @Column("s_is_deleted")
    var sIsDeleted: Boolean = false
)
