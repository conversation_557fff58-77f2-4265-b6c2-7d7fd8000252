package ru.sbertroika.tms.gate.output.model.db

import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.sql.Timestamp
import java.util.*

data class SettingsToTerminalProfilePK(
    val stpId: UUID? = null,
    val stpVersion:Int? = null
)

@Table(name = "settings__terminal_profile")
data class SettingsToTerminalProfile(

    @Column("stp_id")
    var stpId: UUID? = null,

    @Column("stp_version")
    var stpVersion: Int,

    @Column("stp_version_created_at")
    var stpVersionCreatedAt: Timestamp? = null,

    @Column("stp_version_created_by")
    var stpVersionCreatedBy: UUID? = null,

    @Column("s_id")
    var sId: UUID? = null,

    @Column("tp_id")
    var tpId: UUID? = null,

    @Column("stp_actual_from")
    var stpActualFrom: Timestamp? = null,

    @Column("stp_actual_till")
    var stpActualTill: Timestamp? = null
)
