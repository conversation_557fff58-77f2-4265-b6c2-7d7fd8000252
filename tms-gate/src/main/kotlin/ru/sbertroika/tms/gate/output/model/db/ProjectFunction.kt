package ru.sbertroika.tms.gate.output.model.db

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.sql.Timestamp
import java.util.*

@Table("project_function")
data class ProjectFunction(

    @Id
    @Column("pf_id")
    var pfId: UUID? = null,

    @Column("pf_version")
    var pfVersion: Int? = null,

    @Column("pf_version_created_at")
    var pfVersionCreatedAt: Timestamp? = null,

    @Column("pf_version_created_by")
    var pfVersionCreatedBy: UUID? = null,

    /**
     * @see ProjectFunctionType
     */
    @Column("pf_type")
    var pfType: Int? = null,

    @Column("pf_project_id")
    var pfProjectId: UUID? = null,

    @Column("pf_active_from")
    var pfActiveFrom: Timestamp? = null,

    @Column("pf_active_till")
    var pfActiveTill: Timestamp? = null,

    // not yet defined
    @Column("pf_status")
    var pfStatus: Int? = null,
)
