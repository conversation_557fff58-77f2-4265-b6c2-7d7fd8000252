package ru.sbertroika.tms.gate.output.model.db

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.util.*

@Table("terminal__sbp_qr_code")
class TerminalQDPQrCode(

    @Id
    @Column("tsbpqr_id")
    var id: UUID? = null,

    @Column("tsbpqr_serial_number")
    var serialNumber: String? = null,

    @Column("tsbpqr_qr_link")
    var qrLink: String? = null,

    @Column("tsbpqr_amount")
    var amount: Long? = null
)