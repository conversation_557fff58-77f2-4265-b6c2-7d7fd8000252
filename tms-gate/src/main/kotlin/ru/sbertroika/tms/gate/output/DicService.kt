package ru.sbertroika.tms.gate.output

import arrow.core.Either
import ru.sbertroika.tms.gate.v1.*

interface DicService {

    suspend fun getManifest(terminalSerial: String): Either<Throwable, Manifest>

    suspend fun getStationList(manifest: Manifest): Either<Throwable, StationListResult>

    suspend fun getTariffList(manifest: Manifest): Either<Throwable, TariffListResult>

    suspend fun getRouteList(manifest: Manifest): Either<Throwable, RouteListResult>

    suspend fun getProductList(manifest: Manifest): Either<Throwable, ProductListResult>

    suspend fun getTransportList(manifest: Manifest): Either<Throwable, TransportListResult>

    suspend fun getProductMenu(manifest: Manifest): Either<Throwable, ProductMenuResult>

    suspend fun getSubscriptionSettings(manifest: Manifest): Either<Throwable, SubscriptionSettingsResult>
}