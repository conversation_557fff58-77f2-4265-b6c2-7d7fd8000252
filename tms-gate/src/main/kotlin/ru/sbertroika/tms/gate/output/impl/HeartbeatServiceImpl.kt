package ru.sbertroika.tms.gate.output.impl

import arrow.core.Either
import org.apache.kafka.clients.producer.ProducerRecord
import org.springframework.beans.factory.annotation.Value
import org.springframework.kafka.core.ProducerFactory
import org.springframework.stereotype.Service
import ru.sbertroika.tms.gate.mapper
import ru.sbertroika.tms.gate.model.Heartbeat
import ru.sbertroika.tms.gate.model.MetaInformation
import ru.sbertroika.tms.gate.output.HeartbeatService
import java.sql.Timestamp
import java.text.SimpleDateFormat
import java.util.*

@Service
class HeartbeatServiceImpl(
    kafkaProducerFactory: ProducerFactory<String, Any>,
    @Value("\${spring.kafka.heartbeat_in_topic}")
    private val heartbeatTopic: String
) : HeartbeatService {

    private val producer = kafkaProducerFactory.createProducer()

    private val mapper = mapper()

    override suspend fun heartbeat(data: Heartbeat): Either<Error, MetaInformation> {
        producer.send(
            ProducerRecord<String, Any>(
                heartbeatTopic,
                UUID.randomUUID().toString(),
                mapper.writeValueAsString(data)
            )
        )

        return Either.Right(
            MetaInformation(
                serverTime = Timestamp(System.currentTimeMillis()),
                timeZone = timeZone, //TODO сделать определение зоны работы терминала
                timeZoneName = timeZoneName //TODO сделать определение зоны работы терминала
            )
        )
    }

    companion object {
        private val timeZone = SimpleDateFormat("XXX", Locale.getDefault()).format(Calendar.getInstance(TimeZone.getTimeZone("UTC"), Locale.getDefault()).time)
        private val timeZoneName = TimeZone.getDefault().id
    }
}