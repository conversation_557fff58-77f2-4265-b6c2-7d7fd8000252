package ru.sbertroika.tms.gate.output.model.db

import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.io.Serializable
import java.sql.Timestamp
import java.util.*

data class TerminalConnectionsPK(
    val tcId: UUID? = null,
    val tcVersion:Int? = null,
): Serializable

@Table("terminal_connections")
data class TerminalConnections(

    @Column("tc_id")
    var tcId: UUID? = null,

    @Column("tc_version")
    var tcVersion: Int? = null,

    @Column("tc_version_created_at")
    var tcVersionCreatedAt: Timestamp? = null,

    @Column("tc_version_created_by")
    var tcVersionCreatedBy: UUID? = null,

    @Column("t_id")
    var tcTerminalId: UUID? = null,

    @Column("tc_ip_address")
    var tcIpAddress: String? = null,

    @Column("tc_port")
    var tcPort: Int? = null,

    @Column("tc_type")
    var tcType: Int? = null,
    /**
     * @see TerminalConnectionsStatus
     */
    @Column("tc_status")
    var tcStatus: TerminalConnectionsStatus? = null,
)


enum class TerminalConnectionsStatus {
    ACTIVE, DISABLED, BLOCKED, IS_DELETED
}