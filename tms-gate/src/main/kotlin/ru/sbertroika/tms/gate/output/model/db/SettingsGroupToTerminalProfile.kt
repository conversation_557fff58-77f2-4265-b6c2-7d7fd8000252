package ru.sbertroika.tms.gate.output.model.db

import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.sql.Timestamp
import java.util.*

data class SettingsGroupToTerminalProfilePK(
    val sgtpId: UUID? = null,
    val sgtpVersion:Int? = null
)

@Table(name = "settings_group__terminal_profile")
data class SettingsGroupToTerminalProfile(

    @Column("sgtp_id")
    var sgtpId: UUID? = null,

    @Column("sgtp_version")
    var sgtpVersion: Int,

    @Column("sgtp_version_created_at")
    var sgtpVersionCreatedAt: Timestamp? = null,

    @Column("sgtp_version_created_by")
    var sgtpVersionCreatedBy: UUID? = null,

    @Column("sg_id")
    var sgId: UUID? = null,

    @Column("tp_id")
    var tpId: UUID? = null,

    @Column("sgtp_actual_from")
    var sgtpActualFrom: Timestamp? = null,

    @Column("sgtp_actual_till")
    var sgtpActualTill: Timestamp? = null
)
