package ru.sbertroika.tms.gate.output.repository

import io.r2dbc.spi.Readable
import kotlinx.coroutines.flow.Flow
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.r2dbc.core.DatabaseClient
import org.springframework.r2dbc.core.awaitOneOrNull
import org.springframework.r2dbc.core.flow
import org.springframework.stereotype.Repository
import ru.sbertroika.tms.gate.output.model.db.SettingsGroup
import ru.sbertroika.tms.gate.output.model.db.SettingsGroupPK
import ru.sbertroika.tms.gate.util.timestampNow
import java.util.*

@Repository
open class SettingsGroupRepository(
    @Qualifier("mainDatabaseClient")
    override val dbClient: DatabaseClient,
    override val repository: SettingsGroupCrudRepository
): AbstractRepository<SettingsGroup, SettingsGroupPK>(dbClient, repository) {
    override fun getQuery(isCount: Boolean) = (if (isCount) "SELECT COUNT(*) " else "SELECT * ") + "FROM settings_group_view o"

    override fun toEntity(t: Readable) = SettingsGroup(
        sgId = t.get("sg_id") as UUID,
        sgVersion = t.get("sg_version") as Int,
        sgName = t.get("sg_name") as String,
        sgComment = t.get("sg_comment") as String?
    )

    override suspend fun findById(id: String): SettingsGroup? {
        return dbClient.sql("${getQuery()} WHERE o.sg_id = '$id'")
            .map(::toEntity).awaitOneOrNull()
    }

    override fun findAll(page: Int, limit: Int): Flow<SettingsGroup> {
        return dbClient.sql(getPageRequest(page, limit))
            .map(::toEntity).flow()
    }

    override fun findAll(): Flow<SettingsGroup> {
        return dbClient.sql(getQuery())
            .map(::toEntity).flow()
    }

    override suspend fun deleted(id: String, userId: UUID) {
        val entity = findById(id)
        if(entity != null && !entity.sgIsDeleted) {
            repository.save(entity.copy(
                sgVersion = entity.sgVersion!! + 1,
                sgVersionCreatedBy = userId,
                sgVersionCreatedAt = timestampNow(),
                sgIsDeleted = true
            ))
        }
    }

}