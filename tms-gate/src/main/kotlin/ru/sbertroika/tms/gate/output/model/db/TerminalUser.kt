package ru.sbertroika.tms.gate.output.model.db

import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.sql.Timestamp
import java.util.*



data class TerminalUserPK(
    var profileId: String,
    var version: Int
)

@Table("terminal_user")
data class TerminalUser(
    @Column( "tu_profile_id")
    var profileId: UUID,

    @Column( "tu_version")
    var version: Int,

    @Column( "tu_version_created_at")
    var versionCreatedAt: Timestamp? = null,

    @Column( "tu_version_created_by")
    var versionCreatedBy: UUID? = null,

    @Column( "tu_organization_id")
    var organizationId: UUID,

    @Column( "tu_role")
    var role: String,

    @Column( "tu_name")
    var name: String,

    @Column( "tu_surname")
    var surname: String,

    @Column( "tu_middle_name")
    var middleName: String? = null,

    @Column( "tu_personal_number")
    var personalNumber: String? = null,

    @Column( "tu_pin_hash")
    var pinHash: String? = null,

    @Column( "tu_enabled")
    var enabled: Boolean = true,

    @Column( "tu_is_deleted")
    var isDeleted: Boolean = true,

    @Column( "tu_login")
    var login: String? = null
)