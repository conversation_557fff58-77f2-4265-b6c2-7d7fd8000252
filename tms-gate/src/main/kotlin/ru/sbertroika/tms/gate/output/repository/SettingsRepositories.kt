package ru.sbertroika.tms.gate.output.repository

import io.r2dbc.spi.Readable
import kotlinx.coroutines.flow.Flow
import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import org.springframework.r2dbc.core.DatabaseClient
import ru.sbertroika.tms.gate.output.model.db.*
import java.util.*

interface SettingsCrudRepository: CoroutineCrudRepository<Settings, SettingsPK> {
    override fun findAllById(ids: Flow<SettingsPK>): Flow<Settings>
}

interface TerminalSettingsViewRepository: CoroutineCrudRepository<TerminalSettingsView, UUID> {
    fun findAllByProfileId(profileId: UUID): Flow<TerminalSettingsView>
}

interface SettingsGroupCrudRepository: CoroutineCrudRepository<SettingsGroup, SettingsGroupPK>
interface TerminalProfileCrudRepository: CoroutineCrudRepository<TerminalProfile, TerminalProfilePK>
interface TemplateSettingsCrudRepository: CoroutineCrudRepository<TemplateSettings, TemplateSettingsPK> {
}

abstract class AbstractRepository<E, K>(
    open val dbClient: DatabaseClient,
    open val repository: CoroutineCrudRepository<E, K>
) {
    abstract fun getQuery(isCount: Boolean = false): String
    abstract fun toEntity(t: Readable): E
    protected fun getPageRequest(page: Int, limit: Int): String = "${getQuery()} OFFSET ${page * limit} LIMIT $limit"
    abstract suspend fun findById(id: String): E?
    abstract fun findAll(page: Int, limit: Int): Flow<E>
    abstract fun findAll(): Flow<E>

    abstract suspend fun deleted(id: String, userId: UUID)

    suspend fun save(entity: E) = repository.save(entity)
}

