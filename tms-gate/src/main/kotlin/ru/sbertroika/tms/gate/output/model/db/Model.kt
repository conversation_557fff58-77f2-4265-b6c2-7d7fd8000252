package ru.sbertroika.tms.gate.output.model.db

import ru.sbertroika.common.Pagination
import ru.sbertroika.common.tms.EventType
import java.time.ZonedDateTime

enum class TerminalStatus {
    ACTIVE,
    DISABLED,
    BLOCKED,
    IS_DELETED,
    ALL,
}

enum class ConnectionStatus {
    ACTIVE,
    DISABLED,
    B<PERSON><PERSON>KED,
    IS_DELETED
}

enum class ProjectFunctionType {
    Transport,
    GO,
    Cash
}

data class JournalFilter(
    val terminalSerial: String? = null,
    val eventType: List<EventType> = emptyList(),
    val createdAtFrom: ZonedDateTime? = null,
    val createdAtTo: ZonedDateTime? = null,
    val userId: String? = null,
    val shiftNum: Int? = null,
    val ern: Int? = null
)

data class JournalResult(
    val result: List<TerminalJournalEvent>,
    val pagination: Pagination
)