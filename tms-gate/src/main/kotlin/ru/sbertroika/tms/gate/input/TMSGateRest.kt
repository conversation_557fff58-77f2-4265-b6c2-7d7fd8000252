package ru.sbertroika.tms.gate.input

import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.toMono
import ru.sbertroika.tms.gate.model.QRLinkResult
import ru.sbertroika.tms.gate.output.TerminalQDPQrCodeService

@RestController
class TMSGateRest(
    val terminalQDPQrCodeService: TerminalQDPQrCodeService
) {
    @GetMapping("/v1/qr/sbp")
    suspend fun getQRSBP(
        @RequestParam("terminalSerial") terminalSerial: String
    ) : Mono<ResponseEntity<QRLinkResult?>> {
        return terminalQDPQrCodeService.getQrBySerialNumber(terminalSerial).fold({
            val qr: QRLinkResult? = null
            ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(qr)
                .toMono()
        }, {
            ResponseEntity.ok().body(it).toMono()
        })
    }

}