package ru.sbertroika.tms.gate.output.model.db

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import java.util.*

data class TerminalSettingsView(

    @Id
    @Column("id")
    var id: UUID? = null,

    @Column("profile_id")
    var profileId: UUID? = null,

    @Column("name")
    var name: String? = null,

    @Column("alias")
    var alias: String? = null,

    @Column("value")
    var value: String? = null
)