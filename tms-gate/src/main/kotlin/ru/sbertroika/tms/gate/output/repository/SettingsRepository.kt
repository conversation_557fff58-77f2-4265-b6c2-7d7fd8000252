package ru.sbertroika.tms.gate.output.repository

import io.r2dbc.spi.Readable
import kotlinx.coroutines.flow.Flow
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.r2dbc.core.DatabaseClient
import org.springframework.r2dbc.core.awaitOneOrNull
import org.springframework.r2dbc.core.flow
import org.springframework.stereotype.Repository
import ru.sbertroika.tms.gate.output.model.db.Settings
import ru.sbertroika.tms.gate.output.model.db.SettingsPK
import ru.sbertroika.tms.gate.util.timestampNow
import java.util.*

@Repository
open class SettingsRepository(
    @Qualifier("mainDatabaseClient")
    override val dbClient: DatabaseClient,
    override val repository: SettingsCrudRepository
): AbstractRepository<Settings, SettingsPK>(dbClient, repository) {
    override fun getQuery(isCount: Boolean) = (if (isCount) "SELECT COUNT(*) " else "SELECT * ") + "FROM settings_view o"

    override fun toEntity(t: Readable): Settings = Settings(
        sId = t.get("s_id") as UUID,
        sVersion = t.get("s_version") as Int,
        sAlias = t.get("s_alias") as String,
        sName = t.get("s_name") as String,
        sValue = t.get("s_value") as String
    )

    override suspend fun findById(id: String): Settings? {
        return dbClient.sql("${getQuery()} WHERE o.s_id = '$id'")
            .map(::toEntity).awaitOneOrNull()
    }

    override fun findAll(page: Int, limit: Int): Flow<Settings> {
        return dbClient.sql(getPageRequest(page, limit))
            .map(::toEntity).flow()
    }

    override fun findAll(): Flow<Settings> {
        return dbClient.sql(getQuery())
            .map(::toEntity).flow()
    }

    override suspend fun deleted(id: String, userId: UUID) {
        val entity = findById(id)
        if(entity != null && !entity.sIsDeleted) {
            repository.save(entity.copy(
                sVersion = entity.sVersion!! + 1,
                sVersionCreatedBy = userId,
                sVersionCreatedAt = timestampNow(),
                sIsDeleted = true
            ))
        }
    }
}