package ru.sbertroika.tms.gate.output.model.db

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.io.Serializable
import java.sql.Timestamp
import java.util.*

data class OrganizationPK(
    val oId: UUID? = null,
    val oVersion: Int? = null,
): Serializable

@Table("organization")
data class Organization(

    @Column("o_id")
    var oId: UUID? = null,

    @Column("o_version")
    var oVersion: Int? = null,

    @Column("o_version_created_at")
    var oVersionCreatedAt: Timestamp? = null,

    @Column("o_version_created_by")
    var oVersionCreatedBy: UUID? = null,

    @Column("o_name")
    var oName: String? = null,

    @Column("o_short_name")
    var oShortName: String? = null,

    @Column("o_inn")
    var oInn: String? = null,

    @Column("o_kpp")
    var oKpp: String? = null,
)
