package ru.sbertroika.tms.gate.model

import ru.sbertroika.common.stop.list.StopListType
import java.sql.Timestamp

data class Position(
    val latitude: Double,
    val longitude: Double
)

data class Heartbeat(
    val terminalSerial: String,
    val terminalTime: String,
    val timeZone: String,
    val timeZoneName: String,
    val imei: List<String>,
    val charge: Short,
    val position: Position,
    val state: Long
)

data class MetaInformation(
    val serverTime: Timestamp,
    val timeZone: String,
    val timeZoneName: String
)

data class BatchResult(
    val eventsCount: Int,
    val ticketCount: Int,
    val events: List<String>,
    val tickets: List<String>
)

data class StopListUpdateRequest(
    val type: StopListType,
    val version: Long
)

data class RegisterTerminalResult(
    val certificate: String,
    val index: Int
)

data class QRLinkResult(
    val qr: String?= null,
    val validTo: String? = null,
    val amount: Long? = null
)
