package ru.sbertroika.tms.gate.output.repository

import io.r2dbc.spi.Readable
import kotlinx.coroutines.flow.Flow
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import org.springframework.r2dbc.core.DatabaseClient
import org.springframework.r2dbc.core.awaitOneOrNull
import org.springframework.r2dbc.core.flow
import org.springframework.stereotype.Component
import ru.sbertroika.tms.gate.output.model.db.TerminalUser
import ru.sbertroika.tms.gate.output.model.db.TerminalUserPK
import ru.sbertroika.tms.gate.util.timestampNow
import java.util.*

interface TerminalUserCRUDRepository: CoroutineCrudRepository<TerminalUser, TerminalUserPK>

@Component
open class TerminalUserRepository(
    @Qualifier("mainDatabaseClient")
    override val dbClient: DatabaseClient,
    override val repository: TerminalUserCRUDRepository
): AbstractRepository<TerminalUser, TerminalUserPK>(dbClient, repository){

    override fun getQuery(isCount: Boolean) = (if (isCount) "SELECT COUNT(*) " else "SELECT * ") + "FROM terminal_user_view o"

    override fun toEntity(t: Readable): TerminalUser = TerminalUser(
        profileId = t.get( "tu_profile_id") as UUID,
        version = t.get( "tu_version") as Int,
        organizationId = t.get( "tu_organization_id") as UUID,
        role = t.get( "tu_role") as String,
        name = t.get( "tu_name") as String,
        surname = t.get( "tu_surname") as String,
        middleName = t.get( "tu_middle_name") as String?,
        personalNumber = t.get( "tu_personal_number") as String?,
        pinHash = t.get( "tu_pin_hash") as String?,
        enabled = t.get( "tu_enabled") as Boolean,
        isDeleted = t.get( "tu_is_deleted") as Boolean,
        login = t.get( "tu_login") as String?,

    )

    override suspend fun findById(id: String): TerminalUser? {
        return dbClient.sql("${getQuery()} WHERE o.tu_profile_id = '$id'")
            .map(::toEntity).awaitOneOrNull()
    }

    override fun findAll(page: Int, limit: Int): Flow<TerminalUser> {
        return dbClient.sql(getPageRequest(page, limit))
            .map(::toEntity).flow()
    }

    override fun findAll(): Flow<TerminalUser> {
        return dbClient.sql(getQuery())
            .map(::toEntity).flow()
    }

    fun findByOrganisationId(organizationId: UUID): Flow<TerminalUser> {
        return dbClient.sql("${getQuery()} WHERE o.tu_organization_id = '$organizationId'")
            .map(::toEntity).flow()
    }

    override suspend fun deleted(id: String, userId: UUID) {
        val entity = findById(id)
        if(entity != null && !entity.isDeleted) {
            repository.save(entity.copy(
                version = entity.version + 1,
                versionCreatedBy = userId,
                versionCreatedAt = timestampNow(),
                isDeleted = true
            ))
        }
    }
}