package ru.sbertroika.tms.gate.config

import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

@Component
//@ConfigurationProperties(prefix = "keycloak")
class KeycloakProperties {
    @Value("\${keycloak.host}")
    var host: String = ""
    @Value("\${keycloak.realm}")
    var realm: String = ""
    @Value("\${keycloak.userRealm}")
    var userRealm: String = ""
    @Value("\${keycloak.username}")
    var username: String = ""
    @Value("\${keycloak.secret}")
    var secret: String = ""
    @Value("\${keycloak.password}")
    var password: String = ""
    @Value("\${keycloak.clientId}")
    var clientId: String = ""
}