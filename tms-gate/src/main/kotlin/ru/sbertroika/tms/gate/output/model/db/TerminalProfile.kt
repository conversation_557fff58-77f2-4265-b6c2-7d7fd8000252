package ru.sbertroika.tms.gate.output.model.db

import org.springframework.data.relational.core.mapping.Column
import java.io.Serializable
import java.sql.Timestamp
import java.util.*

data class TerminalProfilePK(
    val tpId: UUID? = null,
    val tpVersion:Int? = null,
): Serializable

enum class TerminalProfileStatus{
    ACTIVE,
    DRAFT,
    DELETED
}

data class TerminalProfile (

    @Column("tp_id")
    var tpId: UUID? = null,

    @Column("tp_version")
    var tpVersion: Int? = null,

    @Column("tp_version_created_at")
    var tpVersionCreatedAt: Timestamp? = null,

    @Column("tp_version_created_by")
    var tpVersionCreatedBy: UUID? = null,

    @Column("tp_name")
    var tpName: String? = null,

    @Column("tp_active_from")
    var tpActiveFrom: Timestamp? = null,

    @Column("tp_active_till")
    var tpActiveTill: Timestamp? = null,

    @Column("tp_status")
    var tpStatus: Int
)