package ru.sbertroika.tms.gate.output.repository

import io.r2dbc.spi.Readable
import kotlinx.coroutines.flow.Flow
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.r2dbc.core.DatabaseClient
import org.springframework.r2dbc.core.awaitOneOrNull
import org.springframework.r2dbc.core.flow
import org.springframework.stereotype.Component
import ru.sbertroika.tms.gate.output.model.db.TerminalConnections
import ru.sbertroika.tms.gate.output.model.db.TerminalConnectionsPK
import ru.sbertroika.tms.gate.output.model.db.TerminalConnectionsStatus
import ru.sbertroika.tms.gate.util.timestampNow
import java.util.*

@Component
class TerminalConnectionsRepository(
    @Qualifier("mainDatabaseClient")
    override val dbClient: DatabaseClient,
    override val repository: TerminalConnectionsRepo
): AbstractRepository<TerminalConnections, TerminalConnectionsPK>(dbClient, repository) {
    override fun getQuery(isCount: Boolean) = (if (isCount) "SELECT COUNT(*) \n"
    else "SELECT * \n") +
            "FROM terminal_connections o\n" +
            "INNER JOIN (\n" +
            "    SELECT tc_id, MAX(tc_version) vers\n" +
            "    FROM terminal_connections\n" +
            "    GROUP BY tc_id\n" +
            ") o2 ON o.tc_id = o2.tc_id AND o.tc_version = o2.vers AND  o.tc_status = 'ACTIVE'"

    override fun toEntity(t: Readable) = TerminalConnections(
        tcId = t.get("tc_id") as UUID,
        tcVersion = t.get("tc_version") as Int,
        tcStatus = TerminalConnectionsStatus.valueOf(t.get("tc_status") as String),
        tcTerminalId = t.get("tc_terminal_id") as UUID?,
        tcIpAddress = t.get("tc_ip_address") as String?,
        tcPort = t.get("tc_port") as Int?,
        tcType = t.get("tc_type") as Int?
    )

    fun findByTerminalId(terminalId: UUID): Flow<TerminalConnections> {
        return dbClient.sql("${getQuery()} AND o.tc_terminal_id='$terminalId'")
            .map(::toEntity).flow()
    }

    override suspend fun findById(id: String): TerminalConnections? {
        return dbClient.sql("${getQuery()} AND o.tc_id = '$id'")
            .map(::toEntity).awaitOneOrNull()
    }

    override fun findAll(page: Int, limit: Int): Flow<TerminalConnections> {
        return dbClient.sql(getPageRequest(page, limit))
            .map(::toEntity).flow()
    }

    override fun findAll(): Flow<TerminalConnections> {
        return dbClient.sql(getQuery())
            .map(::toEntity).flow()
    }

    override suspend fun deleted(id: String, userId: UUID) {
        val entity = findById(id)
        if(entity != null && entity.tcStatus != TerminalConnectionsStatus.IS_DELETED) {
            repository.save(entity.copy(
                tcVersion = entity.tcVersion!! + 1,
                tcVersionCreatedBy = userId,
                tcVersionCreatedAt = timestampNow(),
                tcStatus = TerminalConnectionsStatus.IS_DELETED
            ))
        }
    }
}