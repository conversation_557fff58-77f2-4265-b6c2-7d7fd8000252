syntax = "proto3";

package ru.sbertroika.tms.gate.v1;

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "common.proto";

option java_multiple_files = true;
option java_package = "ru.sbertroika.tms.gate.v1";

service TMSGatePrivateTSService {
  rpc settingsList(SettingsListRequest) returns (SettingsListResponse);
  rpc settingsGroupList(SettingsGroupListRequest) returns (SettingsGroupListResponse);
  rpc terminalProfileList(TerminalProfileListRequest) returns (TerminalProfileListResponse);
  rpc templateSettingsList(TemplateSettingsListRequest) returns (TemplateSettingsListResponse);

  rpc createSettings(Settings) returns (common.v1.EmptyResponse);
  rpc createSettingsGroup(SettingsGroup) returns (common.v1.EmptyResponse);
  rpc createTerminalProfile(TerminalProfile) returns (common.v1.EmptyResponse);
  rpc createTemplateSettings(TemplateSettings) returns (common.v1.EmptyResponse);

  rpc updateSettings(Settings) returns (common.v1.EmptyResponse);
  rpc updateSettingsGroup(SettingsGroup) returns (common.v1.EmptyResponse);
  rpc updateTerminalProfile(TerminalProfile) returns (common.v1.EmptyResponse);
  rpc updateTemplateSettings(TemplateSettings) returns (common.v1.EmptyResponse);

  rpc deleteSettings(common.v1.ByIdRequest) returns (common.v1.EmptyResponse);
  rpc deleteSettingsGroup(common.v1.ByIdRequest) returns (common.v1.EmptyResponse);
  rpc deleteTerminalProfile(common.v1.ByIdRequest) returns (common.v1.EmptyResponse);
  rpc deleteTemplateSettings(common.v1.ByIdRequest) returns (common.v1.EmptyResponse);

  rpc getSettingsById(common.v1.ByIdRequest) returns (SettingsResponse);
  rpc getSettingsGroupById(common.v1.ByIdRequest) returns (SettingsGroupResponse);
  rpc getTerminalProfileById(common.v1.ByIdRequest) returns (TerminalProfileResponse);
  rpc getTemplateSettingsById(common.v1.ByIdRequest) returns (TemplateSettingsResponse);

  rpc getTemplateSettingsForSettingGroup(common.v1.ByIdRequest) returns (TemplateSettingsListResponse);
  rpc getSettingsForTerminalProfile(common.v1.ByIdRequest) returns (SettingsListResponse);
  rpc getSettingGroupForTerminalProfile(common.v1.ByIdRequest) returns (SettingsGroupListResponse);

  rpc assignTemplateSettingsToSettingGroup(AssignTemplateSettingsToSettingGroupRequest) returns (common.v1.EmptyResponse);
  rpc assignSettingsToTerminalProfile(AssignSettingsToTerminalProfileRequest) returns (common.v1.EmptyResponse);
  rpc assignSettingGroupToTerminalProfile(AssignSettingGroupToTerminalProfileRequest) returns (common.v1.EmptyResponse);
}

message SettingsListRequest{
  optional common.v1.PaginationRequest pagination = 1;
}

message SettingsGroupListRequest{
  optional common.v1.PaginationRequest pagination = 1;
}

message TerminalProfileListRequest{
  optional common.v1.PaginationRequest pagination = 1;
}

message TemplateSettingsListRequest{
  optional common.v1.PaginationRequest pagination = 1;
}

message AssignTemplateSettingsToSettingGroupRequest {
  repeated TemplateSettings templateSettings= 1;
  string groupId = 2;
}

message AssignSettingsToTerminalProfileRequest {
  repeated Settings settings = 1;
  string terminalProfileId = 2;
}

message AssignSettingGroupToTerminalProfileRequest {
  repeated SettingsGroup settingsGroup = 1;
  string terminalProfileId = 2;
}


message SettingsListResponse{
  oneof response {
    common.v1.OperationError error = 1;
    SettingsList result = 2;
  }
}

message SettingsGroupListResponse{
  oneof response {
    common.v1.OperationError error = 1;
    SettingsGroupList result = 2;
  }
}

message TerminalProfileListResponse{
  oneof response {
    common.v1.OperationError error = 1;
    TerminalProfileList result = 2;
  }
}

message TemplateSettingsListResponse{
  oneof response {
    common.v1.OperationError error = 1;
    TemplateSettingsList result = 2;
  }
}

message SettingsList {
  optional common.v1.PaginationResponse pagination = 1;
  repeated Settings settings = 2;
}

message SettingsGroupList {
  optional common.v1.PaginationResponse pagination = 1;
  repeated SettingsGroup settingsGroup = 2;
}

message TerminalProfileList {
  optional common.v1.PaginationResponse pagination = 1;
  repeated TerminalProfile terminalProfile = 2;
}

message TemplateSettingsList {
  optional common.v1.PaginationResponse pagination = 1;
  repeated TemplateSettings templateSettings = 2;
}


enum TerminalProfileStatus {
  TP_ACTIVE = 0;
  TP_DRAFT = 1;
  TP_DELETED = 2;
}

enum TemplateSettingsType {
  STRING = 0;
  BOOLEAN = 1;
  INT = 2;
  UINT = 3;
}

message Settings {
  optional string id = 1;
  string name = 2;
  string alias = 3;
  string value = 4;
  optional bool isDeleted = 5;
}

message SettingsGroup {
  optional string id = 1;
  string name = 2;
  optional string comment = 3;
  optional bool isDeleted = 4;
}

message TerminalProfile {
  optional string id = 1;
  string name = 2;
  optional google.protobuf.Timestamp activeFrom = 3;
  optional google.protobuf.Timestamp activeTill = 4;
  TerminalProfileStatus status = 5;
}

message TemplateSettings {
  optional string id = 1;
  string name = 2;
  string slug = 3;
  TemplateSettingsType type = 4;
  bool isRequired = 5;
  optional string comment = 6;
  optional string validFN = 7;
  optional string defaultValue = 8;
  optional bool isDeleted = 9;
}

message SettingsResponse {
  oneof response {
    common.v1.OperationError error = 1;
    Settings result = 2;
  }
}

message SettingsGroupResponse {
  oneof response {
    common.v1.OperationError error = 1;
    SettingsGroup result = 2;
  }
}

message TerminalProfileResponse {
  oneof response {
    common.v1.OperationError error = 1;
    TerminalProfile result = 2;
  }
}

message TemplateSettingsResponse {
  oneof response {
    common.v1.OperationError error = 1;
    TemplateSettings result = 2;
  }
}