docker-build-and-run: docker-build-gate docker-build-processing
	docker-compose up -d

docker-run-infra:
	docker-compose up -d tms_db keycloak_db keycloak zoo1 s3 kafka kafka-ui

docker-build: docker-build-gate docker-build-processing

start:
	docker-compose up -d

restart:
	docker-compose stop
	docker-compose rm -vf tms_gate
	docker-compose rm -vf tms_processing
	docker-compose up -d

restart-all:
	docker-compose stop && docker-compose rm -vf && docker-compose up -d

docker-rm:
	docker-compose stop && docker-compose rm -vf

docker-rm-all:
	docker-compose rm -vf

stop:
	docker-compose stop

rebuild-and-restart: stop docker-rm docker-build start

rebuild-and-restart-all: stop docker-rm-all docker-build start

docker-build-gate:
	@echo "[INFO] Сборка docker-образа tms-gate с пробросом gradle.properties"
	@mkdir -p .ci-gradle
	@if [ -f "$$HOME/.gradle/gradle.properties" ]; then \
		cp "$$HOME/.gradle/gradle.properties" .ci-gradle/; \
	elif [ -n "$$USERPROFILE" ] && [ -f "$$USERPROFILE\\.gradle\\gradle.properties" ]; then \
		cp "$$USERPROFILE\\.gradle\\gradle.properties" .ci-gradle/; \
	else \
		echo "[ERROR] gradle.properties не найден!"; exit 1; \
	fi
	docker build --build-arg GRADLE_USER_HOME=/tmp/.gradle \
		-t tms-gate:local \
		-f tms-gate/Dockerfile .
	rm -rf .ci-gradle

docker-build-processing:
	@echo "[INFO] Сборка docker-образа tms-processing с пробросом gradle.properties"
	@mkdir -p .ci-gradle
	@if [ -f "$$HOME/.gradle/gradle.properties" ]; then \
		cp "$$HOME/.gradle/gradle.properties" .ci-gradle/; \
	elif [ -n "$$USERPROFILE" ] && [ -f "$$USERPROFILE\\.gradle\\gradle.properties" ]; then \
		cp "$$USERPROFILE\\.gradle\\gradle.properties" .ci-gradle/; \
	else \
		echo "[ERROR] gradle.properties не найден!"; exit 1; \
	fi
	docker build --build-arg GRADLE_USER_HOME=/tmp/.gradle \
		-t tms-processing:local \
		-f tms-processing/Dockerfile .
	rm -rf .ci-gradle 