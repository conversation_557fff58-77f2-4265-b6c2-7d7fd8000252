syntax = "proto3";

package ru.sbertroika.common.tms;

option java_multiple_files = true;
option java_package = "ru.sbertroika.common.tms";

enum EventType {
  // Открытие смены
  T_SHIFT_OPEN = 0;
  // Закрытие смены
  T_SHIFT_CLOSE = 1;
  // Оплата банковской картой
  T_EMV_PAY_SUCCESS = 2;
  // Отказ банковского ядра
  T_EMV_FAIL = 3;
  // Оплата наличными
  T_CASH_PAY = 4;
  // Изменение состояния терминала
  T_SERVICE_STAT = 5;
  // Успешная печать
  T_PRINT_SUCCESS = 6;
  // Неуспешная печать
  T_PRINT_ERROR = 7;
  // Печать отчета о реализации
  T_SHIFT_REPORT_PRINT = 8;
  // Отказ в обслуживании (БК заблокирована по списку BIN)
  T_EMV_BIN_STOP_LIST = 9;
  // Отказ в обслуживании (БК заблокирована по списку PAN)
  T_EMV_PAN_STOP_LIST = 10;
  // Оплата транспортной картой Простор
  T_TK_PROSTOR_PAY = 11;
  // Полинг карты
  T_POLL_CARD = 12;
  // Ошибка определения типа карты
  T_CARD_PROCESS_FAIL = 13;
  // Оплата картой Тройка кошелёк
  T_TROIKA_WALLET_PAY_SUCCESS = 14;
  // Отказ оплаты картой Тройка кошелёк
  T_TROIKA_WALLET_PAY_FAIL = 15;
  // Отказ в обслуживании (БК заблокирована по списку PAR)
  T_EMV_PAR_STOP_LIST = 16;
  // Отказ в обслуживании (Тройка заблокирована по списку UID)
  T_TROIKA_STOP_LIST = 17;
  // Выбор маршрута
  T_ROUTE_SELECT = 18;
  // Выбор Т/С
  T_VEHICLE_SELECT = 19;
  // Оплата картой Тройка абонементом
  T_TROIKA_TICKET_PAY_SUCCESS = 20;
  // Отказ оплаты картой Тройка абонементом
  T_TROIKA_TICKET_PAY_FAIL = 21;

  // Оплата идентификатором (Кошелек)
  T_ABT_WALLET_PAY_SUCCESS = 22;
  // Отказ оплаты идентификатором (Кошелек)
  T_ABT_WALLET_PAY_FAIL = 23;
  // Оплата идентификатором (Билет/Абонемент)
  T_ABT_TICKET_PAY_SUCCESS = 24;
  // Отказ оплаты идентификатором (Билет/Абонемент)
  T_ABT_TICKET_PAY_FAIL = 25;

  // Оплата кошельком QR (виртуальной картой)
  T_QR_WALLET_PAY_SUCCESS = 26;
  // Отказ оплаты кошельком QR (виртуальной картой)
  T_QR_WALLET_PAY_FAIL = 27;
  // Оплата билетом/абонементом QR (виртуальной картой)
  T_QR_TICKET_PAY_SUCCESS = 28;
  // Отказ билетом/абонементом кошельком QR (виртуальной картой)
  T_QR_TICKET_PAY_FAIL = 29;

  // Не найден тариф оплаты
  T_TARIFF_NOT_FOUND = 30;

  // КРС: оплата была совершена
  T_KRS_CONTROL_SUCCESS = 31;
  // КРС: оплаты не было
  T_KRS_CONTROL_FAIL = 32;
  // КРС: ошибка конфигурации
  T_KRS_CONFIG_ERROR = 33;

  // Повторное прикладывание карты
  T_CARD_ATTACH_AGAIN = 34;
}

enum EventAttribute {
  // Единый регистрационный номер
  EA_ERN = 0;
  // Код операции
  EA_OPERATION_NUMBER = 1;
  // Идентификатор пользователя
  EA_USER_ID = 2;
  // Номер смены
  EA_SHIFT_NUM = 3;
  // Сообщение в формате ISO8583
  EA_ISO = 4;
  // Код ответа (от переферии, банковского ядра или библиотеки)
  EA_CODE = 5;
  // Проивольное сообщение в UTF_8
  EA_MESSAGE = 6;
  // Серия билета
  EA_TICKET_SERIES = 7;
  // Номер билета
  EA_TICKET_NUMBER = 8;
  // Идентификатор манифеста
  EA_MANIFEST_NUMBER = 9;
  // PAN Hash in sha256 (если отличный от SHA256 то должен быть проставлен EA_PAN_HASH_TYPE)
  EA_PAN_HASH = 10;
  // Список билетов в формате serial:number;serial:number;.... Разделитель билетов ';'
  EA_TICKETS = 11;
  // Версия стоп-листа
  EA_STOP_LIST_VERSION = 12;
  // Дата последнего обновления стоп-листа
  EA_STOP_LIST_UPDATE_DATE = 13;
  // Битмап после списания в HEX
  EA_RAW = 14;
  // UID карты HEX
  EA_CARD_UID = 15;
  // SAK карты HEX
  EA_CARD_SAK = 16;
  // ATQA карты HEX/null
  EA_CARD_ATQA = 17;
  // Флаг(boolean) карта типа B
  EA_CARD_B = 18;
  // Флаг(boolean) банковская карта
  EA_CARD_EMV = 19;
  // Формат сообщения
  EA_RAW_FORMAT = 20;
  // PAR
  EA_PAR = 21;
  // Версия манифеста
  EA_MANIFEST_VER = 22;
  // Идентификатор маршрута
  EA_ROUTE_ID = 23;
  // Идентификатор Т/С
  EA_VEHICLE_ID = 24;
  // Тип хэша функции (Возможные значения: SHA256/HMAC_SHA1/HMAC_SHA256/HMAC_SHA256_SHA256/STRIBOG512)
  EA_PAN_HASH_TYPE = 25;
  // Идентификатор шаблона (списания/продления)
  // Берется из белого списка абонементов из поля WriteOffsId
  EA_TEMPLATE_ID = 26;
  // Счётчики успешных оплат
  EA_COUNTERS = 27;
  // Тип оплаты
  EA_PAY_TYPE = 28;
  // Номер абонемента
  // Берется из белого списка абонементов из поля AbonementId
  EA_ABONEMENT_NUM = 29;
  // Данные в рамках события
  EA_VALUE = 100;
  // Check-in
  EA_CHECK_IN = 101;
  // Check-out
  EA_CHECK_OUT = 102;
  // Transport card number
  EA_CARD_NUM = 103;
}

enum SettingParam {
  // Период синхронизации
  P_SYNC_TIMEOUT = 0;
  // Ключ A к сектору 1 для карт простор
  P_PROSTOR_KEY_1A = 100;
  // Ключ B к сектору 1 для карт простор
  P_PROSTOR_KEY_1B = 101;
  // Тип ключа на чтение к сектору 1 для карт простор
  P_PROSTOR_KEY_TYPE_1R = 104;
  // Тип ключа на запись к сектору 1 для карт простор
  P_PROSTOR_KEY_TYPE_1W = 105;

  // Ключ A к сектору 8 для карт простор
  P_PROSTOR_KEY_8A = 102;
  // Ключ B к сектору 8 для карт простор
  P_PROSTOR_KEY_8B = 103;
  // Тип ключа на чтение к сектору 8 для карт простор
  P_PROSTOR_KEY_TYPE_8R = 106;
  // Тип ключа на запись к сектору 8 для карт простор
  P_PROSTOR_KEY_TYPE_8W = 107;

  // Флаг(boolean) использования физического SAM
  P_TROIKA_IS_USE_SAM = 200;
  // Флаг(boolean) включение пересадок по кошельку "Тройки"
  P_TROIKA_WALLET_TRANSFER_ENABLE = 201;
  // Скидка при пересадке задаётся в процентах,
  // если не задан или false, то в копейках
  P_TROIKA_WALLET_TRANSFER_IS_PERCENT = 202;
  // Время для пересадки в минутах
  P_TROIKA_WALLET_TRANSFER_DELAY = 203;
  // Размер скидки при пересадке
  P_TROIKA_WALLET_TRANSFER_DISCOUNT = 204;

  // Режим работы КРС: BT - Bluetooth, WF - WiFi, CD - Card
  P_KRS_WORK_TYPE = 300;
  // Имя Bluetooth устройства
  P_KRS_BT_DEVICE_NAME = 301;
  // Список разрешенных имён (масок) устройств
  P_KRS_BT_TRUSTED_NAMES = 302;
  // Время доступности устройства для сопряжения
  P_KRS_BT_SERVER_DISCOVERABLE_TIME = 303;
  // Очистка разрешенных имён (масок) устройств
  P_KRS_BT_CLEAR_TRUSTED_NAMES = 304;

  // Адрес хоста FTP
  P_FTP_HOST_ADDRESS = 400;
  // Порт хоста FTP
  P_FTP_HOST_PORT = 401;
  // Имя пользователя FTP
  P_FTP_USERNAME = 402;
  // Пароль пользователя FTP
  P_FTP_PASSWORD = 403;

  // Управление загрузкой фотографий льготников: 0 - не загружать, != 0 загрузить
  P_LOAD_PHOTOS = 500;

  // Сколько может быть открыта смена времени (в ms)
  P_MAX_OPEN_SHIFT_TIME_MS = 600;
  // Максимальное отклонение времени терминала от времени сервера в меньшую сторону
  P_MAX_FROM_INACCURACY_SERVER_TIME_MS = 601;
  // Максимальное отклонение времени терминала от времени сервера в большую сторону
  P_MAX_BEFORE_INACCURACY_SERVER_TIME_MS = 602;

  // Включить чек-ин / чек-аут
  P_IS_CHECK_IN_CHECK_OUT_ENABLE = 700;
  // Списывать максимум при чек-ин / чек-ауте
  P_IS_CHECK_IN_CHECK_OUT_PAYMENT_MAX = 701;
}

/* Тип данных операции */
enum OperationRawFormat {
  // Сырые данные упакованные в Base64
  ORF_CB64 = 0;
}
