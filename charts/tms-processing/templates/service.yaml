apiVersion: v1
kind: Service
metadata:
  name: {{ include "tms-processing.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "tms-processing.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.grpc.type }}
  ports:
    - name: http
      port: {{ .Values.service.http.port }}
      targetPort: {{ .Values.service.http.targetPort }}
      protocol: TCP
    - name: grpc
      port: {{ .Values.service.grpc.port }}
      targetPort: {{ .Values.service.grpc.targetPort }}
      protocol: TCP
  selector:
    {{- include "tms-processing.selectorLabels" . | nindent 4 }}
