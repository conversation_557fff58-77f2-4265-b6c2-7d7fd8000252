# Stage-specific values for tms-gate (prod environment)

replicaCount: 1

image:
  pullPolicy: IfNotPresent
  tag: "prod"

resources:
  limits:
    cpu: 1000m
    memory: 512Mi
  requests:
    cpu: 500m
    memory: 128Mi


env:
  profile: "all,log"
  client:
    logging:
      enable: true
  db:
    migration:
      enable: true
  keycloak:
    client_id: ""
    realm:
      url: ""
  kafka:
    servers: ""
  zookeeper:
    nodes: ""
  stop_list_service: ""

