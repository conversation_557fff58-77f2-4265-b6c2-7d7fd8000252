# Stage-specific values for tms-gate (dev environment)

replicaCount: 1

image:
  pullPolicy: Always
  tag: "dev"

resources:
  limits:
    cpu: 1000m
    memory: 512Mi
  requests:
    cpu: 500m
    memory: 128Mi

env:
  keycloak:
    client_id: "test-auth"
    realm:
      url: "https://dev-auth.sbertroika.tech/realms/test-asop"
  kafka:
    servers: "10.4.32.25:9092;10.4.32.140:9092;10.4.32.88:9092"
  zookeeper:
    nodes: "10.4.45.220:2181,10.4.45.19:2181,10.4.45.163:2181"
  stop_list_service: "stop-list-gate.sl.svc.cluster.local:5000"

