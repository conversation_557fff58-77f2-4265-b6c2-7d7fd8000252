{{- if .Values.virtualService.grpc.enabled }}
---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: tms-gate
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "tms-gate.labels" . | nindent 4 }}
spec:
  hosts:
    {{- range .Values.virtualService.grpc.hosts }}
    - {{ . | quote }}
    {{- end }}
  gateways:
    - "istio-ingressgateway/tms-grpc-gateway"
  http:
    - match:
        - uri:
            prefix: "/"
      route:
        - destination:
            host: "tms-gate.tms.svc.cluster.local"
            port:
              number: 5000
status: {}
{{- end }}

{{- if .Values.virtualService.https.enabled }}
---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: ext-tms-gate
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "tms-gate.labels" . | nindent 4 }}
spec:
  hosts:
    {{- range .Values.virtualService.https.hosts }}
    - {{ . | quote }}
    {{- end }}
  gateways:
    - "istio-ingressgateway/tms-https-gateway"
  http:
    - match:
        - uri:
            prefix: "/"
      route:
        - destination:
            host: "tms-gate.tms.svc.cluster.local"
            port:
              number: 8080
status: {}
{{- end }}

{{- if .Values.virtualService.https_sc.enabled }}
---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: tms-gate-https-sc
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "tms-gate.labels" . | nindent 4 }}
spec:
  hosts:
    {{- range .Values.virtualService.https_sc.hosts }}
    - {{ . | quote }}
    {{- end }}
  gateways:
    - "istio-ingressgateway/tms-https-sc-gateway"
  http:
    - match:
        - uri:
            prefix: /v1/tkp2/sl/
      route:
        - destination:
            host: stop-list-gate.sl.svc.cluster.local
            port:
              number: 8080
      rewrite:
        uri: /v1/tkp2/sl/
    - match:
        - uri:
            prefix: "/sl/"
      route:
        - destination:
            host: "stop-list-gate.sl.svc.cluster.local"
            port:
              number: 8080
      rewrite:
        uri: /sl/
    - match:
        - uri:
            prefix: "/"
      route:
        - destination:
            host: "tms-gate.tms.svc.cluster.local"
            port:
              number: 8080
status: {}
{{- end }}

{{- if .Values.virtualService.pki.enabled }}
---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: pki-tms-gate
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "tms-gate.labels" . | nindent 4 }}
spec:
  hosts:
    {{- range .Values.virtualService.pki.hosts }}
    - {{ . | quote }}
    {{- end }}
  gateways:
    - "istio-ingressgateway/pki-tms-grpc-gateway"
  http:
    - match:
        - uri:
            prefix: "/ru.sbertroika.tms.gate.v1.TMSGateService/registration"
      route:
        - destination:
            host: "tms-gate.tms.svc.cluster.local"
            port:
              number: 5000
status: {}
{{- end }} 