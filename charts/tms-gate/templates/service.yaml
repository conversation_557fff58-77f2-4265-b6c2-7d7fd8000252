apiVersion: v1
kind: Service
metadata:
  name: {{ include "tms-gate.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "tms-gate.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.grpc.type }}
  ports:
    - name: grpc
      port: {{ .Values.service.grpc.port }}
      targetPort: {{ .Values.service.grpc.targetPort }}
      protocol: TCP
    - name: http
      port: {{ .Values.service.http.port }}
      targetPort: {{ .Values.service.http.targetPort }}
      protocol: TCP
  selector:
    {{- include "tms-gate.selectorLabels" . | nindent 4 }}