{{- if .Values.gateway.grpc.enabled }}
---
apiVersion: networking.istio.io/v1beta1
kind: Gateway
metadata:
  name: tms-grpc-gateway
  namespace: istio-ingressgateway
  labels:
    {{- include "tms-gate.labels" . | nindent 4 }}
spec:
  servers:
    - port:
        number: 5000
        protocol: HTTPS
        name: grpc
      hosts:
        {{- range .Values.gateway.grpc.hosts }}
        - {{ . | quote }}
        {{- end }}
      tls:
        mode: MUTUAL
        caCertificates: {{ .Values.gateway.grpc.tls.caCertificates | quote }}
        credentialName: {{ .Values.gateway.grpc.tls.credentialName | quote }}
  selector:
    istio: ingressgateway
status: {}
{{- end }}

{{- if .Values.gateway.https.enabled }}
---
apiVersion: networking.istio.io/v1beta1
kind: Gateway
metadata:
  name: tms-https-gateway
  namespace: istio-ingressgateway
  labels:
    {{- include "tms-gate.labels" . | nindent 4 }}
spec:
  servers:
    - port:
        number: 443
        protocol: HTTPS
        name: https
      hosts:
        {{- range .Values.gateway.https.hosts }}
        - {{ . | quote }}
        {{- end }}
      tls:
        mode: SIMPLE
        credentialName: {{ .Values.gateway.https.tls.credentialName | quote }}
  selector:
    istio: ingressgateway
status: {}
{{- end }}

{{- if .Values.gateway.https_sc.enabled }}
---
apiVersion: networking.istio.io/v1beta1
kind: Gateway
metadata:
  name: tms-https-sc-gateway
  namespace: istio-ingressgateway
  labels:
    {{- include "tms-gate.labels" . | nindent 4 }}
spec:
  servers:
    - port:
        number: 443
        protocol: HTTPS
        name: https
      hosts:
        {{- range .Values.gateway.https_sc.hosts }}
        - {{ . | quote }}
        {{- end }}
      tls:
        caCertificates: {{ .Values.gateway.https_sc.tls.caCertificates | quote }}
        credentialName: {{ .Values.gateway.https_sc.tls.credentialName | quote }}
  selector:
    istio: ingressgateway
status: {}
{{- end }}

{{- if .Values.gateway.pki.enabled }}
---
apiVersion: networking.istio.io/v1beta1
kind: Gateway
metadata:
  name: pki-tms-grpc-gateway
  namespace: istio-ingressgateway
  labels:
    {{- include "tms-gate.labels" . | nindent 4 }}
spec:
  servers:
    - port:
        number: 5000
        protocol: HTTPS
        name: grpc
      hosts:
        {{- range .Values.gateway.pki.hosts }}
        - {{ . | quote }}
        {{- end }}
      tls:
        mode: SIMPLE
        credentialName: {{ .Values.gateway.pki.tls.credentialName | quote }}
  selector:
    istio: ingressgateway
status: {}
{{- end }} 